#!/usr/bin/env python3
"""
Initialize the first admin user for the RAID application.
"""

import sys
from sqlalchemy.orm import Session
from app.db import SessionLocal, create_tables
from app.auth import create_user
from app.models.user import User


def create_admin_user():
    """Create the first admin user."""
    # Create tables first
    create_tables()
    
    db: Session = SessionLocal()
    try:
        # Check if any admin users exist
        existing_admin = db.query(User).filter(User.role == "admin").first()
        if existing_admin:
            print(f"Admin user already exists: {existing_admin.email}")
            return
        
        # Get admin details
        print("Creating first admin user...")
        email = input("Admin email: ").strip()
        if not email:
            print("Email is required")
            sys.exit(1)
        
        password = input("Admin password (min 6 chars): ").strip()
        if len(password) < 6:
            print("Password must be at least 6 characters")
            sys.exit(1)
        
        full_name = input("Admin full name: ").strip()
        if not full_name:
            print("Full name is required")
            sys.exit(1)
        
        # Create admin user
        try:
            admin_user = create_user(
                db=db,
                email=email,
                password=password,
                full_name=full_name,
                role="admin"
            )
            print(f"✅ Admin user created successfully!")
            print(f"   Email: {admin_user.email}")
            print(f"   Name: {admin_user.full_name}")
            print(f"   Role: {admin_user.role}")
            print(f"   ID: {admin_user.id}")
            
        except ValueError as e:
            print(f"❌ Error creating admin user: {e}")
            sys.exit(1)
            
    finally:
        db.close()


if __name__ == "__main__":
    create_admin_user()
