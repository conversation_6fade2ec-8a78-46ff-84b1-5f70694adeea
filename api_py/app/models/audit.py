"""Audit trail model for tracking changes."""

from uuid import uuid4
from sqlalchemy import Column, String, DateTime, func, Text, Index
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.types import JSON
from ..db import Base


class AuditEntry(Base):
    """Audit trail for tracking all changes to RAID items."""
    __tablename__ = "audit_entries"

    id = Column(String, primary_key=True, default=lambda: str(uuid4()))
    
    # What was changed
    itemType = Column(String, nullable=False, index=True)  # project, risk, action, issue, decision
    itemId = Column(String, nullable=False, index=True)
    
    # Change details
    action = Column(String, nullable=False, index=True)  # create, update, delete
    changeSet = Column(JSON().with_variant(JSONB, "postgresql"), nullable=False)  # What changed
    oldValues = Column(JSON().with_variant(JSONB, "postgresql"), nullable=True)  # Previous values
    newValues = Column(JSON().with_variant(JSONB, "postgresql"), nullable=True)  # New values
    
    # Who and when
    changedBy = Column(String, nullable=False, index=True)  # User ID
    changedAt = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    
    # Additional context
    reason = Column(Text, nullable=True)  # Optional reason for change
    ip_address = Column(String, nullable=True)
    user_agent = Column(String, nullable=True)
    
    def __repr__(self):
        return f"<AuditEntry(id='{self.id}', itemType='{self.itemType}', action='{self.action}')>"

    # Composite indexes for better query performance
    __table_args__ = (
        Index('ix_audit_item_type_id', 'itemType', 'itemId'),
        Index('ix_audit_item_changed_at', 'itemType', 'itemId', 'changedAt'),
        Index('ix_audit_user_action', 'changedBy', 'action'),
        Index('ix_audit_changed_at_desc', 'changedAt'),
    )
