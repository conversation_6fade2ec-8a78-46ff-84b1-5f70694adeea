"""SavedFilter model for storing user-defined filters."""

from sqlalchemy import Column, String, Boolean, Text, DateTime, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from uuid import uuid4

from ..db import Base


class SavedFilter(Base):
    """Model for saved filters."""
    
    __tablename__ = "saved_filters"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid4()))
    name = Column(String(100), nullable=False, index=True)
    description = Column(Text, nullable=True)
    filter_data = Column(JSON, nullable=False)
    is_public = Column(Boolean, default=False, nullable=False, index=True)
    
    # Relationships
    project_id = Column(String, ForeignKey("projects.id", ondelete="CASCADE"), nullable=True, index=True)
    created_by = Column(String, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)
    
    # Timestamps
    createdAt = Column(DateTime(timezone=True), server_default=func.now(), nullable=False, index=True)
    updatedAt = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    project = relationship("Project", back_populates="saved_filters")
    creator = relationship("User", back_populates="saved_filters")
    
    def __repr__(self):
        return f"<SavedFilter(id='{self.id}', name='{self.name}', created_by='{self.created_by}')>"
    
    def to_dict(self):
        """Convert model to dictionary."""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "filter_data": self.filter_data,
            "is_public": self.is_public,
            "project_id": self.project_id,
            "created_by": self.created_by,
            "created_at": self.createdAt.isoformat() if self.createdAt else None,
            "updated_at": self.updatedAt.isoformat() if self.updatedAt else None,
        }
