"""Decision model with comprehensive fields and relationships."""

from sqlalchemy import Column, String, Text, DateTime, ForeignKey, func, Index
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.types import JSON
from sqlalchemy.orm import relationship
from ..db import Base


class Decision(Base):
    """Decision model representing project decisions."""
    __tablename__ = "decisions"

    id = Column(String, primary_key=True)
    projectId = Column(String, ForeignKey("projects.id", ondelete="CASCADE"), nullable=False, index=True)

    title = Column(String, nullable=False, index=True)
    description = Column(Text, nullable=True)

    # Decision details
    rationale = Column(Text, nullable=True)  # Why this decision was made
    alternatives = Column(Text, nullable=True)  # Other options considered
    implications = Column(Text, nullable=True)  # Expected consequences

    # Decision makers and stakeholders
    decidedBy = Column(String, nullable=True, index=True)  # Primary decision maker
    stakeholders = Column(JSON().with_variant(JSONB, "postgresql"), nullable=False, default=list)
    approvers = Column(JSON().with_variant(JSONB, "postgresql"), nullable=False, default=list)

    # Decision status and tracking
    status = Column(String, nullable=False, default="Proposed", index=True)  # Proposed, Approved, Rejected, Implemented
    priority = Column(String, nullable=False, default="Medium")  # Low, Medium, High, Critical
    category = Column(String, nullable=True)  # Strategic, Tactical, Operational, etc.

    # Dates
    proposedAt = Column(DateTime(timezone=True), server_default=func.now())
    decidedOn = Column(DateTime(timezone=True), nullable=True, index=True)
    implementedAt = Column(DateTime(timezone=True), nullable=True)
    reviewDate = Column(DateTime(timezone=True), nullable=True)  # When to review this decision

    # Additional data
    links = Column(JSON().with_variant(JSONB, "postgresql"), nullable=False, default=list)
    tags = Column(JSON().with_variant(JSONB, "postgresql"), nullable=False, default=list)
    attachments = Column(JSON().with_variant(JSONB, "postgresql"), nullable=False, default=list)

    # Timestamps
    createdAt = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    updatedAt = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    project = relationship("Project", back_populates="decisions")

    def __repr__(self):
        return f"<Decision(id='{self.id}', title='{self.title}', status='{self.status}')>"

    # Composite indexes for better query performance
    __table_args__ = (
        Index('ix_decision_project_status', 'projectId', 'status'),
        Index('ix_decision_project_priority', 'projectId', 'priority'),
        Index('ix_decision_status_decided', 'status', 'decidedOn'),
        Index('ix_decision_decided_by_status', 'decidedBy', 'status'),
        Index('ix_decision_decided_on', 'decidedOn'),
        Index('ix_decision_category_status', 'category', 'status'),
    )


