"""Project model with comprehensive fields and relationships."""

from sqlalchemy import Column, String, DateTime, func, Text
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.types import JSON
from sqlalchemy.orm import relationship
from ..db import Base


class Project(Base):
    """Project model representing a RAID project."""
    __tablename__ = "projects"

    id = Column(String, primary_key=True)
    name = Column(String, nullable=False, index=True)
    description = Column(Text, nullable=True)
    cadence = Column(String, nullable=False, default="weekly")
    categories = Column(
        JSON().with_variant(JSONB, "postgresql"),
        nullable=False,
        default=lambda: ["assumptions", "dependencies"]
    )
    status = Column(String, nullable=False, default="active")  # active, archived, completed
    owner = Column(String, nullable=True)

    # Timestamps
    createdAt = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    updatedAt = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    risks = relationship("Risk", back_populates="project", cascade="all, delete-orphan")
    actions = relationship("Action", back_populates="project", cascade="all, delete-orphan")
    issues = relationship("Issue", back_populates="project", cascade="all, delete-orphan")
    decisions = relationship("Decision", back_populates="project", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Project(id='{self.id}', name='{self.name}')>"


