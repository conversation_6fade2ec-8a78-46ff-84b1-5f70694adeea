"""User model for authentication and authorization."""

from uuid import uuid4
from sqlalchemy import Column, String, DateTime, func, Boolean, Text, Index
from sqlalchemy.orm import relationship
from ..db import Base


class User(Base):
    """User model for authentication and role management."""
    __tablename__ = "users"

    id = Column(String, primary_key=True, default=lambda: str(uuid4()))
    email = Column(String, nullable=False, unique=True, index=True)
    password_hash = Column(String, nullable=False)  # Hashed password
    full_name = Column(String, nullable=False)
    username = Column(String, nullable=True, unique=True, index=True)
    
    # Role-based access control
    role = Column(String, nullable=False, default="viewer", index=True)  # viewer, contributor, pm, admin
    is_active = Column(Boolean, nullable=False, default=True)
    is_verified = Column(Boolean, nullable=False, default=False)
    
    # Profile information
    bio = Column(Text, nullable=True)
    avatar_url = Column(String, nullable=True)
    timezone = Column(String, nullable=True, default="UTC")
    preferences = Column(String, nullable=True)  # JSON string for user preferences
    
    # Timestamps
    createdAt = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    updatedAt = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    lastLoginAt = Column(DateTime(timezone=True), nullable=True)
    
    def __repr__(self):
        return f"<User(id='{self.id}', email='{self.email}', role='{self.role}')>"
    
    def has_permission(self, required_role: str) -> bool:
        """Check if user has required permission level."""
        role_hierarchy = {
            "viewer": 1,
            "contributor": 2,
            "pm": 3,
            "admin": 4
        }
        user_level = role_hierarchy.get(self.role, 0)
        required_level = role_hierarchy.get(required_role, 0)
        return user_level >= required_level

    # Composite indexes for better query performance
    __table_args__ = (
        Index('ix_user_role_active', 'role', 'is_active'),
        Index('ix_user_active_verified', 'is_active', 'is_verified'),
        Index('ix_user_created_role', 'createdAt', 'role'),
    )
