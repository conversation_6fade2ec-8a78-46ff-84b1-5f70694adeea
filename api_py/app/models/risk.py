"""Risk model with comprehensive fields and relationships."""

from sqlalchemy import Column, String, Text, Integer, DateTime, ForeignKey, func, Float, Index
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.types import JSON
from sqlalchemy.orm import relationship
from ..db import Base


class Risk(Base):
    """Risk model representing project risks."""
    __tablename__ = "risks"

    id = Column(String, primary_key=True)
    projectId = Column(String, ForeignKey("projects.id", ondelete="CASCADE"), nullable=False, index=True)

    title = Column(String, nullable=False, index=True)
    description = Column(Text, nullable=True)
    trigger = Column(Text, nullable=True)  # What could trigger this risk

    # Risk assessment (standardized naming)
    probability = Column(Float, nullable=False, default=0.0)  # 0.0 to 1.0
    likelihood = Column(Integer, nullable=True)  # Legacy field, keep for compatibility
    impact = Column(Integer, nullable=False, default=1)  # 1-5 scale
    score = Column(Float, nullable=False, default=0.0)  # probability * impact
    severity = Column(String, nullable=True)  # Low, Medium, High, Critical

    # Risk management
    response = Column(Text, nullable=True)  # Response strategy
    mitigation = Column(Text, nullable=True)  # Mitigation plan
    contingency = Column(Text, nullable=True)  # Contingency plan
    rationale = Column(Text, nullable=True)

    # Assignment and tracking
    owner = Column(String, nullable=True, index=True)
    status = Column(String, nullable=False, default="Open", index=True)  # Open, In progress, Resolved, Closed
    priority = Column(String, nullable=False, default="Medium")  # Low, Medium, High, Critical

    # Dates
    identifiedAt = Column(DateTime(timezone=True), server_default=func.now())
    dueAt = Column(DateTime(timezone=True), nullable=True, index=True)
    resolvedAt = Column(DateTime(timezone=True), nullable=True)

    # Additional data
    links = Column(JSON().with_variant(JSONB, "postgresql"), nullable=False, default=list)
    tags = Column(JSON().with_variant(JSONB, "postgresql"), nullable=False, default=list)

    # Timestamps
    createdAt = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    updatedAt = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    project = relationship("Project", back_populates="risks")
    actions = relationship("Action", back_populates="risk")
    issues = relationship("Issue", back_populates="risk")

    def __repr__(self):
        return f"<Risk(id='{self.id}', title='{self.title}', score={self.score})>"

    def calculate_score(self):
        """Calculate and update the risk score."""
        self.score = self.probability * self.impact
        return self.score

    # Composite indexes for better query performance
    __table_args__ = (
        Index('ix_risk_project_status', 'projectId', 'status'),
        Index('ix_risk_project_priority', 'projectId', 'priority'),
        Index('ix_risk_score_status', 'score', 'status'),
        Index('ix_risk_owner_status', 'owner', 'status'),
        Index('ix_risk_due_status', 'dueAt', 'status'),
    )


