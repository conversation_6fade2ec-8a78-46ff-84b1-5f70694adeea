from sqlalchemy import Column, String, Text, Integer, DateTime, ForeignKey, func
from ..db import Base


class Risk(Base):
    __tablename__ = "risks"

    id = Column(String, primary_key=True)
    projectId = Column(String, ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)

    title = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    owner = Column(String, nullable=True)
    status = Column(String, nullable=False, default="Open")
    priority = Column(String, nullable=True)
    dueAt = Column(DateTime(timezone=True), nullable=True)

    # Risk-specific scoring fields
    likelihood = Column(Integer, nullable=True)
    impact = Column(Integer, nullable=True)
    score = Column(Integer, nullable=True)
    severity = Column(String, nullable=True)
    rationale = Column(Text, nullable=True)

    createdAt = Column(DateTime(timezone=True), server_default=func.now())
    updatedAt = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())


