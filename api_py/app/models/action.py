"""Action model with comprehensive fields and relationships."""

from sqlalchemy import Column, String, Text, DateTime, ForeignKey, func, Integer, Index
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.types import JSON
from sqlalchemy.orm import relationship
from ..db import Base


class Action(Base):
    """Action model representing project actions/tasks."""
    __tablename__ = "actions"

    id = Column(String, primary_key=True)
    projectId = Column(String, ForeignKey("projects.id", ondelete="CASCADE"), nullable=False, index=True)

    title = Column(String, nullable=False, index=True)
    description = Column(Text, nullable=True)

    # Assignment and tracking
    owner = Column(String, nullable=True, index=True)
    assignee = Column(String, nullable=True)  # Can be different from owner
    status = Column(String, nullable=False, default="Open", index=True)  # Open, In progress, Done, Cancelled
    priority = Column(String, nullable=False, default="Medium")  # Low, Medium, High, Critical

    # Progress tracking
    progress = Column(Integer, nullable=False, default=0)  # 0-100 percentage
    effort_estimate = Column(Integer, nullable=True)  # Estimated hours
    effort_actual = Column(Integer, nullable=True)  # Actual hours spent

    # Relationships to other RAID items
    relatedId = Column(String, nullable=True)  # ID of related item
    relatedType = Column(String, nullable=True)  # risk, issue, decision
    riskId = Column(String, ForeignKey("risks.id", ondelete="SET NULL"), nullable=True)

    # Dates
    dueAt = Column(DateTime(timezone=True), nullable=True, index=True)
    startedAt = Column(DateTime(timezone=True), nullable=True)
    completedAt = Column(DateTime(timezone=True), nullable=True)

    # Additional data
    links = Column(JSON().with_variant(JSONB, "postgresql"), nullable=False, default=list)
    tags = Column(JSON().with_variant(JSONB, "postgresql"), nullable=False, default=list)
    notes = Column(Text, nullable=True)

    # Timestamps
    createdAt = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    updatedAt = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    project = relationship("Project", back_populates="actions")
    risk = relationship("Risk", back_populates="actions")

    def __repr__(self):
        return f"<Action(id='{self.id}', title='{self.title}', status='{self.status}')>"

    # Composite indexes for better query performance
    __table_args__ = (
        Index('ix_action_project_status', 'projectId', 'status'),
        Index('ix_action_project_priority', 'projectId', 'priority'),
        Index('ix_action_assignee_status', 'assignee', 'status'),
        Index('ix_action_owner_status', 'owner', 'status'),
        Index('ix_action_due_status', 'dueAt', 'status'),
        Index('ix_action_progress_status', 'progress', 'status'),
    )


