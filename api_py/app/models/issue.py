"""Issue model with comprehensive fields and relationships."""

from sqlalchemy import Column, String, Text, DateTime, ForeignKey, func, Index
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.types import JSON
from sqlalchemy.orm import relationship
from ..db import Base


class Issue(Base):
    """Issue model representing project issues/problems."""
    __tablename__ = "issues"

    id = Column(String, primary_key=True)
    projectId = Column(String, ForeignKey("projects.id", ondelete="CASCADE"), nullable=False, index=True)

    title = Column(String, nullable=False, index=True)
    description = Column(Text, nullable=True)

    # Issue classification
    severity = Column(String, nullable=False, default="Low", index=True)  # Low, Medium, High, Critical
    priority = Column(String, nullable=False, default="Medium")  # Low, Medium, High, Critical
    category = Column(String, nullable=True)  # Bug, Enhancement, Task, etc.

    # Assignment and tracking
    owner = Column(String, nullable=True, index=True)
    assignee = Column(String, nullable=True)
    reporter = Column(String, nullable=True)
    status = Column(String, nullable=False, default="Open", index=True)  # Open, In progress, Resolved, Closed

    # Resolution
    resolution = Column(Text, nullable=True)
    resolution_type = Column(String, nullable=True)  # Fixed, Won't Fix, Duplicate, etc.

    # Relationships to other RAID items
    relatedRiskId = Column(String, ForeignKey("risks.id", ondelete="SET NULL"), nullable=True)

    # Dates
    reportedAt = Column(DateTime(timezone=True), server_default=func.now())
    dueAt = Column(DateTime(timezone=True), nullable=True, index=True)
    resolvedAt = Column(DateTime(timezone=True), nullable=True)
    closedAt = Column(DateTime(timezone=True), nullable=True)

    # Additional data
    links = Column(JSON().with_variant(JSONB, "postgresql"), nullable=False, default=list)
    tags = Column(JSON().with_variant(JSONB, "postgresql"), nullable=False, default=list)
    attachments = Column(JSON().with_variant(JSONB, "postgresql"), nullable=False, default=list)

    # Timestamps
    createdAt = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    updatedAt = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    project = relationship("Project", back_populates="issues")
    risk = relationship("Risk", back_populates="issues")

    def __repr__(self):
        return f"<Issue(id='{self.id}', title='{self.title}', severity='{self.severity}')>"

    # Composite indexes for better query performance
    __table_args__ = (
        Index('ix_issue_project_status', 'projectId', 'status'),
        Index('ix_issue_project_severity', 'projectId', 'severity'),
        Index('ix_issue_severity_status', 'severity', 'status'),
        Index('ix_issue_owner_status', 'owner', 'status'),
        Index('ix_issue_due_status', 'dueAt', 'status'),
    )


