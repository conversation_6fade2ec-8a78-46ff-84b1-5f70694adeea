"""Notification models for RAID application."""

from sqlalchemy import Column, String, Text, Boolean, DateTime, ForeignKey, func, JSON
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from uuid import uuid4
from ..db import Base


class NotificationTemplate(Base):
    """Template for different types of notifications."""
    __tablename__ = "notification_templates"

    id = Column(String, primary_key=True, default=lambda: str(uuid4()))
    name = Column(String(100), nullable=False, unique=True, index=True)
    type = Column(String(50), nullable=False, index=True)  # email, in_app, both
    event_type = Column(String(50), nullable=False, index=True)  # created, updated, due_soon, overdue, assigned, etc.
    
    # Template content
    subject_template = Column(String(200), nullable=False)
    body_template = Column(Text, nullable=False)
    html_template = Column(Text, nullable=True)
    
    # Configuration
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    priority = Column(String(20), default="normal", nullable=False)  # low, normal, high, urgent
    
    # Metadata
    variables = Column(JSON().with_variant(JSONB, "postgresql"), nullable=False, default=list)  # Available template variables
    description = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"<NotificationTemplate(id='{self.id}', name='{self.name}', event_type='{self.event_type}')>"


class Notification(Base):
    """Individual notification instance."""
    __tablename__ = "notifications"

    id = Column(String, primary_key=True, default=lambda: str(uuid4()))
    
    # Recipient and sender
    recipient_id = Column(String, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)
    sender_id = Column(String, ForeignKey("users.id", ondelete="SET NULL"), nullable=True, index=True)
    
    # Notification content
    type = Column(String(50), nullable=False, index=True)  # email, in_app, both
    event_type = Column(String(50), nullable=False, index=True)
    subject = Column(String(200), nullable=False)
    message = Column(Text, nullable=False)
    html_content = Column(Text, nullable=True)
    
    # Related entity
    entity_type = Column(String(50), nullable=True, index=True)  # risk, action, issue, decision, project
    entity_id = Column(String, nullable=True, index=True)
    project_id = Column(String, ForeignKey("projects.id", ondelete="CASCADE"), nullable=True, index=True)
    
    # Status and metadata
    status = Column(String(20), default="pending", nullable=False, index=True)  # pending, sent, failed, read
    priority = Column(String(20), default="normal", nullable=False, index=True)
    
    # Delivery tracking
    sent_at = Column(DateTime(timezone=True), nullable=True, index=True)
    read_at = Column(DateTime(timezone=True), nullable=True, index=True)
    failed_at = Column(DateTime(timezone=True), nullable=True)
    failure_reason = Column(Text, nullable=True)
    
    # Additional data
    notification_metadata = Column(JSON().with_variant(JSONB, "postgresql"), nullable=False, default=dict)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    recipient = relationship("User", foreign_keys=[recipient_id], back_populates="received_notifications")
    sender = relationship("User", foreign_keys=[sender_id], back_populates="sent_notifications")
    project = relationship("Project", back_populates="notifications")

    def __repr__(self):
        return f"<Notification(id='{self.id}', type='{self.type}', status='{self.status}')>"


class NotificationPreference(Base):
    """User notification preferences."""
    __tablename__ = "notification_preferences"

    id = Column(String, primary_key=True, default=lambda: str(uuid4()))
    user_id = Column(String, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)
    
    # Event type preferences
    event_type = Column(String(50), nullable=False, index=True)
    
    # Delivery preferences
    email_enabled = Column(Boolean, default=True, nullable=False)
    in_app_enabled = Column(Boolean, default=True, nullable=False)
    
    # Timing preferences
    immediate = Column(Boolean, default=True, nullable=False)
    daily_digest = Column(Boolean, default=False, nullable=False)
    weekly_digest = Column(Boolean, default=False, nullable=False)
    
    # Filtering
    project_id = Column(String, ForeignKey("projects.id", ondelete="CASCADE"), nullable=True, index=True)  # Project-specific preferences
    priority_threshold = Column(String(20), default="low", nullable=False)  # Minimum priority to notify
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="notification_preferences")
    project = relationship("Project", back_populates="notification_preferences")

    def __repr__(self):
        return f"<NotificationPreference(id='{self.id}', user_id='{self.user_id}', event_type='{self.event_type}')>"


class NotificationQueue(Base):
    """Queue for batch notification processing."""
    __tablename__ = "notification_queue"

    id = Column(String, primary_key=True, default=lambda: str(uuid4()))
    
    # Queue metadata
    batch_id = Column(String, nullable=True, index=True)  # For grouping related notifications
    priority = Column(String(20), default="normal", nullable=False, index=True)
    
    # Notification data
    notification_data = Column(JSON().with_variant(JSONB, "postgresql"), nullable=False)
    
    # Processing status
    status = Column(String(20), default="queued", nullable=False, index=True)  # queued, processing, completed, failed
    attempts = Column(String, default="0", nullable=False)
    max_attempts = Column(String, default="3", nullable=False)
    
    # Scheduling
    scheduled_for = Column(DateTime(timezone=True), nullable=True, index=True)
    processed_at = Column(DateTime(timezone=True), nullable=True, index=True)
    
    # Error handling
    last_error = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"<NotificationQueue(id='{self.id}', status='{self.status}', priority='{self.priority}')>"
