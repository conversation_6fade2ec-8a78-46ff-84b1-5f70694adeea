"""Notification API endpoints."""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional

from ..db import get_db
from ..auth import get_current_active_user
from ..models.user import User
from ..models.notification import Notification, NotificationTemplate, NotificationPreference
from ..schemas.notification import (
    NotificationOut, NotificationCreate, NotificationUpdate,
    NotificationTemplateOut, NotificationTemplateCreate, NotificationTemplateUpdate,
    NotificationPreferenceOut, NotificationPreferenceCreate, NotificationPreferenceUpdate,
    BulkNotificationCreate, NotificationStats, NotificationEventType
)
from ..services.notification_service import NotificationService

router = APIRouter(prefix="/notifications", tags=["notifications"])
templates_router = APIRouter(prefix="/notification-templates", tags=["notification-templates"])


@router.get("/", response_model=List[NotificationOut])
async def get_notifications(
    unread_only: bool = Query(False, description="Get only unread notifications"),
    project_id: Optional[str] = Query(None, description="Filter by project"),
    limit: int = Query(50, ge=1, le=100, description="Number of notifications to return"),
    offset: int = Query(0, ge=0, description="Number of notifications to skip"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get notifications for the current user."""
    service = NotificationService(db)
    notifications = service.get_user_notifications(
        user_id=current_user.id,
        unread_only=unread_only,
        project_id=project_id,
        limit=limit,
        offset=offset
    )
    return notifications


@router.get("/stats", response_model=NotificationStats)
async def get_notification_stats(
    project_id: Optional[str] = Query(None, description="Filter by project"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get notification statistics for the current user."""
    query = db.query(Notification).filter(Notification.recipient_id == current_user.id)
    
    if project_id:
        query = query.filter(Notification.project_id == project_id)
    
    all_notifications = query.all()
    
    # Calculate statistics
    total_notifications = len(all_notifications)
    unread_count = len([n for n in all_notifications if n.status != "read"])
    pending_count = len([n for n in all_notifications if n.status == "pending"])
    sent_count = len([n for n in all_notifications if n.status == "sent"])
    failed_count = len([n for n in all_notifications if n.status == "failed"])
    
    # Group by type and priority
    by_type = {}
    by_priority = {}
    for notification in all_notifications:
        by_type[notification.type] = by_type.get(notification.type, 0) + 1
        by_priority[notification.priority] = by_priority.get(notification.priority, 0) + 1
    
    # Get recent notifications
    recent_notifications = query.order_by(Notification.created_at.desc()).limit(5).all()
    
    return NotificationStats(
        total_notifications=total_notifications,
        unread_count=unread_count,
        pending_count=pending_count,
        sent_count=sent_count,
        failed_count=failed_count,
        by_type=by_type,
        by_priority=by_priority,
        recent_notifications=recent_notifications
    )


@router.post("/", response_model=NotificationOut)
async def create_notification(
    notification_data: NotificationCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create a new notification."""
    service = NotificationService(db)
    
    notification = service.create_notification(
        recipient_id=notification_data.recipient_id,
        event_type=notification_data.event_type,
        subject=notification_data.subject,
        message=notification_data.message,
        sender_id=current_user.id,
        entity_type=notification_data.entity_type,
        entity_id=notification_data.entity_id,
        project_id=notification_data.project_id,
        priority=notification_data.priority,
        notification_type=notification_data.type,
        metadata=notification_data.notification_metadata
    )
    
    if not notification:
        raise HTTPException(status_code=400, detail="Notification not created due to user preferences")
    
    return notification


@router.post("/bulk", response_model=List[NotificationOut])
async def create_bulk_notifications(
    bulk_data: BulkNotificationCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create multiple notifications."""
    service = NotificationService(db)
    
    if bulk_data.template_id:
        # Create from template
        notifications = []
        template = db.query(NotificationTemplate).filter(
            NotificationTemplate.id == bulk_data.template_id
        ).first()
        
        if not template:
            raise HTTPException(status_code=404, detail="Template not found")
        
        for recipient_id in bulk_data.recipient_ids:
            notification = service.create_from_template(
                template_id=bulk_data.template_id,
                recipient_id=recipient_id,
                variables=bulk_data.variables,
                sender_id=current_user.id,
                entity_type=bulk_data.entity_type,
                entity_id=bulk_data.entity_id,
                project_id=bulk_data.project_id
            )
            if notification:
                notifications.append(notification)
        
        return notifications
    else:
        raise HTTPException(status_code=400, detail="Template ID or notification content required")


@router.patch("/{notification_id}", response_model=NotificationOut)
async def update_notification(
    notification_id: str,
    update_data: NotificationUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update a notification."""
    notification = db.query(Notification).filter(
        Notification.id == notification_id,
        Notification.recipient_id == current_user.id
    ).first()
    
    if not notification:
        raise HTTPException(status_code=404, detail="Notification not found")
    
    update_dict = update_data.dict(exclude_unset=True)
    for field, value in update_dict.items():
        setattr(notification, field, value)
    
    db.commit()
    db.refresh(notification)
    return notification


@router.post("/{notification_id}/read")
async def mark_notification_as_read(
    notification_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Mark a notification as read."""
    service = NotificationService(db)
    success = service.mark_as_read(notification_id, current_user.id)
    
    if not success:
        raise HTTPException(status_code=404, detail="Notification not found")
    
    return {"message": "Notification marked as read"}


@router.post("/read-all")
async def mark_all_notifications_as_read(
    project_id: Optional[str] = Query(None, description="Mark all in specific project"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Mark all notifications as read."""
    service = NotificationService(db)
    count = service.mark_all_as_read(current_user.id, project_id)
    
    return {"message": f"Marked {count} notifications as read"}


@router.delete("/{notification_id}")
async def delete_notification(
    notification_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete a notification."""
    notification = db.query(Notification).filter(
        Notification.id == notification_id,
        Notification.recipient_id == current_user.id
    ).first()
    
    if not notification:
        raise HTTPException(status_code=404, detail="Notification not found")
    
    db.delete(notification)
    db.commit()
    
    return {"message": "Notification deleted"}


# Notification Preferences Endpoints
@router.get("/preferences", response_model=List[NotificationPreferenceOut])
async def get_notification_preferences(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get user notification preferences."""
    preferences = db.query(NotificationPreference).filter(
        NotificationPreference.user_id == current_user.id
    ).all()
    return preferences


@router.post("/preferences", response_model=NotificationPreferenceOut)
async def create_notification_preference(
    preference_data: NotificationPreferenceCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create or update notification preference."""
    # Check if preference already exists
    existing = db.query(NotificationPreference).filter(
        NotificationPreference.user_id == current_user.id,
        NotificationPreference.event_type == preference_data.event_type.value,
        NotificationPreference.project_id == preference_data.project_id
    ).first()
    
    if existing:
        # Update existing preference
        update_dict = preference_data.dict(exclude={"user_id"}, exclude_unset=True)
        for field, value in update_dict.items():
            if hasattr(existing, field):
                setattr(existing, field, value)
        db.commit()
        db.refresh(existing)
        return existing
    else:
        # Create new preference
        preference = NotificationPreference(
            user_id=current_user.id,
            **preference_data.dict(exclude={"user_id"})
        )
        db.add(preference)
        db.commit()
        db.refresh(preference)
        return preference


@router.patch("/preferences/{preference_id}", response_model=NotificationPreferenceOut)
async def update_notification_preference(
    preference_id: str,
    update_data: NotificationPreferenceUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update notification preference."""
    preference = db.query(NotificationPreference).filter(
        NotificationPreference.id == preference_id,
        NotificationPreference.user_id == current_user.id
    ).first()
    
    if not preference:
        raise HTTPException(status_code=404, detail="Preference not found")
    
    update_dict = update_data.dict(exclude_unset=True)
    for field, value in update_dict.items():
        setattr(preference, field, value)
    
    db.commit()
    db.refresh(preference)
    return preference


@router.delete("/preferences/{preference_id}")
async def delete_notification_preference(
    preference_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete notification preference."""
    preference = db.query(NotificationPreference).filter(
        NotificationPreference.id == preference_id,
        NotificationPreference.user_id == current_user.id
    ).first()
    
    if not preference:
        raise HTTPException(status_code=404, detail="Preference not found")
    
    db.delete(preference)
    db.commit()
    
    return {"message": "Preference deleted"}


# Notification Templates Endpoints (Admin only)
@templates_router.get("/", response_model=List[NotificationTemplateOut])
async def get_notification_templates(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get all notification templates (admin only)."""
    if current_user.role != "admin":
        raise HTTPException(status_code=403, detail="Admin access required")

    templates = db.query(NotificationTemplate).all()
    return templates


@templates_router.post("/", response_model=NotificationTemplateOut)
async def create_notification_template(
    template_data: NotificationTemplateCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create notification template (admin only)."""
    if current_user.role != "admin":
        raise HTTPException(status_code=403, detail="Admin access required")

    template = NotificationTemplate(**template_data.dict())
    db.add(template)
    db.commit()
    db.refresh(template)
    return template


@templates_router.patch("/{template_id}", response_model=NotificationTemplateOut)
async def update_notification_template(
    template_id: str,
    update_data: NotificationTemplateUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update notification template (admin only)."""
    if current_user.role != "admin":
        raise HTTPException(status_code=403, detail="Admin access required")

    template = db.query(NotificationTemplate).filter(
        NotificationTemplate.id == template_id
    ).first()

    if not template:
        raise HTTPException(status_code=404, detail="Template not found")

    update_dict = update_data.dict(exclude_unset=True)
    for field, value in update_dict.items():
        setattr(template, field, value)

    db.commit()
    db.refresh(template)
    return template


@templates_router.delete("/{template_id}")
async def delete_notification_template(
    template_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete notification template (admin only)."""
    if current_user.role != "admin":
        raise HTTPException(status_code=403, detail="Admin access required")

    template = db.query(NotificationTemplate).filter(
        NotificationTemplate.id == template_id
    ).first()

    if not template:
        raise HTTPException(status_code=404, detail="Template not found")

    db.delete(template)
    db.commit()

    return {"message": "Template deleted"}
