"""Projects router with comprehensive CRUD operations."""

from typing import List, Optional
from uuid import uuid4
from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from sqlalchemy import func

from ..db import get_db
from ..models import Project, Risk, Action, Issue, Decision
from ..schemas.project import ProjectCreate, ProjectUpdate, ProjectOut, ProjectWithStats
from ..schemas.common import PaginationParams, PaginatedResponse, FilterParams
from ..exceptions import NotFoundError, ValidationError
from ..auth import get_current_active_user, require_contributor_or_above, require_pm_or_admin
from ..models.user import User

router = APIRouter(prefix="/projects", tags=["Projects"])


@router.get("/", response_model=List[ProjectOut])
def list_projects(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    skip: int = Query(0, ge=0, description="Number of projects to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of projects to return"),
    status: Optional[str] = Query(None, description="Filter by project status"),
    owner: Optional[str] = Query(None, description="Filter by project owner"),
    q: Optional[str] = Query(None, description="Search in project name and description")
):
    """List all projects with optional filtering."""
    query = db.query(Project)

    # Apply filters
    if status:
        query = query.filter(Project.status == status)
    if owner:
        query = query.filter(Project.owner == owner)
    if q:
        search_term = f"%{q}%"
        query = query.filter(
            (Project.name.ilike(search_term)) |
            (Project.description.ilike(search_term))
        )

    # Apply pagination and ordering
    projects = query.order_by(Project.createdAt.desc()).offset(skip).limit(limit).all()
    return projects


@router.post("/", response_model=ProjectOut)
def create_project(
    payload: ProjectCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_contributor_or_above)
):
    """Create a new project."""
    # Validate unique name
    existing = db.query(Project).filter(Project.name == payload.name).first()
    if existing:
        raise ValidationError(f"Project with name '{payload.name}' already exists", "name")

    new_project = Project(
        id=str(uuid4()),
        name=payload.name,
        description=payload.description,
        cadence=payload.cadence,
        categories=payload.categories,
        status=payload.status,
        owner=payload.owner
    )

    db.add(new_project)
    db.commit()
    db.refresh(new_project)
    return new_project


@router.get("/{project_id}", response_model=ProjectWithStats)
def get_project(
    project_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get a specific project with statistics."""
    project = db.query(Project).filter(Project.id == project_id).first()
    if not project:
        raise NotFoundError("Project", project_id)

    # Calculate aggregates
    risk_count = db.query(func.count(Risk.id)).filter(Risk.projectId == project_id).scalar()
    action_count = db.query(func.count(Action.id)).filter(Action.projectId == project_id).scalar()
    issue_count = db.query(func.count(Issue.id)).filter(Issue.projectId == project_id).scalar()
    decision_count = db.query(func.count(Decision.id)).filter(Decision.projectId == project_id).scalar()

    # Convert to dict and add aggregates
    project_dict = {
        **project.__dict__,
        "aggregates": {
            "risks": risk_count,
            "actions": action_count,
            "issues": issue_count,
            "decisions": decision_count
        }
    }

    return project_dict


@router.put("/{project_id}", response_model=ProjectOut)
def update_project(
    project_id: str,
    payload: ProjectUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_contributor_or_above)
):
    """Update a project."""
    project = db.query(Project).filter(Project.id == project_id).first()
    if not project:
        raise NotFoundError("Project", project_id)

    # Update only provided fields
    update_data = payload.dict(exclude_unset=True)

    # Validate unique name if changing
    if "name" in update_data and update_data["name"] != project.name:
        existing = db.query(Project).filter(Project.name == update_data["name"]).first()
        if existing:
            raise ValidationError(f"Project with name '{update_data['name']}' already exists", "name")

    for field, value in update_data.items():
        setattr(project, field, value)

    db.commit()
    db.refresh(project)
    return project


@router.delete("/{project_id}")
def delete_project(
    project_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_pm_or_admin)
):
    """Delete a project and all related RAID items."""
    project = db.query(Project).filter(Project.id == project_id).first()
    if not project:
        raise NotFoundError("Project", project_id)

    db.delete(project)
    db.commit()

    return {"success": True, "message": f"Project '{project.name}' deleted successfully"}


