from typing import Optional
from uuid import uuid4
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from ..db import get_db, Base, engine
from ..models import Project, Risk, Issue, Action, Decision
from ..schemas.risk import RiskCreate, RiskOut
from ..schemas.issue import IssueCreate, IssueOut
from ..schemas.action import ActionCreate, ActionOut
from ..schemas.decision import DecisionCreate, DecisionOut

router = APIRouter(prefix="/projects", tags=["raid"])


@router.get("/{project_id}/raid")
def get_raid(project_id: str, category: Optional[str] = None, db: Session = Depends(get_db)):
    project = db.get(Project, project_id)
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")

    if category == "risks":
        return db.query(Risk).filter(Risk.projectId == project_id).all()
    if category == "issues":
        return db.query(Issue).filter(Issue.projectId == project_id).all()
    if category == "actions":
        return db.query(Action).filter(Action.projectId == project_id).all()
    if category == "decisions":
        return db.query(Decision).filter(Decision.projectId == project_id).all()

    return {
        "risks": db.query(Risk).filter(Risk.projectId == project_id).all(),
        "issues": db.query(Issue).filter(Issue.projectId == project_id).all(),
        "actions": db.query(Action).filter(Action.projectId == project_id).all(),
        "decisions": db.query(Decision).filter(Decision.projectId == project_id).all(),
    }


@router.post("/{project_id}/raid")
def create_raid_item(
    project_id: str,
    category: str,
    payload: dict,
    db: Session = Depends(get_db),
):
    project = db.get(Project, project_id)
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")

    cat = category.lower()
    if cat == "risks":
        data = RiskCreate(projectId=project_id, **payload)
        item = Risk(id=str(uuid4()), **data.model_dump())
    elif cat == "issues":
        data = IssueCreate(projectId=project_id, **payload)
        item = Issue(id=str(uuid4()), **data.model_dump())
    elif cat == "actions":
        data = ActionCreate(projectId=project_id, **payload)
        item = Action(id=str(uuid4()), **data.model_dump())
    elif cat == "decisions":
        data = DecisionCreate(projectId=project_id, **payload)
        item = Decision(id=str(uuid4()), **data.model_dump())
    else:
        raise HTTPException(status_code=400, detail="Unknown category")

    db.add(item)
    db.commit()
    db.refresh(item)
    return item


