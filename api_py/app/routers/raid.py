"""RAID router with comprehensive CRUD operations for all RAID items."""

from typing import Optional, List, Union
from uuid import uuid4
from fastapi import APIRouter, Depends, Query, Path
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from ..db import get_db
from ..models import Project, Risk, Issue, Action, Decision, User, SavedFilter
from ..schemas.risk import RiskCreate, RiskUpdate, RiskOut
from ..schemas.issue import IssueCreate, IssueUpdate, IssueOut
from ..schemas.action import ActionCreate, ActionUpdate, ActionOut
from ..schemas.decision import DecisionCreate, DecisionUpdate, DecisionOut
from ..schemas.common import (
    FilterParams, SavedFilterCreate, SavedFilterUpdate, SavedFilterOut,
    SearchSuggestionsResponse, FilterOptionsResponse, PaginatedResponse
)
from ..exceptions import NotFoundError, ValidationError, PermissionError
from ..services.filtering import AdvancedFilterService
from ..auth import get_current_active_user

router = APIRouter(prefix="/projects", tags=["RAID"])


@router.get("/{project_id}/raid")
def get_raid_items(
    project_id: str = Path(..., description="Project ID"),
    category: Optional[str] = Query(None, description="Filter by category: risks, issues, actions, decisions"),
    filters: FilterParams = Depends(),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get RAID items for a project with advanced filtering."""
    # Verify project exists
    project = db.query(Project).filter(Project.id == project_id).first()
    if not project:
        raise NotFoundError("Project", project_id)

    def get_filtered_items(model):
        """Get filtered items for a specific model."""
        query = db.query(model).filter(model.projectId == project_id)
        query = AdvancedFilterService.apply_advanced_filters(query, model, filters)

        # Apply pagination
        total = query.count()
        items = query.offset(filters.offset).limit(filters.limit).all()

        return {
            "items": items,
            "total": total,
            "offset": filters.offset,
            "limit": filters.limit
        }

    if category == "risks":
        return get_filtered_items(Risk)
    elif category == "issues":
        return get_filtered_items(Issue)
    elif category == "actions":
        return get_filtered_items(Action)
    elif category == "decisions":
        return get_filtered_items(Decision)
    else:
        # Return all RAID items with pagination info
        return {
            "risks": get_filtered_items(Risk),
            "issues": get_filtered_items(Issue),
            "actions": get_filtered_items(Action),
            "decisions": get_filtered_items(Decision),
        }


@router.get("/{project_id}/filter-options", response_model=FilterOptionsResponse)
def get_filter_options(
    project_id: str = Path(..., description="Project ID"),
    category: str = Query(..., description="RAID category: risks, issues, actions, decisions"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get available filter options for a RAID category."""
    # Verify project exists
    project = db.query(Project).filter(Project.id == project_id).first()
    if not project:
        raise NotFoundError("Project", project_id)

    # Map category to model
    model_map = {
        "risks": Risk,
        "issues": Issue,
        "actions": Action,
        "decisions": Decision
    }

    model = model_map.get(category)
    if not model:
        raise ValidationError(f"Invalid category: {category}")

    options = AdvancedFilterService.get_filter_options(db, project_id, model)

    # Fill in missing keys with empty lists
    return FilterOptionsResponse(
        statuses=options.get('status', []),
        priorities=options.get('priority', []),
        categories=options.get('category', []),
        severities=options.get('severity', []),
        owners=options.get('owner', []),
        assignees=options.get('assignee', []),
        tags=[]  # TODO: Implement tags when added to models
    )


@router.get("/{project_id}/search-suggestions", response_model=SearchSuggestionsResponse)
def get_search_suggestions(
    project_id: str = Path(..., description="Project ID"),
    category: str = Query(..., description="RAID category: risks, issues, actions, decisions"),
    q: str = Query(..., min_length=2, description="Search query"),
    limit: int = Query(10, ge=1, le=50, description="Maximum number of suggestions"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get search suggestions for a RAID category."""
    # Verify project exists
    project = db.query(Project).filter(Project.id == project_id).first()
    if not project:
        raise NotFoundError("Project", project_id)

    # Map category to model
    model_map = {
        "risks": Risk,
        "issues": Issue,
        "actions": Action,
        "decisions": Decision
    }

    model = model_map.get(category)
    if not model:
        raise ValidationError(f"Invalid category: {category}")

    suggestions = AdvancedFilterService.get_search_suggestions(
        db, project_id, q, model, limit
    )

    # TODO: Add recent searches from user preferences
    recent_searches = []

    return SearchSuggestionsResponse(
        suggestions=suggestions,
        recent_searches=recent_searches
    )


# Saved Filters endpoints
@router.post("/{project_id}/saved-filters", response_model=SavedFilterOut)
def create_saved_filter(
    filter_data: SavedFilterCreate,
    project_id: str = Path(..., description="Project ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Create a new saved filter."""
    # Verify project exists
    project = db.query(Project).filter(Project.id == project_id).first()
    if not project:
        raise NotFoundError("Project", project_id)

    # Check if filter name already exists for this user/project
    existing = db.query(SavedFilter).filter(
        SavedFilter.name == filter_data.name,
        SavedFilter.created_by == current_user.id,
        SavedFilter.project_id == project_id
    ).first()

    if existing:
        raise ValidationError(f"Filter with name '{filter_data.name}' already exists")

    saved_filter = AdvancedFilterService.save_filter(
        db=db,
        user_id=current_user.id,
        name=filter_data.name,
        description=filter_data.description,
        filter_data=filter_data.filter_data,
        project_id=project_id,
        is_public=filter_data.is_public
    )

    return saved_filter


@router.get("/{project_id}/saved-filters", response_model=List[SavedFilterOut])
def get_saved_filters(
    project_id: str = Path(..., description="Project ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get saved filters for a project."""
    # Verify project exists
    project = db.query(Project).filter(Project.id == project_id).first()
    if not project:
        raise NotFoundError("Project", project_id)

    filters = AdvancedFilterService.get_saved_filters(
        db=db,
        user_id=current_user.id,
        project_id=project_id
    )

    return filters


@router.get("/saved-filters/{filter_id}", response_model=SavedFilterOut)
def get_saved_filter(
    filter_id: str = Path(..., description="Filter ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get a specific saved filter."""
    saved_filter = db.query(SavedFilter).filter(SavedFilter.id == filter_id).first()
    if not saved_filter:
        raise NotFoundError("SavedFilter", filter_id)

    # Check permissions
    if not saved_filter.is_public and saved_filter.created_by != current_user.id:
        raise PermissionError("You don't have permission to access this filter")

    return saved_filter


@router.put("/saved-filters/{filter_id}", response_model=SavedFilterOut)
def update_saved_filter(
    filter_data: SavedFilterUpdate,
    filter_id: str = Path(..., description="Filter ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Update a saved filter."""
    saved_filter = db.query(SavedFilter).filter(SavedFilter.id == filter_id).first()
    if not saved_filter:
        raise NotFoundError("SavedFilter", filter_id)

    # Check permissions
    if saved_filter.created_by != current_user.id:
        raise PermissionError("You can only update your own filters")

    # Update fields
    if filter_data.name is not None:
        saved_filter.name = filter_data.name
    if filter_data.description is not None:
        saved_filter.description = filter_data.description
    if filter_data.filter_data is not None:
        saved_filter.filter_data = filter_data.filter_data
    if filter_data.is_public is not None:
        saved_filter.is_public = filter_data.is_public

    db.commit()
    db.refresh(saved_filter)

    return saved_filter


@router.delete("/saved-filters/{filter_id}")
def delete_saved_filter(
    filter_id: str = Path(..., description="Filter ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Delete a saved filter."""
    saved_filter = db.query(SavedFilter).filter(SavedFilter.id == filter_id).first()
    if not saved_filter:
        raise NotFoundError("SavedFilter", filter_id)

    # Check permissions
    if saved_filter.created_by != current_user.id:
        raise PermissionError("You can only delete your own filters")

    db.delete(saved_filter)
    db.commit()

    return {"success": True, "message": "Filter deleted successfully"}


# Risk endpoints
@router.post("/{project_id}/risks", response_model=RiskOut)
def create_risk(project_id: str, payload: RiskCreate, db: Session = Depends(get_db)):
    """Create a new risk."""
    # Verify project exists
    project = db.query(Project).filter(Project.id == project_id).first()
    if not project:
        raise NotFoundError("Project", project_id)

    # Create risk with calculated score
    risk_data = payload.dict()
    risk_data["projectId"] = project_id

    new_risk = Risk(
        id=str(uuid4()),
        **risk_data
    )

    # Calculate score
    new_risk.calculate_score()

    db.add(new_risk)
    db.commit()
    db.refresh(new_risk)
    return new_risk


@router.get("/{project_id}/risks", response_model=List[RiskOut])
def list_risks(project_id: str, db: Session = Depends(get_db)):
    """List all risks for a project."""
    project = db.query(Project).filter(Project.id == project_id).first()
    if not project:
        raise NotFoundError("Project", project_id)

    return db.query(Risk).filter(Risk.projectId == project_id).order_by(Risk.createdAt.desc()).all()


@router.get("/risks/{risk_id}", response_model=RiskOut)
def get_risk(risk_id: str, db: Session = Depends(get_db)):
    """Get a specific risk."""
    risk = db.query(Risk).filter(Risk.id == risk_id).first()
    if not risk:
        raise NotFoundError("Risk", risk_id)
    return risk


@router.put("/risks/{risk_id}", response_model=RiskOut)
def update_risk(risk_id: str, payload: RiskUpdate, db: Session = Depends(get_db)):
    """Update a risk."""
    risk = db.query(Risk).filter(Risk.id == risk_id).first()
    if not risk:
        raise NotFoundError("Risk", risk_id)

    # Update only provided fields
    update_data = payload.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(risk, field, value)

    # Recalculate score if probability or impact changed
    if "probability" in update_data or "impact" in update_data:
        risk.calculate_score()

    db.commit()
    db.refresh(risk)
    return risk


@router.delete("/risks/{risk_id}")
def delete_risk(risk_id: str, db: Session = Depends(get_db)):
    """Delete a risk."""
    risk = db.query(Risk).filter(Risk.id == risk_id).first()
    if not risk:
        raise NotFoundError("Risk", risk_id)

    db.delete(risk)
    db.commit()
    return {"success": True, "message": "Risk deleted successfully"}


# Issue endpoints
@router.post("/{project_id}/issues", response_model=IssueOut)
def create_issue(project_id: str, payload: IssueCreate, db: Session = Depends(get_db)):
    """Create a new issue."""
    project = db.query(Project).filter(Project.id == project_id).first()
    if not project:
        raise NotFoundError("Project", project_id)

    issue_data = payload.dict()
    issue_data["projectId"] = project_id

    new_issue = Issue(
        id=str(uuid4()),
        **issue_data
    )

    db.add(new_issue)
    db.commit()
    db.refresh(new_issue)
    return new_issue


@router.get("/{project_id}/issues", response_model=List[IssueOut])
def list_issues(project_id: str, db: Session = Depends(get_db)):
    """List all issues for a project."""
    project = db.query(Project).filter(Project.id == project_id).first()
    if not project:
        raise NotFoundError("Project", project_id)

    return db.query(Issue).filter(Issue.projectId == project_id).order_by(Issue.createdAt.desc()).all()


@router.get("/issues/{issue_id}", response_model=IssueOut)
def get_issue(issue_id: str, db: Session = Depends(get_db)):
    """Get a specific issue."""
    issue = db.query(Issue).filter(Issue.id == issue_id).first()
    if not issue:
        raise NotFoundError("Issue", issue_id)
    return issue


@router.put("/issues/{issue_id}", response_model=IssueOut)
def update_issue(issue_id: str, payload: IssueUpdate, db: Session = Depends(get_db)):
    """Update an issue."""
    issue = db.query(Issue).filter(Issue.id == issue_id).first()
    if not issue:
        raise NotFoundError("Issue", issue_id)

    update_data = payload.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(issue, field, value)

    db.commit()
    db.refresh(issue)
    return issue


@router.delete("/issues/{issue_id}")
def delete_issue(issue_id: str, db: Session = Depends(get_db)):
    """Delete an issue."""
    issue = db.query(Issue).filter(Issue.id == issue_id).first()
    if not issue:
        raise NotFoundError("Issue", issue_id)

    db.delete(issue)
    db.commit()
    return {"success": True, "message": "Issue deleted successfully"}


# Action endpoints
@router.post("/{project_id}/actions", response_model=ActionOut)
def create_action(project_id: str, payload: ActionCreate, db: Session = Depends(get_db)):
    """Create a new action."""
    project = db.query(Project).filter(Project.id == project_id).first()
    if not project:
        raise NotFoundError("Project", project_id)

    action_data = payload.dict()
    action_data["projectId"] = project_id

    new_action = Action(
        id=str(uuid4()),
        **action_data
    )

    db.add(new_action)
    db.commit()
    db.refresh(new_action)
    return new_action


@router.get("/{project_id}/actions", response_model=List[ActionOut])
def list_actions(project_id: str, db: Session = Depends(get_db)):
    """List all actions for a project."""
    project = db.query(Project).filter(Project.id == project_id).first()
    if not project:
        raise NotFoundError("Project", project_id)

    return db.query(Action).filter(Action.projectId == project_id).order_by(Action.createdAt.desc()).all()


@router.get("/actions/{action_id}", response_model=ActionOut)
def get_action(action_id: str, db: Session = Depends(get_db)):
    """Get a specific action."""
    action = db.query(Action).filter(Action.id == action_id).first()
    if not action:
        raise NotFoundError("Action", action_id)
    return action


@router.put("/actions/{action_id}", response_model=ActionOut)
def update_action(action_id: str, payload: ActionUpdate, db: Session = Depends(get_db)):
    """Update an action."""
    action = db.query(Action).filter(Action.id == action_id).first()
    if not action:
        raise NotFoundError("Action", action_id)

    update_data = payload.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(action, field, value)

    db.commit()
    db.refresh(action)
    return action


@router.delete("/actions/{action_id}")
def delete_action(action_id: str, db: Session = Depends(get_db)):
    """Delete an action."""
    action = db.query(Action).filter(Action.id == action_id).first()
    if not action:
        raise NotFoundError("Action", action_id)

    db.delete(action)
    db.commit()
    return {"success": True, "message": "Action deleted successfully"}


# Decision endpoints
@router.post("/{project_id}/decisions", response_model=DecisionOut)
def create_decision(project_id: str, payload: DecisionCreate, db: Session = Depends(get_db)):
    """Create a new decision."""
    project = db.query(Project).filter(Project.id == project_id).first()
    if not project:
        raise NotFoundError("Project", project_id)

    decision_data = payload.dict()
    decision_data["projectId"] = project_id

    new_decision = Decision(
        id=str(uuid4()),
        **decision_data
    )

    db.add(new_decision)
    db.commit()
    db.refresh(new_decision)
    return new_decision


@router.get("/{project_id}/decisions", response_model=List[DecisionOut])
def list_decisions(project_id: str, db: Session = Depends(get_db)):
    """List all decisions for a project."""
    project = db.query(Project).filter(Project.id == project_id).first()
    if not project:
        raise NotFoundError("Project", project_id)

    return db.query(Decision).filter(Decision.projectId == project_id).order_by(Decision.createdAt.desc()).all()


@router.get("/decisions/{decision_id}", response_model=DecisionOut)
def get_decision(decision_id: str, db: Session = Depends(get_db)):
    """Get a specific decision."""
    decision = db.query(Decision).filter(Decision.id == decision_id).first()
    if not decision:
        raise NotFoundError("Decision", decision_id)
    return decision


@router.put("/decisions/{decision_id}", response_model=DecisionOut)
def update_decision(decision_id: str, payload: DecisionUpdate, db: Session = Depends(get_db)):
    """Update a decision."""
    decision = db.query(Decision).filter(Decision.id == decision_id).first()
    if not decision:
        raise NotFoundError("Decision", decision_id)

    update_data = payload.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(decision, field, value)

    db.commit()
    db.refresh(decision)
    return decision


@router.delete("/decisions/{decision_id}")
def delete_decision(decision_id: str, db: Session = Depends(get_db)):
    """Delete a decision."""
    decision = db.query(Decision).filter(Decision.id == decision_id).first()
    if not decision:
        raise NotFoundError("Decision", decision_id)

    db.delete(decision)
    db.commit()
    return {"success": True, "message": "Decision deleted successfully"}


# Utility endpoints
@router.post("/risks/{risk_id}/convert-to-issue", response_model=IssueOut)
def convert_risk_to_issue(risk_id: str, db: Session = Depends(get_db)):
    """Convert a risk to an issue."""
    risk = db.query(Risk).filter(Risk.id == risk_id).first()
    if not risk:
        raise NotFoundError("Risk", risk_id)

    # Create issue from risk
    new_issue = Issue(
        id=str(uuid4()),
        projectId=risk.projectId,
        title=risk.title,
        description=risk.description or risk.trigger,
        severity="High" if risk.score >= 3.0 else "Medium" if risk.score >= 2.0 else "Low",
        owner=risk.owner,
        status="Open",
        relatedRiskId=risk.id
    )

    # Update risk status
    risk.status = "In progress"

    db.add(new_issue)
    db.commit()
    db.refresh(new_issue)

    return new_issue


