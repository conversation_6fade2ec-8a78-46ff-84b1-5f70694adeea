"""Authentication router with login, registration, and user management."""

from datetime import timedelta
from typing import List
from fastapi import APIRouter, Depends, status
from sqlalchemy.orm import Session

from ..db import get_db
from ..models.user import User
from ..schemas.auth import (
    UserLogin, UserRegister, Token, PasswordChange, 
    UserProfile, RoleUpdate, UserStatus
)
from ..schemas.user import UserOut, UserCreate, UserUpdate
from ..auth import (
    authenticate_user, create_access_token, create_user,
    get_current_active_user, require_admin, require_pm_or_admin,
    get_password_hash, verify_password
)
from ..config import settings
from ..exceptions import AuthenticationError, ValidationError, NotFoundError

router = APIRouter(prefix="/auth", tags=["Authentication"])


@router.post("/login", response_model=Token)
def login(user_credentials: UserLogin, db: Session = Depends(get_db)):
    """Authenticate user and return access token."""
    user = authenticate_user(db, user_credentials.email, user_credentials.password)
    if not user:
        raise AuthenticationError("Incorrect email or password")
    
    if not user.is_active:
        raise AuthenticationError("User account is disabled")
    
    access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
    access_token = create_access_token(
        data={"sub": user.id, "email": user.email, "role": user.role},
        expires_delta=access_token_expires
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": settings.access_token_expire_minutes * 60
    }


@router.post("/register", response_model=UserOut)
def register(user_data: UserRegister, db: Session = Depends(get_db)):
    """Register a new user."""
    try:
        user = create_user(
            db=db,
            email=user_data.email,
            password=user_data.password,
            full_name=user_data.full_name,
            role=user_data.role or "viewer"
        )
        return user
    except ValueError as e:
        raise ValidationError(str(e), "email")


@router.get("/me", response_model=UserOut)
def get_current_user_profile(current_user: User = Depends(get_current_active_user)):
    """Get current user profile."""
    return current_user


@router.put("/me", response_model=UserOut)
def update_current_user_profile(
    profile_data: UserProfile,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update current user profile."""
    update_data = profile_data.dict(exclude_unset=True)
    
    for field, value in update_data.items():
        setattr(current_user, field, value)
    
    db.commit()
    db.refresh(current_user)
    return current_user


@router.post("/change-password")
def change_password(
    password_data: PasswordChange,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Change user password."""
    # Verify current password
    if not verify_password(password_data.current_password, current_user.password_hash):
        raise AuthenticationError("Current password is incorrect")
    
    # Update password
    current_user.password_hash = get_password_hash(password_data.new_password)
    db.commit()
    
    return {"success": True, "message": "Password changed successfully"}


# Admin-only endpoints
@router.get("/users", response_model=List[UserOut])
def list_users(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin)
):
    """List all users (admin only)."""
    users = db.query(User).offset(skip).limit(limit).all()
    return users


@router.get("/users/{user_id}", response_model=UserOut)
def get_user(
    user_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_pm_or_admin)
):
    """Get user by ID (PM or admin only)."""
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise NotFoundError("User", user_id)
    return user


@router.put("/users/{user_id}/role", response_model=UserOut)
def update_user_role(
    user_id: str,
    role_data: RoleUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin)
):
    """Update user role (admin only)."""
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise NotFoundError("User", user_id)
    
    # Validate role
    valid_roles = ["viewer", "contributor", "pm", "admin"]
    if role_data.role not in valid_roles:
        raise ValidationError(f"Invalid role. Must be one of: {valid_roles}", "role")
    
    user.role = role_data.role
    db.commit()
    db.refresh(user)
    return user


@router.put("/users/{user_id}/status", response_model=UserOut)
def update_user_status(
    user_id: str,
    status_data: UserStatus,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin)
):
    """Update user status (admin only)."""
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise NotFoundError("User", user_id)
    
    # Prevent admin from disabling themselves
    if user.id == current_user.id and not status_data.is_active:
        raise ValidationError("Cannot disable your own account", "is_active")
    
    user.is_active = status_data.is_active
    db.commit()
    db.refresh(user)
    return user


@router.delete("/users/{user_id}")
def delete_user(
    user_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin)
):
    """Delete user (admin only)."""
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise NotFoundError("User", user_id)
    
    # Prevent admin from deleting themselves
    if user.id == current_user.id:
        raise ValidationError("Cannot delete your own account", "user_id")
    
    db.delete(user)
    db.commit()
    return {"success": True, "message": f"User {user.email} deleted successfully"}


@router.post("/create-admin", response_model=UserOut)
def create_admin_user(
    user_data: UserRegister,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin)
):
    """Create admin user (admin only)."""
    try:
        user = create_user(
            db=db,
            email=user_data.email,
            password=user_data.password,
            full_name=user_data.full_name,
            role="admin"
        )
        return user
    except ValueError as e:
        raise ValidationError(str(e), "email")
