"""Audit router for viewing change history."""

from typing import List, Optional
from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session

from ..db import get_db
from ..models.audit import AuditEntry
from ..schemas.audit import AuditEntryOut
from ..auth import get_current_active_user
from ..models.user import User
from ..services.audit import AuditService

router = APIRouter(prefix="/audit", tags=["Audit"])


@router.get("/", response_model=List[AuditEntryOut])
def get_audit_history(
    item_type: Optional[str] = Query(None, description="Filter by item type (project, risk, action, issue, decision)"),
    item_id: Optional[str] = Query(None, description="Filter by specific item ID"),
    user_id: Optional[str] = Query(None, description="Filter by user who made changes"),
    action: Optional[str] = Query(None, description="Filter by action type (create, update, delete)"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of results"),
    offset: int = Query(0, ge=0, description="Number of results to skip"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get audit history with optional filters.
    
    Returns a list of audit entries showing changes made to RAID items.
    """
    return AuditService.get_audit_history(
        db=db,
        item_type=item_type,
        item_id=item_id,
        user_id=user_id,
        action=action,
        limit=limit,
        offset=offset
    )


@router.get("/item/{item_type}/{item_id}", response_model=List[AuditEntryOut])
def get_item_audit_history(
    item_type: str,
    item_id: str,
    limit: int = Query(50, ge=1, le=500, description="Maximum number of results"),
    offset: int = Query(0, ge=0, description="Number of results to skip"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get audit history for a specific item.
    
    Returns all changes made to a specific project, risk, action, issue, or decision.
    """
    return AuditService.get_audit_history(
        db=db,
        item_type=item_type,
        item_id=item_id,
        limit=limit,
        offset=offset
    )


@router.get("/user/{user_id}", response_model=List[AuditEntryOut])
def get_user_audit_history(
    user_id: str,
    item_type: Optional[str] = Query(None, description="Filter by item type"),
    action: Optional[str] = Query(None, description="Filter by action type"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of results"),
    offset: int = Query(0, ge=0, description="Number of results to skip"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get audit history for a specific user.
    
    Returns all changes made by a specific user.
    """
    return AuditService.get_audit_history(
        db=db,
        item_type=item_type,
        user_id=user_id,
        action=action,
        limit=limit,
        offset=offset
    )


@router.get("/recent", response_model=List[AuditEntryOut])
def get_recent_changes(
    limit: int = Query(20, ge=1, le=100, description="Maximum number of results"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get recent changes across all RAID items.
    
    Returns the most recent changes made to any project, risk, action, issue, or decision.
    """
    return AuditService.get_audit_history(
        db=db,
        limit=limit,
        offset=0
    )
