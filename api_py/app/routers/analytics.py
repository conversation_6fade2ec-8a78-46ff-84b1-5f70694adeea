"""Analytics and dashboard API endpoints."""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.orm import Session
from sqlalchemy import desc, or_

from ..db import get_db
from ..models.user import User
from ..models.analytics import Dashboard, DashboardWidget, TrendAnalysis
from ..services.analytics_service import AnalyticsService
from ..services.chart_processor import ChartProcessor
from ..schemas.analytics import (
    DashboardCreate, DashboardUpdate, DashboardOut,
    DashboardWidgetCreate, DashboardWidgetUpdate, DashboardWidgetOut,
    AnalyticsConfigCreate, AnalyticsConfigOut,
    TrendAnalysisCreate, TrendAnalysisOut,
    AnalyticsQuery, AnalyticsResult,
    ChartDataOut, DashboardSummary
)
from ..auth import get_current_user, require_permission


# Dashboard Management Router
dashboard_router = APIRouter(prefix="/dashboards", tags=["dashboards"])


@dashboard_router.post("/", response_model=DashboardOut)
async def create_dashboard(
    dashboard_data: DashboardCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new dashboard."""
    service = AnalyticsService(db)
    dashboard = service.create_dashboard(dashboard_data, current_user.id)
    
    # Add widget count
    dashboard.widget_count = len(dashboard.widgets) if dashboard.widgets else 0
    
    return dashboard


@dashboard_router.get("/", response_model=List[DashboardOut])
async def get_dashboards(
    project_id: Optional[str] = Query(None, description="Filter by project ID"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get dashboards accessible to the current user."""
    service = AnalyticsService(db)
    dashboards = service.get_dashboards(current_user.id, project_id)
    
    # Add widget counts
    for dashboard in dashboards:
        dashboard.widget_count = len(dashboard.widgets) if dashboard.widgets else 0
    
    return dashboards


@dashboard_router.get("/{dashboard_id}", response_model=DashboardOut)
async def get_dashboard(
    dashboard_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a specific dashboard."""
    service = AnalyticsService(db)
    dashboard = service.get_dashboard(dashboard_id, current_user.id)
    
    if not dashboard:
        raise HTTPException(status_code=404, detail="Dashboard not found or access denied")
    
    # Add widget count
    dashboard.widget_count = len(dashboard.widgets) if dashboard.widgets else 0
    
    return dashboard


@dashboard_router.put("/{dashboard_id}", response_model=DashboardOut)
async def update_dashboard(
    dashboard_id: str,
    dashboard_data: DashboardUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update a dashboard."""
    service = AnalyticsService(db)
    dashboard = service.update_dashboard(dashboard_id, dashboard_data, current_user.id)
    
    if not dashboard:
        raise HTTPException(status_code=404, detail="Dashboard not found or access denied")
    
    # Add widget count
    dashboard.widget_count = len(dashboard.widgets) if dashboard.widgets else 0
    
    return dashboard


@dashboard_router.delete("/{dashboard_id}")
async def delete_dashboard(
    dashboard_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a dashboard."""
    service = AnalyticsService(db)
    success = service.delete_dashboard(dashboard_id, current_user.id)
    
    if not success:
        raise HTTPException(status_code=404, detail="Dashboard not found or access denied")
    
    return {"message": "Dashboard deleted successfully"}


@dashboard_router.get("/{dashboard_id}/widgets", response_model=List[DashboardWidgetOut])
async def get_dashboard_widgets(
    dashboard_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get widgets for a dashboard."""
    service = AnalyticsService(db)
    widgets = service.get_dashboard_widgets(dashboard_id, current_user.id)
    return widgets


# Widget Management Router
widget_router = APIRouter(prefix="/widgets", tags=["widgets"])


@widget_router.post("/", response_model=DashboardWidgetOut)
async def create_widget(
    widget_data: DashboardWidgetCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new dashboard widget."""
    service = AnalyticsService(db)
    widget = service.create_widget(widget_data, current_user.id)
    
    if not widget:
        raise HTTPException(status_code=404, detail="Dashboard not found or access denied")
    
    return widget


@widget_router.get("/{widget_id}", response_model=DashboardWidgetOut)
async def get_widget(
    widget_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a specific widget."""
    widget = db.query(DashboardWidget).join(Dashboard).filter(
        DashboardWidget.id == widget_id,
        or_(
            Dashboard.created_by == current_user.id,
            Dashboard.is_public == True,
            Dashboard.allowed_users.contains([current_user.id])
        )
    ).first()

    if not widget:
        raise HTTPException(status_code=404, detail="Widget not found or access denied")

    return widget


@widget_router.put("/{widget_id}", response_model=DashboardWidgetOut)
async def update_widget(
    widget_id: str,
    widget_data: DashboardWidgetUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update a dashboard widget."""
    service = AnalyticsService(db)
    widget = service.update_widget(widget_id, widget_data, current_user.id)
    
    if not widget:
        raise HTTPException(status_code=404, detail="Widget not found or access denied")
    
    return widget


@widget_router.delete("/{widget_id}")
async def delete_widget(
    widget_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a dashboard widget."""
    service = AnalyticsService(db)
    success = service.delete_widget(widget_id, current_user.id)
    
    if not success:
        raise HTTPException(status_code=404, detail="Widget not found or access denied")
    
    return {"message": "Widget deleted successfully"}


@widget_router.get("/{widget_id}/data")
async def get_widget_data(
    widget_id: str,
    force_refresh: bool = Query(False, description="Force refresh cached data"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get processed data for a widget."""
    service = AnalyticsService(db)
    data = service.get_widget_data(widget_id, current_user.id, force_refresh)
    
    if data is None:
        raise HTTPException(status_code=404, detail="Widget not found or access denied")
    
    return data


# Analytics Query Router
analytics_router = APIRouter(prefix="/analytics", tags=["analytics"])


@analytics_router.post("/query", response_model=AnalyticsResult)
async def execute_analytics_query(
    query_data: AnalyticsQuery,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Execute a custom analytics query."""
    service = AnalyticsService(db)
    result = service.execute_analytics_query(query_data, current_user.id)
    
    if 'error' in result:
        raise HTTPException(status_code=400, detail=result['error'])
    
    return result


@analytics_router.get("/summary", response_model=DashboardSummary)
async def get_dashboard_summary(
    project_id: Optional[str] = Query(None, description="Filter by project ID"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get dashboard summary statistics."""
    service = AnalyticsService(db)
    summary = service.get_dashboard_summary(current_user.id, project_id)
    return summary


# Trend Analysis Router
trends_router = APIRouter(prefix="/trends", tags=["trends"])


@trends_router.post("/", response_model=TrendAnalysisOut)
async def create_trend_analysis(
    analysis_data: TrendAnalysisCreate,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new trend analysis."""
    service = AnalyticsService(db)
    analysis = service.create_trend_analysis(analysis_data, current_user.id)
    return analysis


@trends_router.get("/", response_model=List[TrendAnalysisOut])
async def get_trend_analyses(
    project_id: Optional[str] = Query(None, description="Filter by project ID"),
    analysis_type: Optional[str] = Query(None, description="Filter by analysis type"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get trend analyses for the current user."""
    query = db.query(TrendAnalysis).filter(TrendAnalysis.created_by == current_user.id)
    
    if project_id:
        query = query.filter(TrendAnalysis.project_id == project_id)
    
    if analysis_type:
        query = query.filter(TrendAnalysis.analysis_type == analysis_type)
    
    analyses = query.order_by(desc(TrendAnalysis.created_at)).all()
    return analyses


@trends_router.get("/{analysis_id}", response_model=TrendAnalysisOut)
async def get_trend_analysis(
    analysis_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a specific trend analysis."""
    analysis = db.query(TrendAnalysis).filter(
        TrendAnalysis.id == analysis_id,
        TrendAnalysis.created_by == current_user.id
    ).first()
    
    if not analysis:
        raise HTTPException(status_code=404, detail="Trend analysis not found")
    
    return analysis


@trends_router.delete("/{analysis_id}")
async def delete_trend_analysis(
    analysis_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a trend analysis."""
    analysis = db.query(TrendAnalysis).filter(
        TrendAnalysis.id == analysis_id,
        TrendAnalysis.created_by == current_user.id
    ).first()
    
    if not analysis:
        raise HTTPException(status_code=404, detail="Trend analysis not found")
    
    db.delete(analysis)
    db.commit()
    
    return {"message": "Trend analysis deleted successfully"}


# Chart Processing Router
charts_router = APIRouter(prefix="/charts", tags=["charts"])


@charts_router.post("/process/pie")
async def process_pie_chart(
    data: List[Dict[str, Any]],
    value_field: str,
    label_field: str,
    max_slices: int = Query(10, ge=3, le=20),
    current_user: User = Depends(get_current_user)
):
    """Process data for pie chart visualization."""
    result = ChartProcessor.process_pie_chart(data, value_field, label_field, max_slices)
    return result


@charts_router.post("/process/bar")
async def process_bar_chart(
    data: List[Dict[str, Any]],
    x_field: str,
    y_field: str,
    sort_by: str = Query("value", regex="^(value|label)$"),
    ascending: bool = Query(False),
    current_user: User = Depends(get_current_user)
):
    """Process data for bar chart visualization."""
    result = ChartProcessor.process_bar_chart(data, x_field, y_field, sort_by, ascending)
    return result


@charts_router.post("/process/line")
async def process_line_chart(
    data: List[Dict[str, Any]],
    x_field: str,
    y_field: str,
    time_aggregation: str = Query("day", regex="^(hour|day|week|month)$"),
    current_user: User = Depends(get_current_user)
):
    """Process data for line chart visualization."""
    result = ChartProcessor.process_line_chart(data, x_field, y_field, time_aggregation)
    return result


@charts_router.post("/process/scatter")
async def process_scatter_chart(
    data: List[Dict[str, Any]],
    x_field: str,
    y_field: str,
    size_field: Optional[str] = None,
    color_field: Optional[str] = None,
    current_user: User = Depends(get_current_user)
):
    """Process data for scatter plot visualization."""
    result = ChartProcessor.process_scatter_chart(data, x_field, y_field, size_field, color_field)
    return result


@charts_router.post("/process/heatmap")
async def process_heatmap_chart(
    data: List[Dict[str, Any]],
    x_field: str,
    y_field: str,
    value_field: str,
    current_user: User = Depends(get_current_user)
):
    """Process data for heatmap visualization."""
    result = ChartProcessor.process_heatmap_chart(data, x_field, y_field, value_field)
    return result


@charts_router.post("/process/gauge")
async def process_gauge_chart(
    current_value: float,
    min_value: float = Query(0),
    max_value: float = Query(100),
    thresholds: Optional[Dict[str, float]] = None,
    current_user: User = Depends(get_current_user)
):
    """Process data for gauge chart visualization."""
    result = ChartProcessor.process_gauge_chart(current_value, min_value, max_value, thresholds)
    return result
