"""Notification service for RAID application."""

import smtplib
import logging
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from jinja2 import Template

from ..models.notification import (
    Notification, NotificationTemplate, NotificationPreference, NotificationQueue
)
from ..models.user import User
from ..models.project import Project
from ..schemas.notification import (
    NotificationCreate, NotificationEventType, NotificationType,
    NotificationPriority, NotificationStatus, EmailConfig
)

logger = logging.getLogger(__name__)


class NotificationService:
    """Service for managing notifications."""

    def __init__(self, db: Session, email_config: Optional[EmailConfig] = None):
        self.db = db
        self.email_config = email_config

    def create_notification(
        self,
        recipient_id: str,
        event_type: NotificationEventType,
        subject: str,
        message: str,
        sender_id: Optional[str] = None,
        entity_type: Optional[str] = None,
        entity_id: Optional[str] = None,
        project_id: Optional[str] = None,
        priority: NotificationPriority = NotificationPriority.NORMAL,
        notification_type: NotificationType = NotificationType.BOTH,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Notification:
        """Create a new notification."""
        
        # Check user preferences
        preferences = self.get_user_preferences(recipient_id, event_type, project_id)
        if not self._should_notify(preferences, priority, notification_type):
            logger.info(f"Notification skipped due to user preferences: {recipient_id}")
            return None

        notification = Notification(
            recipient_id=recipient_id,
            sender_id=sender_id,
            type=notification_type.value,
            event_type=event_type.value,
            subject=subject,
            message=message,
            entity_type=entity_type,
            entity_id=entity_id,
            project_id=project_id,
            priority=priority.value,
            notification_metadata=metadata or {}
        )

        self.db.add(notification)
        self.db.commit()
        self.db.refresh(notification)

        # Process notification based on type and preferences
        if preferences and preferences.immediate:
            self._process_notification_immediately(notification)
        else:
            self._queue_notification(notification)

        return notification

    def create_from_template(
        self,
        template_id: str,
        recipient_id: str,
        variables: Dict[str, Any],
        sender_id: Optional[str] = None,
        entity_type: Optional[str] = None,
        entity_id: Optional[str] = None,
        project_id: Optional[str] = None
    ) -> Optional[Notification]:
        """Create notification from template."""
        
        template = self.db.query(NotificationTemplate).filter(
            NotificationTemplate.id == template_id,
            NotificationTemplate.is_active == True
        ).first()

        if not template:
            logger.error(f"Template not found or inactive: {template_id}")
            return None

        # Render template with variables
        subject = self._render_template(template.subject_template, variables)
        message = self._render_template(template.body_template, variables)
        html_content = None
        if template.html_template:
            html_content = self._render_template(template.html_template, variables)

        notification = Notification(
            recipient_id=recipient_id,
            sender_id=sender_id,
            type=template.type,
            event_type=template.event_type,
            subject=subject,
            message=message,
            html_content=html_content,
            entity_type=entity_type,
            entity_id=entity_id,
            project_id=project_id,
            priority=template.priority,
            notification_metadata=variables
        )

        self.db.add(notification)
        self.db.commit()
        self.db.refresh(notification)

        return notification

    def bulk_create_notifications(
        self,
        recipient_ids: List[str],
        event_type: NotificationEventType,
        subject: str,
        message: str,
        sender_id: Optional[str] = None,
        entity_type: Optional[str] = None,
        entity_id: Optional[str] = None,
        project_id: Optional[str] = None,
        priority: NotificationPriority = NotificationPriority.NORMAL
    ) -> List[Notification]:
        """Create multiple notifications."""
        
        notifications = []
        for recipient_id in recipient_ids:
            notification = self.create_notification(
                recipient_id=recipient_id,
                event_type=event_type,
                subject=subject,
                message=message,
                sender_id=sender_id,
                entity_type=entity_type,
                entity_id=entity_id,
                project_id=project_id,
                priority=priority
            )
            if notification:
                notifications.append(notification)

        return notifications

    def mark_as_read(self, notification_id: str, user_id: str) -> bool:
        """Mark notification as read."""
        
        notification = self.db.query(Notification).filter(
            Notification.id == notification_id,
            Notification.recipient_id == user_id
        ).first()

        if not notification:
            return False

        notification.status = NotificationStatus.READ.value
        notification.read_at = datetime.utcnow()
        self.db.commit()

        return True

    def mark_all_as_read(self, user_id: str, project_id: Optional[str] = None) -> int:
        """Mark all notifications as read for a user."""
        
        query = self.db.query(Notification).filter(
            Notification.recipient_id == user_id,
            Notification.status != NotificationStatus.READ.value
        )

        if project_id:
            query = query.filter(Notification.project_id == project_id)

        count = query.count()
        query.update({
            "status": NotificationStatus.READ.value,
            "read_at": datetime.utcnow()
        })
        self.db.commit()

        return count

    def get_user_notifications(
        self,
        user_id: str,
        unread_only: bool = False,
        project_id: Optional[str] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[Notification]:
        """Get notifications for a user."""
        
        query = self.db.query(Notification).filter(
            Notification.recipient_id == user_id
        )

        if unread_only:
            query = query.filter(Notification.status != NotificationStatus.READ.value)

        if project_id:
            query = query.filter(Notification.project_id == project_id)

        return query.order_by(Notification.created_at.desc()).offset(offset).limit(limit).all()

    def get_user_preferences(
        self,
        user_id: str,
        event_type: NotificationEventType,
        project_id: Optional[str] = None
    ) -> Optional[NotificationPreference]:
        """Get user notification preferences."""
        
        # Try project-specific preferences first
        if project_id:
            preference = self.db.query(NotificationPreference).filter(
                NotificationPreference.user_id == user_id,
                NotificationPreference.event_type == event_type.value,
                NotificationPreference.project_id == project_id
            ).first()
            if preference:
                return preference

        # Fall back to global preferences
        return self.db.query(NotificationPreference).filter(
            NotificationPreference.user_id == user_id,
            NotificationPreference.event_type == event_type.value,
            NotificationPreference.project_id.is_(None)
        ).first()

    def _should_notify(
        self,
        preferences: Optional[NotificationPreference],
        priority: NotificationPriority,
        notification_type: NotificationType
    ) -> bool:
        """Check if notification should be sent based on preferences."""
        
        if not preferences:
            return True  # Default to sending if no preferences set

        # Check priority threshold
        priority_levels = {
            NotificationPriority.LOW: 0,
            NotificationPriority.NORMAL: 1,
            NotificationPriority.HIGH: 2,
            NotificationPriority.URGENT: 3
        }
        
        threshold_level = priority_levels.get(NotificationPriority(preferences.priority_threshold), 0)
        current_level = priority_levels.get(priority, 1)
        
        if current_level < threshold_level:
            return False

        # Check delivery method preferences
        if notification_type == NotificationType.EMAIL and not preferences.email_enabled:
            return False
        if notification_type == NotificationType.IN_APP and not preferences.in_app_enabled:
            return False

        return True

    def _process_notification_immediately(self, notification: Notification):
        """Process notification immediately."""
        
        if notification.type in [NotificationType.EMAIL.value, NotificationType.BOTH.value]:
            self._send_email_notification(notification)

        if notification.type in [NotificationType.IN_APP.value, NotificationType.BOTH.value]:
            # In-app notifications are already created, just mark as sent
            notification.status = NotificationStatus.SENT.value
            notification.sent_at = datetime.utcnow()
            self.db.commit()

    def _queue_notification(self, notification: Notification):
        """Queue notification for batch processing."""
        
        queue_item = NotificationQueue(
            notification_data={
                "notification_id": notification.id,
                "type": notification.type,
                "priority": notification.priority
            },
            priority=notification.priority,
            scheduled_for=datetime.utcnow() + timedelta(minutes=5)  # Process in 5 minutes
        )

        self.db.add(queue_item)
        self.db.commit()

    def _send_email_notification(self, notification: Notification) -> bool:
        """Send email notification."""
        
        if not self.email_config:
            logger.warning("Email configuration not provided")
            return False

        try:
            # Get recipient email
            recipient = self.db.query(User).filter(User.id == notification.recipient_id).first()
            if not recipient:
                logger.error(f"Recipient not found: {notification.recipient_id}")
                return False

            # Create email message
            msg = MIMEMultipart('alternative')
            msg['Subject'] = notification.subject
            msg['From'] = f"{self.email_config.from_name} <{self.email_config.from_email}>"
            msg['To'] = recipient.email

            # Add text content
            text_part = MIMEText(notification.message, 'plain')
            msg.attach(text_part)

            # Add HTML content if available
            if notification.html_content:
                html_part = MIMEText(notification.html_content, 'html')
                msg.attach(html_part)

            # Send email
            with smtplib.SMTP(self.email_config.smtp_host, self.email_config.smtp_port) as server:
                if self.email_config.use_tls:
                    server.starttls()
                server.login(self.email_config.smtp_username, self.email_config.smtp_password)
                server.send_message(msg)

            # Update notification status
            notification.status = NotificationStatus.SENT.value
            notification.sent_at = datetime.utcnow()
            self.db.commit()

            logger.info(f"Email sent successfully: {notification.id}")
            return True

        except Exception as e:
            logger.error(f"Failed to send email notification {notification.id}: {str(e)}")
            notification.status = NotificationStatus.FAILED.value
            notification.failed_at = datetime.utcnow()
            notification.failure_reason = str(e)
            self.db.commit()
            return False

    def _render_template(self, template_str: str, variables: Dict[str, Any]) -> str:
        """Render Jinja2 template with variables."""
        
        try:
            template = Template(template_str)
            return template.render(**variables)
        except Exception as e:
            logger.error(f"Template rendering failed: {str(e)}")
            return template_str  # Return original if rendering fails
