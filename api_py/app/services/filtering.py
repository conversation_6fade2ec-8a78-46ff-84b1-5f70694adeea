"""Advanced filtering service for RAID items."""

from typing import List, Dict, Any, Optional, Union, Type
from datetime import datetime
from sqlalchemy.orm import Session, Query
from sqlalchemy import and_, or_, func, text
from sqlalchemy.sql import operators

from ..models import Risk, Action, Issue, Decision, SavedFilter, User
from ..schemas.common import FilterParams, SearchSuggestion


class AdvancedFilterService:
    """Service for advanced filtering and search functionality."""
    
    # Define searchable fields for each model
    SEARCHABLE_FIELDS = {
        Risk: ['title', 'description', 'category', 'owner'],
        Action: ['title', 'description', 'category', 'owner', 'assignee'],
        Issue: ['title', 'description', 'category', 'owner'],
        Decision: ['title', 'description', 'rationale', 'category', 'decidedBy'],
    }
    
    # Define sortable fields for each model
    SORTABLE_FIELDS = {
        Risk: ['title', 'priority', 'probability', 'impact', 'score', 'status', 'createdAt', 'updatedAt'],
        Action: ['title', 'priority', 'status', 'progress', 'dueAt', 'createdAt', 'updatedAt'],
        Issue: ['title', 'priority', 'severity', 'status', 'createdAt', 'updatedAt'],
        Decision: ['title', 'priority', 'status', 'createdAt', 'updatedAt'],
    }
    
    @classmethod
    def apply_advanced_filters(
        cls,
        query: Query,
        model: Type[Union[Risk, Action, Issue, Decision]],
        filters: FilterParams
    ) -> Query:
        """Apply advanced filters to a query."""
        
        # Text search
        if filters.q:
            query = cls._apply_text_search(query, model, filters)
        
        # Multi-value filters
        if filters.status:
            query = query.filter(model.status.in_(filters.status))
        
        if filters.priority:
            query = query.filter(model.priority.in_(filters.priority))
        
        if filters.category:
            query = query.filter(model.category.in_(filters.category))
        
        if filters.owner:
            query = query.filter(model.owner.in_(filters.owner))
        
        # Model-specific filters
        if hasattr(model, 'severity') and filters.severity:
            query = query.filter(model.severity.in_(filters.severity))
        
        if hasattr(model, 'assignee') and filters.assignee:
            query = query.filter(model.assignee.in_(filters.assignee))
        
        if hasattr(model, 'decidedBy') and filters.decided_by:
            query = query.filter(model.decidedBy.in_(filters.decided_by))
        
        # Date range filters
        query = cls._apply_date_filters(query, model, filters)
        
        # Numeric range filters
        query = cls._apply_numeric_filters(query, model, filters)
        
        # Apply sorting
        query = cls._apply_sorting(query, model, filters)
        
        return query
    
    @classmethod
    def _apply_text_search(
        cls,
        query: Query,
        model: Type[Union[Risk, Action, Issue, Decision]],
        filters: FilterParams
    ) -> Query:
        """Apply text search with advanced options."""
        search_term = filters.q.strip()
        if not search_term:
            return query
        
        # Determine search fields
        search_fields = filters.search_fields or cls.SEARCHABLE_FIELDS.get(model, [])
        
        # Build search conditions
        search_conditions = []
        
        for field_name in search_fields:
            if hasattr(model, field_name):
                field = getattr(model, field_name)
                
                # Apply search mode
                if filters.search_mode == "exact":
                    if filters.case_sensitive:
                        condition = field == search_term
                    else:
                        condition = func.lower(field) == search_term.lower()
                elif filters.search_mode == "starts_with":
                    if filters.case_sensitive:
                        condition = field.like(f"{search_term}%")
                    else:
                        condition = field.ilike(f"{search_term}%")
                elif filters.search_mode == "ends_with":
                    if filters.case_sensitive:
                        condition = field.like(f"%{search_term}")
                    else:
                        condition = field.ilike(f"%{search_term}")
                else:  # contains (default)
                    if filters.case_sensitive:
                        condition = field.like(f"%{search_term}%")
                    else:
                        condition = field.ilike(f"%{search_term}%")
                
                search_conditions.append(condition)
        
        if search_conditions:
            query = query.filter(or_(*search_conditions))
        
        return query
    
    @classmethod
    def _apply_date_filters(
        cls,
        query: Query,
        model: Type[Union[Risk, Action, Issue, Decision]],
        filters: FilterParams
    ) -> Query:
        """Apply date range filters."""
        
        # Created date filters
        if filters.created_after:
            try:
                date = datetime.fromisoformat(filters.created_after.replace('Z', '+00:00'))
                query = query.filter(model.createdAt >= date)
            except ValueError:
                pass  # Invalid date format, skip filter
        
        if filters.created_before:
            try:
                date = datetime.fromisoformat(filters.created_before.replace('Z', '+00:00'))
                query = query.filter(model.createdAt <= date)
            except ValueError:
                pass
        
        # Updated date filters
        if filters.updated_after:
            try:
                date = datetime.fromisoformat(filters.updated_after.replace('Z', '+00:00'))
                query = query.filter(model.updatedAt >= date)
            except ValueError:
                pass
        
        if filters.updated_before:
            try:
                date = datetime.fromisoformat(filters.updated_before.replace('Z', '+00:00'))
                query = query.filter(model.updatedAt <= date)
            except ValueError:
                pass
        
        # Due date filters (for Actions)
        if hasattr(model, 'dueAt'):
            if filters.due_after:
                try:
                    date = datetime.fromisoformat(filters.due_after.replace('Z', '+00:00'))
                    query = query.filter(model.dueAt >= date)
                except ValueError:
                    pass
            
            if filters.due_before:
                try:
                    date = datetime.fromisoformat(filters.due_before.replace('Z', '+00:00'))
                    query = query.filter(model.dueAt <= date)
                except ValueError:
                    pass
        
        return query
    
    @classmethod
    def _apply_numeric_filters(
        cls,
        query: Query,
        model: Type[Union[Risk, Action, Issue, Decision]],
        filters: FilterParams
    ) -> Query:
        """Apply numeric range filters."""
        
        # Score filters (for Risks)
        if hasattr(model, 'score'):
            if filters.score_min is not None:
                query = query.filter(model.score >= filters.score_min)
            if filters.score_max is not None:
                query = query.filter(model.score <= filters.score_max)
        
        # Progress filters (for Actions)
        if hasattr(model, 'progress'):
            if filters.progress_min is not None:
                query = query.filter(model.progress >= filters.progress_min)
            if filters.progress_max is not None:
                query = query.filter(model.progress <= filters.progress_max)
        
        return query
    
    @classmethod
    def _apply_sorting(
        cls,
        query: Query,
        model: Type[Union[Risk, Action, Issue, Decision]],
        filters: FilterParams
    ) -> Query:
        """Apply sorting to the query."""
        sort_field = filters.sort_by or "createdAt"
        sort_order = filters.sort_order or "desc"
        
        # Validate sort field
        sortable_fields = cls.SORTABLE_FIELDS.get(model, [])
        if sort_field not in sortable_fields:
            sort_field = "createdAt"
        
        if hasattr(model, sort_field):
            field = getattr(model, sort_field)
            if sort_order.lower() == "asc":
                query = query.order_by(field.asc())
            else:
                query = query.order_by(field.desc())
        
        return query
    
    @classmethod
    def get_filter_options(
        cls,
        db: Session,
        project_id: str,
        model: Type[Union[Risk, Action, Issue, Decision]]
    ) -> Dict[str, List[str]]:
        """Get available filter options for a model."""
        
        base_query = db.query(model).filter(model.projectId == project_id)
        
        options = {}
        
        # Get unique values for common fields
        for field in ['status', 'priority', 'category', 'owner']:
            if hasattr(model, field):
                values = base_query.with_entities(getattr(model, field)).distinct().all()
                options[field] = [v[0] for v in values if v[0] is not None]
        
        # Model-specific fields
        if hasattr(model, 'severity'):
            values = base_query.with_entities(model.severity).distinct().all()
            options['severity'] = [v[0] for v in values if v[0] is not None]
        
        if hasattr(model, 'assignee'):
            values = base_query.with_entities(model.assignee).distinct().all()
            options['assignee'] = [v[0] for v in values if v[0] is not None]
        
        if hasattr(model, 'decidedBy'):
            values = base_query.with_entities(model.decidedBy).distinct().all()
            options['decidedBy'] = [v[0] for v in values if v[0] is not None]
        
        return options
    
    @classmethod
    def get_search_suggestions(
        cls,
        db: Session,
        project_id: str,
        query_text: str,
        model: Type[Union[Risk, Action, Issue, Decision]],
        limit: int = 10
    ) -> List[SearchSuggestion]:
        """Get search suggestions based on query text."""
        suggestions = []
        
        if not query_text or len(query_text) < 2:
            return suggestions
        
        # Get field-based suggestions
        searchable_fields = cls.SEARCHABLE_FIELDS.get(model, [])
        
        for field_name in searchable_fields:
            if hasattr(model, field_name):
                field = getattr(model, field_name)
                
                # Get matching values from the field
                matches = db.query(field).filter(
                    model.projectId == project_id,
                    field.ilike(f"%{query_text}%")
                ).distinct().limit(limit).all()
                
                for match in matches:
                    if match[0] and query_text.lower() in match[0].lower():
                        suggestions.append(SearchSuggestion(
                            text=match[0],
                            type="value",
                            field=field_name
                        ))
        
        return suggestions[:limit]
    
    @classmethod
    def save_filter(
        cls,
        db: Session,
        user_id: str,
        name: str,
        filter_data: Dict[str, Any],
        description: Optional[str] = None,
        project_id: Optional[str] = None,
        is_public: bool = False
    ) -> SavedFilter:
        """Save a filter for future use."""
        
        saved_filter = SavedFilter(
            name=name,
            description=description,
            filter_data=filter_data,
            is_public=is_public,
            project_id=project_id,
            created_by=user_id
        )
        
        db.add(saved_filter)
        db.commit()
        db.refresh(saved_filter)
        
        return saved_filter
    
    @classmethod
    def get_saved_filters(
        cls,
        db: Session,
        user_id: str,
        project_id: Optional[str] = None
    ) -> List[SavedFilter]:
        """Get saved filters for a user."""
        
        query = db.query(SavedFilter).filter(
            or_(
                SavedFilter.created_by == user_id,
                SavedFilter.is_public == True
            )
        )
        
        if project_id:
            query = query.filter(
                or_(
                    SavedFilter.project_id == project_id,
                    SavedFilter.project_id.is_(None)
                )
            )
        
        return query.order_by(SavedFilter.createdAt.desc()).all()
