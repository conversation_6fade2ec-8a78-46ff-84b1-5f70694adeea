"""Notification event handlers for RAID items."""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

from ..models.user import User
from ..models.project import Project
from ..models.risk import Risk
from ..models.action import Action
from ..models.issue import Issue
from ..models.decision import Decision
from ..schemas.notification import NotificationEventType, NotificationPriority, NotificationType
from .notification_service import NotificationService

logger = logging.getLogger(__name__)


class NotificationEventHandler:
    """Handles automatic notification creation for RAID events."""

    def __init__(self, db: Session):
        self.db = db
        self.notification_service = NotificationService(db)

    def on_risk_created(self, risk: Risk, created_by: str):
        """Handle risk creation event."""
        self._notify_project_members(
            project_id=risk.projectId,
            event_type=NotificationEventType.CREATED,
            subject=f"New Risk Created: {risk.title}",
            message=f"A new risk '{risk.title}' has been created in the project.",
            entity_type="risk",
            entity_id=risk.id,
            sender_id=created_by,
            exclude_user_id=created_by,
            priority=self._get_priority_from_risk_priority(risk.priority)
        )

        # Notify assigned owner if different from creator
        if risk.owner and risk.owner != created_by:
            self._notify_user(
                user_id=risk.owner,
                event_type=NotificationEventType.ASSIGNED,
                subject=f"Risk Assigned to You: {risk.title}",
                message=f"You have been assigned to risk '{risk.title}' with priority {risk.priority}.",
                entity_type="risk",
                entity_id=risk.id,
                project_id=risk.projectId,
                sender_id=created_by,
                priority=self._get_priority_from_risk_priority(risk.priority)
            )

    def on_risk_updated(self, risk: Risk, updated_by: str, changes: Dict[str, Any]):
        """Handle risk update event."""
        # Notify owner if changed
        if "owner" in changes and changes["owner"] != updated_by:
            self._notify_user(
                user_id=changes["owner"],
                event_type=NotificationEventType.ASSIGNED,
                subject=f"Risk Assigned to You: {risk.title}",
                message=f"You have been assigned to risk '{risk.title}'.",
                entity_type="risk",
                entity_id=risk.id,
                project_id=risk.projectId,
                sender_id=updated_by,
                priority=self._get_priority_from_risk_priority(risk.priority)
            )

        # Notify on status change
        if "status" in changes:
            self._notify_project_members(
                project_id=risk.projectId,
                event_type=NotificationEventType.STATUS_CHANGED,
                subject=f"Risk Status Changed: {risk.title}",
                message=f"Risk '{risk.title}' status changed from {changes['status']} to {risk.status}.",
                entity_type="risk",
                entity_id=risk.id,
                sender_id=updated_by,
                exclude_user_id=updated_by,
                priority=NotificationPriority.NORMAL
            )

        # Notify on priority change
        if "priority" in changes:
            priority = self._get_priority_from_risk_priority(risk.priority)
            self._notify_project_members(
                project_id=risk.projectId,
                event_type=NotificationEventType.PRIORITY_CHANGED,
                subject=f"Risk Priority Changed: {risk.title}",
                message=f"Risk '{risk.title}' priority changed from {changes['priority']} to {risk.priority}.",
                entity_type="risk",
                entity_id=risk.id,
                sender_id=updated_by,
                exclude_user_id=updated_by,
                priority=priority
            )

    def on_action_created(self, action: Action, created_by: str):
        """Handle action creation event."""
        self._notify_project_members(
            project_id=action.projectId,
            event_type=NotificationEventType.CREATED,
            subject=f"New Action Created: {action.title}",
            message=f"A new action '{action.title}' has been created in the project.",
            entity_type="action",
            entity_id=action.id,
            sender_id=created_by,
            exclude_user_id=created_by,
            priority=self._get_priority_from_action_priority(action.priority)
        )

        # Notify assigned owner
        if action.assignee and action.assignee != created_by:
            self._notify_user(
                user_id=action.assignee,
                event_type=NotificationEventType.ASSIGNED,
                subject=f"Action Assigned to You: {action.title}",
                message=f"You have been assigned to action '{action.title}' with due date {action.dueDate}.",
                entity_type="action",
                entity_id=action.id,
                project_id=action.projectId,
                sender_id=created_by,
                priority=self._get_priority_from_action_priority(action.priority)
            )

    def on_action_completed(self, action: Action, completed_by: str):
        """Handle action completion event."""
        self._notify_project_members(
            project_id=action.projectId,
            event_type=NotificationEventType.COMPLETED,
            subject=f"Action Completed: {action.title}",
            message=f"Action '{action.title}' has been completed by {completed_by}.",
            entity_type="action",
            entity_id=action.id,
            sender_id=completed_by,
            exclude_user_id=completed_by,
            priority=NotificationPriority.NORMAL
        )

    def on_issue_created(self, issue: Issue, created_by: str):
        """Handle issue creation event."""
        priority = self._get_priority_from_issue_severity(issue.severity)
        
        self._notify_project_members(
            project_id=issue.projectId,
            event_type=NotificationEventType.CREATED,
            subject=f"New Issue Created: {issue.title}",
            message=f"A new {issue.severity} issue '{issue.title}' has been created.",
            entity_type="issue",
            entity_id=issue.id,
            sender_id=created_by,
            exclude_user_id=created_by,
            priority=priority
        )

        # Notify assigned owner
        if issue.assignee and issue.assignee != created_by:
            self._notify_user(
                user_id=issue.assignee,
                event_type=NotificationEventType.ASSIGNED,
                subject=f"Issue Assigned to You: {issue.title}",
                message=f"You have been assigned to {issue.severity} issue '{issue.title}'.",
                entity_type="issue",
                entity_id=issue.id,
                project_id=issue.projectId,
                sender_id=created_by,
                priority=priority
            )

    def on_decision_created(self, decision: Decision, created_by: str):
        """Handle decision creation event."""
        self._notify_project_members(
            project_id=decision.projectId,
            event_type=NotificationEventType.CREATED,
            subject=f"New Decision Made: {decision.title}",
            message=f"A new decision '{decision.title}' has been made.",
            entity_type="decision",
            entity_id=decision.id,
            sender_id=created_by,
            exclude_user_id=created_by,
            priority=NotificationPriority.NORMAL
        )

    def check_due_dates(self):
        """Check for items due soon and send notifications."""
        tomorrow = datetime.now().date() + timedelta(days=1)
        next_week = datetime.now().date() + timedelta(days=7)

        # Check actions due soon
        actions_due_soon = self.db.query(Action).filter(
            Action.dueDate.between(tomorrow, next_week),
            Action.status != "Completed"
        ).all()

        for action in actions_due_soon:
            if action.assignee:
                days_until_due = (action.dueDate - datetime.now().date()).days
                self._notify_user(
                    user_id=action.assignee,
                    event_type=NotificationEventType.DUE_SOON,
                    subject=f"Action Due Soon: {action.title}",
                    message=f"Action '{action.title}' is due in {days_until_due} days.",
                    entity_type="action",
                    entity_id=action.id,
                    project_id=action.projectId,
                    priority=NotificationPriority.HIGH
                )

    def check_overdue_items(self):
        """Check for overdue items and send notifications."""
        today = datetime.now().date()

        # Check overdue actions
        overdue_actions = self.db.query(Action).filter(
            Action.dueDate < today,
            Action.status != "Completed"
        ).all()

        for action in overdue_actions:
            if action.assignee:
                days_overdue = (today - action.dueDate).days
                self._notify_user(
                    user_id=action.assignee,
                    event_type=NotificationEventType.OVERDUE,
                    subject=f"Action Overdue: {action.title}",
                    message=f"Action '{action.title}' is {days_overdue} days overdue.",
                    entity_type="action",
                    entity_id=action.id,
                    project_id=action.projectId,
                    priority=NotificationPriority.URGENT
                )

    def _notify_project_members(
        self,
        project_id: str,
        event_type: NotificationEventType,
        subject: str,
        message: str,
        entity_type: str,
        entity_id: str,
        sender_id: str,
        exclude_user_id: Optional[str] = None,
        priority: NotificationPriority = NotificationPriority.NORMAL
    ):
        """Notify all project members."""
        # Get project members (simplified - in real implementation, you'd have project membership)
        project = self.db.query(Project).filter(Project.id == project_id).first()
        if not project:
            return

        # For now, notify all users (in real implementation, filter by project membership)
        users = self.db.query(User).filter(User.is_active == True).all()
        
        for user in users:
            if exclude_user_id and user.id == exclude_user_id:
                continue
                
            self.notification_service.create_notification(
                recipient_id=user.id,
                event_type=event_type,
                subject=subject,
                message=message,
                sender_id=sender_id,
                entity_type=entity_type,
                entity_id=entity_id,
                project_id=project_id,
                priority=priority
            )

    def _notify_user(
        self,
        user_id: str,
        event_type: NotificationEventType,
        subject: str,
        message: str,
        entity_type: str,
        entity_id: str,
        project_id: str,
        sender_id: Optional[str] = None,
        priority: NotificationPriority = NotificationPriority.NORMAL
    ):
        """Notify a specific user."""
        self.notification_service.create_notification(
            recipient_id=user_id,
            event_type=event_type,
            subject=subject,
            message=message,
            sender_id=sender_id,
            entity_type=entity_type,
            entity_id=entity_id,
            project_id=project_id,
            priority=priority
        )

    def _get_priority_from_risk_priority(self, risk_priority: str) -> NotificationPriority:
        """Convert risk priority to notification priority."""
        mapping = {
            "Low": NotificationPriority.LOW,
            "Medium": NotificationPriority.NORMAL,
            "High": NotificationPriority.HIGH,
            "Critical": NotificationPriority.URGENT
        }
        return mapping.get(risk_priority, NotificationPriority.NORMAL)

    def _get_priority_from_action_priority(self, action_priority: str) -> NotificationPriority:
        """Convert action priority to notification priority."""
        mapping = {
            "Low": NotificationPriority.LOW,
            "Medium": NotificationPriority.NORMAL,
            "High": NotificationPriority.HIGH,
            "Critical": NotificationPriority.URGENT
        }
        return mapping.get(action_priority, NotificationPriority.NORMAL)

    def _get_priority_from_issue_severity(self, issue_severity: str) -> NotificationPriority:
        """Convert issue severity to notification priority."""
        mapping = {
            "Low": NotificationPriority.LOW,
            "Medium": NotificationPriority.NORMAL,
            "High": NotificationPriority.HIGH,
            "Critical": NotificationPriority.URGENT
        }
        return mapping.get(issue_severity, NotificationPriority.NORMAL)
