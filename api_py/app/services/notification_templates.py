"""Default notification templates for RAID application."""

from sqlalchemy.orm import Session
from ..models.notification import NotificationTemplate
from ..schemas.notification import NotificationType, NotificationEventType, NotificationPriority


def create_default_templates(db: Session):
    """Create default notification templates."""
    
    templates = [
        # Risk Templates
        {
            "name": "Risk Created",
            "type": NotificationType.BOTH,
            "event_type": NotificationEventType.CREATED,
            "subject_template": "New Risk: {{ risk_title }}",
            "body_template": "A new {{ risk_priority }} priority risk '{{ risk_title }}' has been created in project {{ project_name }}.\n\nDescription: {{ risk_description }}\nOwner: {{ risk_owner }}\nStatus: {{ risk_status }}",
            "html_template": """
            <h3>New Risk Created</h3>
            <p>A new <strong>{{ risk_priority }}</strong> priority risk has been created in project <strong>{{ project_name }}</strong>.</p>
            <div style="border-left: 4px solid #ff6b6b; padding-left: 16px; margin: 16px 0;">
                <h4>{{ risk_title }}</h4>
                <p>{{ risk_description }}</p>
                <p><strong>Owner:</strong> {{ risk_owner }}</p>
                <p><strong>Status:</strong> {{ risk_status }}</p>
            </div>
            """,
            "priority": NotificationPriority.NORMAL,
            "variables": ["risk_title", "risk_description", "risk_priority", "risk_owner", "risk_status", "project_name"]
        },
        {
            "name": "Risk Assigned",
            "type": NotificationType.BOTH,
            "event_type": NotificationEventType.ASSIGNED,
            "subject_template": "Risk Assigned: {{ risk_title }}",
            "body_template": "You have been assigned to risk '{{ risk_title }}' in project {{ project_name }}.\n\nPriority: {{ risk_priority }}\nDescription: {{ risk_description }}",
            "html_template": """
            <h3>Risk Assigned to You</h3>
            <p>You have been assigned to a risk in project <strong>{{ project_name }}</strong>.</p>
            <div style="border-left: 4px solid #ffa726; padding-left: 16px; margin: 16px 0;">
                <h4>{{ risk_title }}</h4>
                <p><strong>Priority:</strong> {{ risk_priority }}</p>
                <p>{{ risk_description }}</p>
            </div>
            """,
            "priority": NotificationPriority.HIGH,
            "variables": ["risk_title", "risk_description", "risk_priority", "project_name"]
        },
        
        # Action Templates
        {
            "name": "Action Created",
            "type": NotificationType.BOTH,
            "event_type": NotificationEventType.CREATED,
            "subject_template": "New Action: {{ action_title }}",
            "body_template": "A new action '{{ action_title }}' has been created in project {{ project_name }}.\n\nDue Date: {{ action_due_date }}\nAssignee: {{ action_assignee }}\nPriority: {{ action_priority }}",
            "html_template": """
            <h3>New Action Created</h3>
            <p>A new action has been created in project <strong>{{ project_name }}</strong>.</p>
            <div style="border-left: 4px solid #4caf50; padding-left: 16px; margin: 16px 0;">
                <h4>{{ action_title }}</h4>
                <p><strong>Due Date:</strong> {{ action_due_date }}</p>
                <p><strong>Assignee:</strong> {{ action_assignee }}</p>
                <p><strong>Priority:</strong> {{ action_priority }}</p>
            </div>
            """,
            "priority": NotificationPriority.NORMAL,
            "variables": ["action_title", "action_due_date", "action_assignee", "action_priority", "project_name"]
        },
        {
            "name": "Action Assigned",
            "type": NotificationType.BOTH,
            "event_type": NotificationEventType.ASSIGNED,
            "subject_template": "Action Assigned: {{ action_title }}",
            "body_template": "You have been assigned to action '{{ action_title }}' in project {{ project_name }}.\n\nDue Date: {{ action_due_date }}\nPriority: {{ action_priority }}",
            "html_template": """
            <h3>Action Assigned to You</h3>
            <p>You have been assigned to an action in project <strong>{{ project_name }}</strong>.</p>
            <div style="border-left: 4px solid #2196f3; padding-left: 16px; margin: 16px 0;">
                <h4>{{ action_title }}</h4>
                <p><strong>Due Date:</strong> {{ action_due_date }}</p>
                <p><strong>Priority:</strong> {{ action_priority }}</p>
            </div>
            """,
            "priority": NotificationPriority.HIGH,
            "variables": ["action_title", "action_due_date", "action_priority", "project_name"]
        },
        {
            "name": "Action Due Soon",
            "type": NotificationType.BOTH,
            "event_type": NotificationEventType.DUE_SOON,
            "subject_template": "Action Due Soon: {{ action_title }}",
            "body_template": "Action '{{ action_title }}' is due in {{ days_until_due }} days.\n\nDue Date: {{ action_due_date }}\nProject: {{ project_name }}",
            "html_template": """
            <h3>Action Due Soon</h3>
            <p>An action assigned to you is due in <strong>{{ days_until_due }} days</strong>.</p>
            <div style="border-left: 4px solid #ff9800; padding-left: 16px; margin: 16px 0;">
                <h4>{{ action_title }}</h4>
                <p><strong>Due Date:</strong> {{ action_due_date }}</p>
                <p><strong>Project:</strong> {{ project_name }}</p>
            </div>
            """,
            "priority": NotificationPriority.HIGH,
            "variables": ["action_title", "action_due_date", "days_until_due", "project_name"]
        },
        {
            "name": "Action Overdue",
            "type": NotificationType.BOTH,
            "event_type": NotificationEventType.OVERDUE,
            "subject_template": "OVERDUE Action: {{ action_title }}",
            "body_template": "Action '{{ action_title }}' is {{ days_overdue }} days overdue.\n\nDue Date: {{ action_due_date }}\nProject: {{ project_name }}",
            "html_template": """
            <h3 style="color: #f44336;">Action Overdue</h3>
            <p>An action assigned to you is <strong>{{ days_overdue }} days overdue</strong>.</p>
            <div style="border-left: 4px solid #f44336; padding-left: 16px; margin: 16px 0; background-color: #ffebee;">
                <h4>{{ action_title }}</h4>
                <p><strong>Due Date:</strong> {{ action_due_date }}</p>
                <p><strong>Project:</strong> {{ project_name }}</p>
            </div>
            """,
            "priority": NotificationPriority.URGENT,
            "variables": ["action_title", "action_due_date", "days_overdue", "project_name"]
        },
        
        # Issue Templates
        {
            "name": "Issue Created",
            "type": NotificationType.BOTH,
            "event_type": NotificationEventType.CREATED,
            "subject_template": "New {{ issue_severity }} Issue: {{ issue_title }}",
            "body_template": "A new {{ issue_severity }} issue '{{ issue_title }}' has been created in project {{ project_name }}.\n\nDescription: {{ issue_description }}\nAssignee: {{ issue_assignee }}",
            "html_template": """
            <h3>New Issue Created</h3>
            <p>A new <strong>{{ issue_severity }}</strong> issue has been created in project <strong>{{ project_name }}</strong>.</p>
            <div style="border-left: 4px solid #e91e63; padding-left: 16px; margin: 16px 0;">
                <h4>{{ issue_title }}</h4>
                <p>{{ issue_description }}</p>
                <p><strong>Assignee:</strong> {{ issue_assignee }}</p>
            </div>
            """,
            "priority": NotificationPriority.HIGH,
            "variables": ["issue_title", "issue_description", "issue_severity", "issue_assignee", "project_name"]
        },
        
        # Decision Templates
        {
            "name": "Decision Made",
            "type": NotificationType.BOTH,
            "event_type": NotificationEventType.CREATED,
            "subject_template": "New Decision: {{ decision_title }}",
            "body_template": "A new decision '{{ decision_title }}' has been made in project {{ project_name }}.\n\nDecision: {{ decision_description }}\nDecided By: {{ decision_decided_by }}",
            "html_template": """
            <h3>New Decision Made</h3>
            <p>A new decision has been made in project <strong>{{ project_name }}</strong>.</p>
            <div style="border-left: 4px solid #9c27b0; padding-left: 16px; margin: 16px 0;">
                <h4>{{ decision_title }}</h4>
                <p>{{ decision_description }}</p>
                <p><strong>Decided By:</strong> {{ decision_decided_by }}</p>
            </div>
            """,
            "priority": NotificationPriority.NORMAL,
            "variables": ["decision_title", "decision_description", "decision_decided_by", "project_name"]
        },
        
        # Status Change Templates
        {
            "name": "Status Changed",
            "type": NotificationType.IN_APP,
            "event_type": NotificationEventType.STATUS_CHANGED,
            "subject_template": "{{ item_type }} Status Changed: {{ item_title }}",
            "body_template": "{{ item_type }} '{{ item_title }}' status changed from {{ old_status }} to {{ new_status }}.",
            "html_template": """
            <h3>Status Changed</h3>
            <p><strong>{{ item_type }}</strong> status has been updated.</p>
            <div style="border-left: 4px solid #607d8b; padding-left: 16px; margin: 16px 0;">
                <h4>{{ item_title }}</h4>
                <p>Status changed from <strong>{{ old_status }}</strong> to <strong>{{ new_status }}</strong></p>
            </div>
            """,
            "priority": NotificationPriority.LOW,
            "variables": ["item_type", "item_title", "old_status", "new_status"]
        }
    ]
    
    for template_data in templates:
        # Check if template already exists
        existing = db.query(NotificationTemplate).filter(
            NotificationTemplate.name == template_data["name"]
        ).first()
        
        if not existing:
            template = NotificationTemplate(**template_data)
            db.add(template)
    
    db.commit()


def get_template_variables():
    """Get available template variables for different entity types."""
    return {
        "risk": [
            "risk_title", "risk_description", "risk_priority", "risk_status", 
            "risk_owner", "risk_severity", "risk_probability", "risk_impact", "risk_score"
        ],
        "action": [
            "action_title", "action_description", "action_priority", "action_status",
            "action_assignee", "action_due_date", "action_progress", "days_until_due", "days_overdue"
        ],
        "issue": [
            "issue_title", "issue_description", "issue_severity", "issue_status",
            "issue_assignee", "issue_resolution"
        ],
        "decision": [
            "decision_title", "decision_description", "decision_status", "decision_decided_by",
            "decision_date", "decision_rationale"
        ],
        "project": [
            "project_name", "project_description", "project_status", "project_owner"
        ],
        "common": [
            "user_name", "user_email", "current_date", "current_time"
        ]
    }
