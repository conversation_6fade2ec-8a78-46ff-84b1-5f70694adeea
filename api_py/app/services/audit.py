"""Audit service for tracking changes to RAID items."""

import json
from datetime import datetime
from typing import Any, Dict, Optional, Union
from uuid import uuid4

from sqlalchemy.orm import Session
from fastapi import Request

from ..models.audit import AuditEntry
from ..models.user import User


class AuditService:
    """Service for creating and managing audit trails."""

    @staticmethod
    def create_audit_entry(
        db: Session,
        item_type: str,
        item_id: str,
        action: str,
        changed_by: str,
        old_values: Optional[Dict[str, Any]] = None,
        new_values: Optional[Dict[str, Any]] = None,
        reason: Optional[str] = None,
        request: Optional[Request] = None,
    ) -> AuditEntry:
        """
        Create an audit entry for tracking changes.
        
        Args:
            db: Database session
            item_type: Type of item (project, risk, action, issue, decision)
            item_id: ID of the item being changed
            action: Action performed (create, update, delete)
            changed_by: User ID who made the change
            old_values: Previous values (for updates)
            new_values: New values (for creates/updates)
            reason: Optional reason for the change
            request: FastAPI request object for IP/user agent
            
        Returns:
            Created AuditEntry instance
        """
        # Calculate change set
        change_set = AuditService._calculate_change_set(old_values, new_values)
        
        # Extract request metadata
        ip_address = None
        user_agent = None
        if request:
            ip_address = AuditService._get_client_ip(request)
            user_agent = request.headers.get("user-agent")

        audit_entry = AuditEntry(
            id=str(uuid4()),
            itemType=item_type,
            itemId=item_id,
            action=action,
            changeSet=change_set,
            oldValues=old_values,
            newValues=new_values,
            changedBy=changed_by,
            reason=reason,
            ip_address=ip_address,
            user_agent=user_agent,
        )

        db.add(audit_entry)
        db.commit()
        db.refresh(audit_entry)
        
        return audit_entry

    @staticmethod
    def _calculate_change_set(
        old_values: Optional[Dict[str, Any]], 
        new_values: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Calculate what fields changed between old and new values."""
        if not old_values and not new_values:
            return {}
        
        if not old_values:
            # Creation - all new values are changes
            return new_values or {}
        
        if not new_values:
            # Deletion - all old values are changes
            return {key: {"from": value, "to": None} for key, value in old_values.items()}
        
        # Update - compare values
        changes = {}
        all_keys = set(old_values.keys()) | set(new_values.keys())
        
        for key in all_keys:
            old_val = old_values.get(key)
            new_val = new_values.get(key)
            
            if old_val != new_val:
                changes[key] = {
                    "from": old_val,
                    "to": new_val
                }
        
        return changes

    @staticmethod
    def _get_client_ip(request: Request) -> Optional[str]:
        """Extract client IP address from request."""
        # Check for forwarded headers first (for reverse proxies)
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        # Fall back to direct client IP
        if hasattr(request, "client") and request.client:
            return request.client.host
        
        return None

    @staticmethod
    def audit_create(
        db: Session,
        item_type: str,
        item: Any,
        user: User,
        reason: Optional[str] = None,
        request: Optional[Request] = None,
    ) -> AuditEntry:
        """Audit a create operation."""
        # Convert item to dict for audit
        item_dict = AuditService._model_to_dict(item)
        
        return AuditService.create_audit_entry(
            db=db,
            item_type=item_type,
            item_id=str(item.id),
            action="create",
            changed_by=user.id,
            new_values=item_dict,
            reason=reason,
            request=request,
        )

    @staticmethod
    def audit_update(
        db: Session,
        item_type: str,
        item_id: str,
        old_values: Dict[str, Any],
        new_item: Any,
        user: User,
        reason: Optional[str] = None,
        request: Optional[Request] = None,
    ) -> AuditEntry:
        """Audit an update operation."""
        # Convert new item to dict for audit
        new_values = AuditService._model_to_dict(new_item)
        
        return AuditService.create_audit_entry(
            db=db,
            item_type=item_type,
            item_id=item_id,
            action="update",
            changed_by=user.id,
            old_values=old_values,
            new_values=new_values,
            reason=reason,
            request=request,
        )

    @staticmethod
    def audit_delete(
        db: Session,
        item_type: str,
        item_id: str,
        old_values: Dict[str, Any],
        user: User,
        reason: Optional[str] = None,
        request: Optional[Request] = None,
    ) -> AuditEntry:
        """Audit a delete operation."""
        return AuditService.create_audit_entry(
            db=db,
            item_type=item_type,
            item_id=item_id,
            action="delete",
            changed_by=user.id,
            old_values=old_values,
            reason=reason,
            request=request,
        )

    @staticmethod
    def _model_to_dict(model: Any) -> Dict[str, Any]:
        """Convert SQLAlchemy model to dictionary for audit."""
        if hasattr(model, "__dict__"):
            result = {}
            for key, value in model.__dict__.items():
                if not key.startswith("_"):
                    # Handle datetime objects
                    if isinstance(value, datetime):
                        result[key] = value.isoformat()
                    # Handle other JSON-serializable types
                    elif isinstance(value, (str, int, float, bool, list, dict)) or value is None:
                        result[key] = value
                    else:
                        result[key] = str(value)
            return result
        return {}

    @staticmethod
    def get_audit_history(
        db: Session,
        item_type: Optional[str] = None,
        item_id: Optional[str] = None,
        user_id: Optional[str] = None,
        action: Optional[str] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[AuditEntry]:
        """
        Get audit history with optional filters.
        
        Args:
            db: Database session
            item_type: Filter by item type
            item_id: Filter by item ID
            user_id: Filter by user who made changes
            action: Filter by action type
            limit: Maximum number of results
            offset: Number of results to skip
            
        Returns:
            List of AuditEntry instances
        """
        query = db.query(AuditEntry)
        
        if item_type:
            query = query.filter(AuditEntry.itemType == item_type)
        if item_id:
            query = query.filter(AuditEntry.itemId == item_id)
        if user_id:
            query = query.filter(AuditEntry.changedBy == user_id)
        if action:
            query = query.filter(AuditEntry.action == action)
        
        return (
            query.order_by(AuditEntry.changedAt.desc())
            .offset(offset)
            .limit(limit)
            .all()
        )
