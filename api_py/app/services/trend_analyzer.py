"""Advanced trend analysis and forecasting utilities."""

from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
import statistics
import math
from collections import defaultdict


class TrendAnalyzer:
    """Advanced trend analysis and forecasting utilities."""

    @staticmethod
    def analyze_trend_patterns(data_points: List[Tuple[datetime, float]], 
                              analysis_type: str = "comprehensive") -> Dict[str, Any]:
        """Analyze trend patterns in time series data."""
        if len(data_points) < 3:
            return {"error": "Insufficient data points for trend analysis"}
        
        # Sort data by date
        sorted_data = sorted(data_points, key=lambda x: x[0])
        dates = [point[0] for point in sorted_data]
        values = [point[1] for point in sorted_data]
        
        # Basic statistics
        mean_value = statistics.mean(values)
        median_value = statistics.median(values)
        std_dev = statistics.stdev(values) if len(values) > 1 else 0
        
        # Trend analysis
        trend_analysis = TrendAnalyzer._calculate_trend_metrics(dates, values)
        
        # Seasonality detection
        seasonality = TrendAnalyzer._detect_seasonality(dates, values)
        
        # Volatility analysis
        volatility = TrendAnalyzer._calculate_volatility(values)
        
        # Change point detection
        change_points = TrendAnalyzer._detect_change_points(values)
        
        # Outlier detection
        outliers = TrendAnalyzer._detect_outliers(values)
        
        result = {
            "basic_stats": {
                "mean": mean_value,
                "median": median_value,
                "std_dev": std_dev,
                "min": min(values),
                "max": max(values),
                "range": max(values) - min(values),
                "data_points": len(values)
            },
            "trend": trend_analysis,
            "seasonality": seasonality,
            "volatility": volatility,
            "change_points": change_points,
            "outliers": outliers
        }
        
        if analysis_type == "comprehensive":
            # Add advanced metrics
            result["advanced_metrics"] = TrendAnalyzer._calculate_advanced_metrics(dates, values)
            result["forecast"] = TrendAnalyzer._generate_forecast(dates, values)
        
        return result

    @staticmethod
    def _calculate_trend_metrics(dates: List[datetime], values: List[float]) -> Dict[str, Any]:
        """Calculate comprehensive trend metrics."""
        n = len(values)
        if n < 2:
            return {"direction": "stable", "strength": 0, "confidence": 0}
        
        # Convert dates to numeric (days since first date)
        first_date = dates[0]
        x_values = [(date - first_date).days for date in dates]
        
        # Linear regression
        sum_x = sum(x_values)
        sum_y = sum(values)
        sum_xy = sum(x * y for x, y in zip(x_values, values))
        sum_x2 = sum(x * x for x in x_values)
        sum_y2 = sum(y * y for y in values)
        
        # Calculate slope and correlation
        denominator = n * sum_x2 - sum_x * sum_x
        if denominator == 0:
            slope = 0
            correlation = 0
        else:
            slope = (n * sum_xy - sum_x * sum_y) / denominator
            
            # Correlation coefficient
            numerator = n * sum_xy - sum_x * sum_y
            denom_corr = math.sqrt((n * sum_x2 - sum_x * sum_x) * (n * sum_y2 - sum_y * sum_y))
            correlation = numerator / denom_corr if denom_corr != 0 else 0
        
        # Determine trend direction
        if slope > 0.01:
            direction = "increasing"
        elif slope < -0.01:
            direction = "decreasing"
        else:
            direction = "stable"
        
        # Calculate trend strength (normalized slope)
        value_range = max(values) - min(values)
        time_range = max(x_values) - min(x_values)
        
        if value_range > 0 and time_range > 0:
            normalized_slope = abs(slope) / (value_range / time_range)
            strength = min(normalized_slope, 1.0)
        else:
            strength = 0
        
        # R-squared as confidence measure
        r_squared = correlation ** 2
        
        return {
            "direction": direction,
            "strength": round(strength, 4),
            "confidence": round(r_squared, 4),
            "slope": round(slope, 6),
            "correlation": round(correlation, 4),
            "r_squared": round(r_squared, 4)
        }

    @staticmethod
    def _detect_seasonality(dates: List[datetime], values: List[float]) -> Dict[str, Any]:
        """Detect seasonal patterns in the data."""
        if len(values) < 14:  # Need at least 2 weeks of data
            return {"has_seasonality": False, "period": None, "strength": 0}
        
        # Check for weekly patterns
        weekly_pattern = TrendAnalyzer._check_weekly_seasonality(dates, values)
        
        # Check for monthly patterns (if enough data)
        monthly_pattern = None
        if len(values) >= 60:  # At least 2 months
            monthly_pattern = TrendAnalyzer._check_monthly_seasonality(dates, values)
        
        # Determine strongest pattern
        patterns = [p for p in [weekly_pattern, monthly_pattern] if p and p["strength"] > 0.3]
        
        if patterns:
            strongest = max(patterns, key=lambda x: x["strength"])
            return {
                "has_seasonality": True,
                "period": strongest["period"],
                "strength": strongest["strength"],
                "patterns": patterns
            }
        
        return {"has_seasonality": False, "period": None, "strength": 0}

    @staticmethod
    def _check_weekly_seasonality(dates: List[datetime], values: List[float]) -> Optional[Dict[str, Any]]:
        """Check for weekly seasonal patterns."""
        # Group by day of week
        day_groups = defaultdict(list)
        for date, value in zip(dates, values):
            day_groups[date.weekday()].append(value)
        
        # Calculate average for each day
        day_averages = {}
        for day, day_values in day_groups.items():
            if len(day_values) >= 2:  # Need at least 2 observations
                day_averages[day] = statistics.mean(day_values)
        
        if len(day_averages) < 5:  # Need most days of the week
            return None
        
        # Calculate variance between days vs within days
        overall_mean = statistics.mean(values)
        between_variance = sum((avg - overall_mean) ** 2 for avg in day_averages.values()) / len(day_averages)
        
        # Calculate within-day variance
        within_variances = []
        for day_values in day_groups.values():
            if len(day_values) > 1:
                within_variances.extend([(v - statistics.mean(day_values)) ** 2 for v in day_values])
        
        within_variance = statistics.mean(within_variances) if within_variances else 0
        
        # Seasonality strength
        total_variance = between_variance + within_variance
        strength = between_variance / total_variance if total_variance > 0 else 0
        
        return {
            "period": "weekly",
            "strength": round(strength, 4),
            "day_averages": {day: round(avg, 2) for day, avg in day_averages.items()}
        }

    @staticmethod
    def _check_monthly_seasonality(dates: List[datetime], values: List[float]) -> Optional[Dict[str, Any]]:
        """Check for monthly seasonal patterns."""
        # Group by day of month
        day_groups = defaultdict(list)
        for date, value in zip(dates, values):
            day_groups[date.day].append(value)
        
        # Calculate average for each day (only if we have enough data)
        day_averages = {}
        for day, day_values in day_groups.items():
            if len(day_values) >= 2:
                day_averages[day] = statistics.mean(day_values)
        
        if len(day_averages) < 15:  # Need at least half the month
            return None
        
        # Similar variance calculation as weekly
        overall_mean = statistics.mean(values)
        between_variance = sum((avg - overall_mean) ** 2 for avg in day_averages.values()) / len(day_averages)
        
        within_variances = []
        for day_values in day_groups.values():
            if len(day_values) > 1:
                within_variances.extend([(v - statistics.mean(day_values)) ** 2 for v in day_values])
        
        within_variance = statistics.mean(within_variances) if within_variances else 0
        total_variance = between_variance + within_variance
        strength = between_variance / total_variance if total_variance > 0 else 0
        
        return {
            "period": "monthly",
            "strength": round(strength, 4),
            "day_averages": {day: round(avg, 2) for day, avg in sorted(day_averages.items())}
        }

    @staticmethod
    def _calculate_volatility(values: List[float]) -> Dict[str, Any]:
        """Calculate volatility metrics."""
        if len(values) < 2:
            return {"coefficient": 0, "level": "stable"}
        
        mean_value = statistics.mean(values)
        std_dev = statistics.stdev(values)
        
        # Coefficient of variation
        cv = std_dev / mean_value if mean_value != 0 else float('inf')
        
        # Classify volatility level
        if cv < 0.1:
            level = "low"
        elif cv < 0.3:
            level = "moderate"
        elif cv < 0.6:
            level = "high"
        else:
            level = "very_high"
        
        return {
            "coefficient": round(cv, 4),
            "level": level,
            "std_dev": round(std_dev, 4),
            "mean": round(mean_value, 4)
        }

    @staticmethod
    def _detect_change_points(values: List[float], min_segment_length: int = 5) -> List[Dict[str, Any]]:
        """Detect significant change points in the data."""
        if len(values) < min_segment_length * 2:
            return []
        
        change_points = []
        
        # Simple change point detection using moving averages
        window_size = max(3, len(values) // 10)
        
        for i in range(window_size, len(values) - window_size):
            # Calculate means before and after potential change point
            before_mean = statistics.mean(values[i-window_size:i])
            after_mean = statistics.mean(values[i:i+window_size])
            
            # Calculate change magnitude
            change_magnitude = abs(after_mean - before_mean)
            
            # Use standard deviation to determine significance
            segment_values = values[max(0, i-window_size*2):min(len(values), i+window_size*2)]
            if len(segment_values) > 1:
                segment_std = statistics.stdev(segment_values)
                
                # Significant change if magnitude > 2 standard deviations
                if change_magnitude > 2 * segment_std:
                    change_points.append({
                        "index": i,
                        "before_mean": round(before_mean, 4),
                        "after_mean": round(after_mean, 4),
                        "change_magnitude": round(change_magnitude, 4),
                        "significance": round(change_magnitude / segment_std, 2)
                    })
        
        # Remove nearby change points (keep the most significant)
        filtered_points = []
        for point in sorted(change_points, key=lambda x: x["significance"], reverse=True):
            if not any(abs(point["index"] - fp["index"]) < min_segment_length for fp in filtered_points):
                filtered_points.append(point)
        
        return sorted(filtered_points, key=lambda x: x["index"])

    @staticmethod
    def _detect_outliers(values: List[float], method: str = "iqr") -> Dict[str, Any]:
        """Detect outliers in the data."""
        if len(values) < 4:
            return {"outlier_indices": [], "outlier_values": [], "method": method}
        
        outlier_indices = []
        
        if method == "iqr":
            # Interquartile range method
            sorted_values = sorted(values)
            n = len(sorted_values)
            q1 = sorted_values[n // 4]
            q3 = sorted_values[3 * n // 4]
            iqr = q3 - q1
            
            lower_bound = q1 - 1.5 * iqr
            upper_bound = q3 + 1.5 * iqr
            
            for i, value in enumerate(values):
                if value < lower_bound or value > upper_bound:
                    outlier_indices.append(i)
        
        elif method == "zscore":
            # Z-score method
            mean_val = statistics.mean(values)
            std_val = statistics.stdev(values) if len(values) > 1 else 0
            
            if std_val > 0:
                for i, value in enumerate(values):
                    z_score = abs(value - mean_val) / std_val
                    if z_score > 2.5:  # More than 2.5 standard deviations
                        outlier_indices.append(i)
        
        outlier_values = [values[i] for i in outlier_indices]
        
        return {
            "outlier_indices": outlier_indices,
            "outlier_values": [round(v, 4) for v in outlier_values],
            "method": method,
            "outlier_count": len(outlier_indices),
            "outlier_percentage": round(len(outlier_indices) / len(values) * 100, 2)
        }

    @staticmethod
    def _calculate_advanced_metrics(dates: List[datetime], values: List[float]) -> Dict[str, Any]:
        """Calculate advanced statistical metrics."""
        if len(values) < 3:
            return {}
        
        # Moving averages
        ma_7 = TrendAnalyzer._calculate_moving_average(values, 7)
        ma_30 = TrendAnalyzer._calculate_moving_average(values, 30) if len(values) >= 30 else None
        
        # Rate of change
        rate_of_change = []
        for i in range(1, len(values)):
            if values[i-1] != 0:
                roc = (values[i] - values[i-1]) / values[i-1] * 100
                rate_of_change.append(roc)
        
        # Momentum (rate of change of rate of change)
        momentum = []
        for i in range(1, len(rate_of_change)):
            momentum.append(rate_of_change[i] - rate_of_change[i-1])
        
        return {
            "moving_averages": {
                "ma_7": [round(v, 4) for v in ma_7] if ma_7 else None,
                "ma_30": [round(v, 4) for v in ma_30] if ma_30 else None
            },
            "rate_of_change": {
                "values": [round(v, 4) for v in rate_of_change],
                "mean": round(statistics.mean(rate_of_change), 4) if rate_of_change else 0,
                "std": round(statistics.stdev(rate_of_change), 4) if len(rate_of_change) > 1 else 0
            },
            "momentum": {
                "values": [round(v, 4) for v in momentum],
                "mean": round(statistics.mean(momentum), 4) if momentum else 0
            }
        }

    @staticmethod
    def _calculate_moving_average(values: List[float], window: int) -> Optional[List[float]]:
        """Calculate moving average with specified window."""
        if len(values) < window:
            return None
        
        moving_avg = []
        for i in range(window - 1, len(values)):
            avg = sum(values[i - window + 1:i + 1]) / window
            moving_avg.append(avg)
        
        return moving_avg

    @staticmethod
    def _generate_forecast(dates: List[datetime], values: List[float], 
                          periods: int = 7) -> Dict[str, Any]:
        """Generate simple forecast based on trend analysis."""
        if len(values) < 3:
            return {"error": "Insufficient data for forecasting"}
        
        # Use linear regression for simple forecasting
        first_date = dates[0]
        x_values = [(date - first_date).days for date in dates]
        
        # Calculate linear regression parameters
        n = len(values)
        sum_x = sum(x_values)
        sum_y = sum(values)
        sum_xy = sum(x * y for x, y in zip(x_values, values))
        sum_x2 = sum(x * x for x in x_values)
        
        denominator = n * sum_x2 - sum_x * sum_x
        if denominator == 0:
            return {"error": "Cannot generate forecast - insufficient variance in data"}
        
        slope = (n * sum_xy - sum_x * sum_y) / denominator
        intercept = (sum_y - slope * sum_x) / n
        
        # Generate forecast
        last_x = x_values[-1]
        forecast_points = []
        
        for i in range(1, periods + 1):
            forecast_x = last_x + i
            forecast_y = slope * forecast_x + intercept
            forecast_date = dates[-1] + timedelta(days=i)
            
            forecast_points.append({
                "date": forecast_date.isoformat(),
                "value": round(forecast_y, 4),
                "confidence": max(0, 1 - (i * 0.1))  # Decreasing confidence
            })
        
        return {
            "forecast_points": forecast_points,
            "method": "linear_regression",
            "parameters": {
                "slope": round(slope, 6),
                "intercept": round(intercept, 4)
            }
        }
