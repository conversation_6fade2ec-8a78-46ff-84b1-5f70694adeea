"""Application configuration settings."""

import os
from typing import List, Optional
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    # Application
    app_name: str = "RAID API"
    app_version: str = "1.0.0"
    debug: bool = False
    
    # Server
    host: str = "0.0.0.0"
    port: int = 8000
    
    # Database
    database_url: str = "sqlite:///./raid.db"
    
    # CORS
    cors_origins: List[str] = ["http://localhost:3000", "http://localhost:3001"]
    cors_allow_credentials: bool = True
    cors_allow_methods: List[str] = ["*"]
    cors_allow_headers: List[str] = ["*"]
    
    # Authentication
    secret_key: str = "your-secret-key-change-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # Auth providers
    auth_issuer: Optional[str] = None
    auth_jwks_uri: Optional[str] = None
    auth_audience: Optional[str] = None
    
    # Notifications
    slack_webhook_url: Optional[str] = None
    teams_webhook_url: Optional[str] = None
    high_score_threshold: float = 3.0
    
    # Frontend
    frontend_base_url: str = "http://localhost:3000"
    
    # Scheduling
    weekly_summary_cron: str = "0 9 * * MON"  # Monday 9 AM
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()
