"""Main FastAPI application with comprehensive setup."""

import logging
from datetime import datetime
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.exceptions import RequestValidationError
from fastapi import HTTPException

from .config import settings
from .db import create_tables
from .exceptions import (
    RAIDException,
    raid_exception_handler,
    validation_exception_handler,
    http_exception_handler,
    general_exception_handler
)
from .routers.projects import router as projects_router
from .routers.raid import router as raid_router
from .routers.auth import router as auth_router
from .schemas.common import HealthCheck

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="A comprehensive RAID (Risks, Actions, Issues, Decisions) management API",
    debug=settings.debug
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=settings.cors_allow_credentials,
    allow_methods=settings.cors_allow_methods,
    allow_headers=settings.cors_allow_headers,
)

# Add exception handlers
app.add_exception_handler(RAIDException, raid_exception_handler)
app.add_exception_handler(RequestValidationError, validation_exception_handler)
app.add_exception_handler(HTTPException, http_exception_handler)
app.add_exception_handler(Exception, general_exception_handler)

# Add request logging middleware
@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = datetime.now()

    # Log request
    logger.info(f"Request: {request.method} {request.url}")

    response = await call_next(request)

    # Log response
    process_time = (datetime.now() - start_time).total_seconds()
    logger.info(f"Response: {response.status_code} - {process_time:.3f}s")

    return response


@app.on_event("startup")
async def startup_event():
    """Initialize application on startup."""
    logger.info("Starting RAID API...")

    # Create database tables
    create_tables()
    logger.info("Database tables created/verified")

    logger.info(f"RAID API started on {settings.host}:{settings.port}")


@app.get("/health", response_model=HealthCheck, tags=["Health"])
async def health_check():
    """Health check endpoint."""
    return HealthCheck(
        status="ok",
        timestamp=datetime.now().isoformat(),
        version=settings.app_version,
        database="ok"
    )


# Include routers
app.include_router(auth_router, prefix="/api/v1")
app.include_router(projects_router, prefix="/api/v1")
app.include_router(raid_router, prefix="/api/v1")

# Import and include audit router
from .routers.audit import router as audit_router
app.include_router(audit_router, prefix="/api/v1")


