from typing import Optional
from pydantic import BaseModel


class RiskBase(BaseModel):
    projectId: str
    title: str
    description: Optional[str] = None
    owner: Optional[str] = None
    status: str = "Open"
    priority: Optional[str] = None
    dueAt: Optional[str] = None
    likelihood: Optional[int] = None
    impact: Optional[int] = None
    score: Optional[int] = None
    severity: Optional[str] = None
    rationale: Optional[str] = None


class RiskCreate(RiskBase):
    pass


class RiskOut(RiskBase):
    id: str

    class Config:
        from_attributes = True


