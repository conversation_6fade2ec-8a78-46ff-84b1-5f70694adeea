"""User schemas for authentication and authorization."""

from typing import Optional
from datetime import datetime
from pydantic import BaseModel, Field, EmailStr, validator


class UserBase(BaseModel):
    """Base user schema with common fields."""
    email: Optional[EmailStr] = Field(None, description="User email")
    name: Optional[str] = Field(None, max_length=100, description="User full name")
    username: Optional[str] = Field(None, max_length=50, description="Username")
    role: str = Field("viewer", description="User role")
    is_active: bool = Field(True, description="User active status")
    timezone: str = Field("UTC", description="User timezone")

    @validator('role')
    def validate_role(cls, v):
        allowed = ['viewer', 'contributor', 'pm', 'admin']
        if v not in allowed:
            raise ValueError(f'Role must be one of: {allowed}')
        return v


class UserCreate(UserBase):
    """Schema for creating a new user."""
    id: str = Field(..., description="User ID from auth provider")


class UserUpdate(BaseModel):
    """Schema for updating a user."""
    email: Optional[EmailStr] = None
    name: Optional[str] = Field(None, max_length=100)
    username: Optional[str] = Field(None, max_length=50)
    role: Optional[str] = None
    is_active: Optional[bool] = None
    timezone: Optional[str] = None
    avatar_url: Optional[str] = None
    preferences: Optional[str] = None


class UserOut(UserBase):
    """Schema for user output."""
    id: str
    is_verified: bool
    avatar_url: Optional[str] = None
    createdAt: datetime
    updatedAt: datetime
    lastLoginAt: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class UserProfile(UserOut):
    """Extended user profile with preferences."""
    preferences: Optional[str] = None
    
    class Config:
        from_attributes = True
