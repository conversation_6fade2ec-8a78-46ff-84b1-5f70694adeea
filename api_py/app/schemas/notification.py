"""Notification schemas for API serialization."""

from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum


class NotificationType(str, Enum):
    """Notification delivery types."""
    EMAIL = "email"
    IN_APP = "in_app"
    BOTH = "both"


class NotificationEventType(str, Enum):
    """Notification event types."""
    CREATED = "created"
    UPDATED = "updated"
    ASSIGNED = "assigned"
    DUE_SOON = "due_soon"
    OVERDUE = "overdue"
    STATUS_CHANGED = "status_changed"
    PRIORITY_CHANGED = "priority_changed"
    COMPLETED = "completed"
    ESCALATED = "escalated"
    COMMENT_ADDED = "comment_added"
    MENTIONED = "mentioned"


class NotificationStatus(str, Enum):
    """Notification status values."""
    PENDING = "pending"
    SENT = "sent"
    FAILED = "failed"
    READ = "read"


class NotificationPriority(str, Enum):
    """Notification priority levels."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class EntityType(str, Enum):
    """Entity types for notifications."""
    RISK = "risk"
    ACTION = "action"
    ISSUE = "issue"
    DECISION = "decision"
    PROJECT = "project"


# Notification Template Schemas
class NotificationTemplateBase(BaseModel):
    """Base notification template schema."""
    name: str = Field(..., max_length=100)
    type: NotificationType
    event_type: NotificationEventType
    subject_template: str = Field(..., max_length=200)
    body_template: str
    html_template: Optional[str] = None
    is_active: bool = True
    priority: NotificationPriority = NotificationPriority.NORMAL
    variables: List[str] = Field(default_factory=list)
    description: Optional[str] = None


class NotificationTemplateCreate(NotificationTemplateBase):
    """Schema for creating notification templates."""
    pass


class NotificationTemplateUpdate(BaseModel):
    """Schema for updating notification templates."""
    name: Optional[str] = Field(None, max_length=100)
    type: Optional[NotificationType] = None
    event_type: Optional[NotificationEventType] = None
    subject_template: Optional[str] = Field(None, max_length=200)
    body_template: Optional[str] = None
    html_template: Optional[str] = None
    is_active: Optional[bool] = None
    priority: Optional[NotificationPriority] = None
    variables: Optional[List[str]] = None
    description: Optional[str] = None


class NotificationTemplateOut(NotificationTemplateBase):
    """Schema for notification template output."""
    id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Notification Schemas
class NotificationBase(BaseModel):
    """Base notification schema."""
    type: NotificationType
    event_type: NotificationEventType
    subject: str = Field(..., max_length=200)
    message: str
    html_content: Optional[str] = None
    entity_type: Optional[EntityType] = None
    entity_id: Optional[str] = None
    project_id: Optional[str] = None
    priority: NotificationPriority = NotificationPriority.NORMAL
    notification_metadata: Dict[str, Any] = Field(default_factory=dict)


class NotificationCreate(NotificationBase):
    """Schema for creating notifications."""
    recipient_id: str
    sender_id: Optional[str] = None


class NotificationUpdate(BaseModel):
    """Schema for updating notifications."""
    status: Optional[NotificationStatus] = None
    read_at: Optional[datetime] = None


class NotificationOut(NotificationBase):
    """Schema for notification output."""
    id: str
    recipient_id: str
    sender_id: Optional[str] = None
    status: NotificationStatus
    sent_at: Optional[datetime] = None
    read_at: Optional[datetime] = None
    failed_at: Optional[datetime] = None
    failure_reason: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Notification Preference Schemas
class NotificationPreferenceBase(BaseModel):
    """Base notification preference schema."""
    event_type: NotificationEventType
    email_enabled: bool = True
    in_app_enabled: bool = True
    immediate: bool = True
    daily_digest: bool = False
    weekly_digest: bool = False
    project_id: Optional[str] = None
    priority_threshold: NotificationPriority = NotificationPriority.LOW


class NotificationPreferenceCreate(NotificationPreferenceBase):
    """Schema for creating notification preferences."""
    user_id: str


class NotificationPreferenceUpdate(BaseModel):
    """Schema for updating notification preferences."""
    email_enabled: Optional[bool] = None
    in_app_enabled: Optional[bool] = None
    immediate: Optional[bool] = None
    daily_digest: Optional[bool] = None
    weekly_digest: Optional[bool] = None
    priority_threshold: Optional[NotificationPriority] = None


class NotificationPreferenceOut(NotificationPreferenceBase):
    """Schema for notification preference output."""
    id: str
    user_id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Bulk Operations
class BulkNotificationCreate(BaseModel):
    """Schema for creating multiple notifications."""
    template_id: Optional[str] = None
    recipient_ids: List[str]
    sender_id: Optional[str] = None
    entity_type: Optional[EntityType] = None
    entity_id: Optional[str] = None
    project_id: Optional[str] = None
    variables: Dict[str, Any] = Field(default_factory=dict)
    priority: NotificationPriority = NotificationPriority.NORMAL
    scheduled_for: Optional[datetime] = None


class NotificationStats(BaseModel):
    """Schema for notification statistics."""
    total_notifications: int
    unread_count: int
    pending_count: int
    sent_count: int
    failed_count: int
    by_type: Dict[str, int]
    by_priority: Dict[str, int]
    recent_notifications: List[NotificationOut]


# Email Configuration
class EmailConfig(BaseModel):
    """Email configuration schema."""
    smtp_host: str
    smtp_port: int = 587
    smtp_username: str
    smtp_password: str
    use_tls: bool = True
    from_email: str
    from_name: str = "RAID System"


# Notification Settings
class NotificationSettings(BaseModel):
    """Global notification settings."""
    email_enabled: bool = True
    batch_size: int = 100
    retry_attempts: int = 3
    retry_delay_minutes: int = 5
    digest_time_hour: int = 9  # 9 AM
    cleanup_days: int = 30  # Delete old notifications after 30 days
