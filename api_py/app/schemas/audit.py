"""Audit schemas for API responses."""

from datetime import datetime
from typing import Any, Dict, Optional
from pydantic import BaseModel


class AuditEntryOut(BaseModel):
    """Schema for audit entry output."""
    
    id: str
    itemType: str
    itemId: str
    action: str
    changeSet: Dict[str, Any]
    oldValues: Optional[Dict[str, Any]] = None
    newValues: Optional[Dict[str, Any]] = None
    changedBy: str
    changedAt: datetime
    reason: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None

    class Config:
        from_attributes = True


class AuditSummary(BaseModel):
    """Schema for audit summary statistics."""
    
    total_changes: int
    changes_by_type: Dict[str, int]
    changes_by_action: Dict[str, int]
    recent_activity: int  # Changes in last 24 hours
    most_active_users: list[Dict[str, Any]]

    class Config:
        from_attributes = True
