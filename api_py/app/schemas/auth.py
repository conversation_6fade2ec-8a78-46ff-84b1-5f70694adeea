"""Authentication and authorization schemas."""

from pydantic import BaseModel, EmailStr, Field
from typing import Optional


class UserLogin(BaseModel):
    """Schema for user login."""
    email: EmailStr = Field(..., description="User email address")
    password: str = Field(..., min_length=6, description="User password")


class UserRegister(BaseModel):
    """Schema for user registration."""
    email: EmailStr = Field(..., description="User email address")
    password: str = Field(..., min_length=6, description="User password")
    full_name: str = Field(..., min_length=1, max_length=100, description="User full name")
    role: Optional[str] = Field("viewer", description="User role")

    class Config:
        schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "password": "securepassword123",
                "full_name": "<PERSON>",
                "role": "viewer"
            }
        }


class Token(BaseModel):
    """Schema for JWT token response."""
    access_token: str = Field(..., description="JWT access token")
    token_type: str = Field("bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiration time in seconds")


class TokenData(BaseModel):
    """Schema for token data."""
    user_id: Optional[str] = None


class PasswordChange(BaseModel):
    """Schema for password change."""
    current_password: str = Field(..., min_length=6, description="Current password")
    new_password: str = Field(..., min_length=6, description="New password")


class PasswordReset(BaseModel):
    """Schema for password reset."""
    email: EmailStr = Field(..., description="User email address")


class PasswordResetConfirm(BaseModel):
    """Schema for password reset confirmation."""
    token: str = Field(..., description="Password reset token")
    new_password: str = Field(..., min_length=6, description="New password")


class UserProfile(BaseModel):
    """Schema for user profile update."""
    full_name: Optional[str] = Field(None, min_length=1, max_length=100, description="User full name")
    bio: Optional[str] = Field(None, max_length=500, description="User bio")
    avatar_url: Optional[str] = Field(None, description="User avatar URL")

    class Config:
        schema_extra = {
            "example": {
                "full_name": "John Doe",
                "bio": "Senior Project Manager",
                "avatar_url": "https://example.com/avatar.jpg"
            }
        }


class RoleUpdate(BaseModel):
    """Schema for updating user role (admin only)."""
    role: str = Field(..., description="New user role")

    class Config:
        schema_extra = {
            "example": {
                "role": "pm"
            }
        }


class UserStatus(BaseModel):
    """Schema for updating user status (admin only)."""
    is_active: bool = Field(..., description="User active status")

    class Config:
        schema_extra = {
            "example": {
                "is_active": True
            }
        }
