from typing import Optional
from pydantic import BaseModel


class ActionBase(BaseModel):
    projectId: str
    title: str
    description: Optional[str] = None
    owner: Optional[str] = None
    status: str = "Open"
    priority: Optional[str] = None
    dueAt: Optional[str] = None


class ActionCreate(ActionBase):
    pass


class ActionOut(ActionBase):
    id: str

    class Config:
        from_attributes = True


