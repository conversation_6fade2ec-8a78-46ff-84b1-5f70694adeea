"""Common schemas for API responses and requests."""

from typing import Any, Dict, List, Optional, Generic, TypeVar
from pydantic import BaseModel, Field

T = TypeVar('T')


class ErrorDetail(BaseModel):
    """Error detail schema."""
    type: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    field: Optional[str] = Field(None, description="Field that caused the error")


class ErrorResponse(BaseModel):
    """Standard error response schema."""
    success: bool = Field(False, description="Success status")
    error: str = Field(..., description="Error message")
    details: Optional[List[ErrorDetail]] = Field(None, description="Error details")
    code: Optional[str] = Field(None, description="Error code")


class SuccessResponse(BaseModel, Generic[T]):
    """Standard success response schema."""
    success: bool = Field(True, description="Success status")
    data: T = Field(..., description="Response data")
    message: Optional[str] = Field(None, description="Success message")


class PaginationParams(BaseModel):
    """Pagination parameters."""
    page: int = Field(1, ge=1, description="Page number")
    size: int = Field(20, ge=1, le=100, description="Page size")
    sort_by: Optional[str] = Field(None, description="Sort field")
    sort_order: str = Field("asc", description="Sort order")
    
    def get_offset(self) -> int:
        """Calculate offset for database queries."""
        return (self.page - 1) * self.size


class PaginatedResponse(BaseModel, Generic[T]):
    """Paginated response schema."""
    items: List[T] = Field(..., description="List of items")
    total: int = Field(..., description="Total number of items")
    page: int = Field(..., description="Current page")
    size: int = Field(..., description="Page size")
    pages: int = Field(..., description="Total number of pages")
    has_next: bool = Field(..., description="Has next page")
    has_prev: bool = Field(..., description="Has previous page")


class FilterParams(BaseModel):
    """Common filter parameters for RAID items."""
    status: Optional[List[str]] = Field(None, description="Filter by status (multiple values)")
    owner: Optional[List[str]] = Field(None, description="Filter by owner (multiple values)")
    priority: Optional[List[str]] = Field(None, description="Filter by priority (multiple values)")
    category: Optional[List[str]] = Field(None, description="Filter by category (multiple values)")
    severity: Optional[List[str]] = Field(None, description="Filter by severity (multiple values)")
    q: Optional[str] = Field(None, description="Search query")
    tags: Optional[List[str]] = Field(None, description="Filter by tags")
    created_after: Optional[str] = Field(None, description="Created after date (ISO format)")
    created_before: Optional[str] = Field(None, description="Created before date (ISO format)")
    updated_after: Optional[str] = Field(None, description="Updated after date (ISO format)")
    updated_before: Optional[str] = Field(None, description="Updated before date (ISO format)")
    due_after: Optional[str] = Field(None, description="Due after date (ISO format)")
    due_before: Optional[str] = Field(None, description="Due before date (ISO format)")
    score_min: Optional[int] = Field(None, ge=0, le=100, description="Minimum score")
    score_max: Optional[int] = Field(None, ge=0, le=100, description="Maximum score")
    progress_min: Optional[int] = Field(None, ge=0, le=100, description="Minimum progress")
    progress_max: Optional[int] = Field(None, ge=0, le=100, description="Maximum progress")
    assignee: Optional[List[str]] = Field(None, description="Filter by assignee (multiple values)")
    decided_by: Optional[List[str]] = Field(None, description="Filter by decided_by (multiple values)")

    # Advanced search options
    search_fields: Optional[List[str]] = Field(None, description="Fields to search in")
    search_mode: str = Field("contains", description="Search mode: contains, exact, starts_with, ends_with")
    case_sensitive: bool = Field(False, description="Case sensitive search")

    # Sorting
    sort_by: Optional[str] = Field("createdAt", description="Sort field")
    sort_order: str = Field("desc", description="Sort order: asc, desc")

    # Pagination
    limit: int = Field(50, ge=1, le=1000, description="Maximum number of results")
    offset: int = Field(0, ge=0, description="Number of results to skip")


class HealthCheck(BaseModel):
    """Health check response."""
    status: str = Field("ok", description="Service status")
    timestamp: str = Field(..., description="Check timestamp")
    version: str = Field("1.0.0", description="API version")
    database: str = Field("ok", description="Database status")


class BulkOperation(BaseModel):
    """Bulk operation request."""
    action: str = Field(..., description="Bulk action type")
    ids: List[str] = Field(..., description="List of item IDs")
    data: Optional[Dict[str, Any]] = Field(None, description="Additional data for operation")


class SavedFilterCreate(BaseModel):
    """Schema for creating a saved filter."""
    name: str = Field(..., min_length=1, max_length=100, description="Filter name")
    description: Optional[str] = Field(None, max_length=500, description="Filter description")
    filter_data: Dict[str, Any] = Field(..., description="Filter parameters")
    is_public: bool = Field(False, description="Whether filter is public")
    project_id: Optional[str] = Field(None, description="Project ID if filter is project-specific")


class SavedFilterUpdate(BaseModel):
    """Schema for updating a saved filter."""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="Filter name")
    description: Optional[str] = Field(None, max_length=500, description="Filter description")
    filter_data: Optional[Dict[str, Any]] = Field(None, description="Filter parameters")
    is_public: Optional[bool] = Field(None, description="Whether filter is public")


class SavedFilterOut(BaseModel):
    """Schema for saved filter output."""
    id: str = Field(..., description="Filter ID")
    name: str = Field(..., description="Filter name")
    description: Optional[str] = Field(None, description="Filter description")
    filter_data: Dict[str, Any] = Field(..., description="Filter parameters")
    is_public: bool = Field(..., description="Whether filter is public")
    project_id: Optional[str] = Field(None, description="Project ID if filter is project-specific")
    created_by: str = Field(..., description="User who created the filter")
    created_at: str = Field(..., description="Creation timestamp")
    updated_at: str = Field(..., description="Last update timestamp")

    class Config:
        from_attributes = True


class SearchSuggestion(BaseModel):
    """Schema for search suggestions."""
    text: str = Field(..., description="Suggestion text")
    type: str = Field(..., description="Suggestion type: field, value, recent")
    field: Optional[str] = Field(None, description="Field name for value suggestions")
    count: Optional[int] = Field(None, description="Number of items matching this suggestion")


class SearchSuggestionsResponse(BaseModel):
    """Schema for search suggestions response."""
    suggestions: List[SearchSuggestion] = Field(..., description="List of suggestions")
    recent_searches: List[str] = Field(..., description="Recent search queries")


class FilterOptionsResponse(BaseModel):
    """Schema for filter options response."""
    statuses: List[str] = Field(..., description="Available status values")
    priorities: List[str] = Field(..., description="Available priority values")
    categories: List[str] = Field(..., description="Available category values")
    severities: List[str] = Field(..., description="Available severity values")
    owners: List[str] = Field(..., description="Available owner values")
    assignees: List[str] = Field(..., description="Available assignee values")
    tags: List[str] = Field(..., description="Available tag values")


class PaginatedResponse(BaseModel, Generic[T]):
    """Schema for paginated responses."""
    items: List[T] = Field(..., description="List of items")
    total: int = Field(..., description="Total number of items")
    offset: int = Field(..., description="Current offset")
    limit: int = Field(..., description="Items per page")
    has_next: bool = Field(..., description="Whether there are more items")
    has_prev: bool = Field(..., description="Whether there are previous items")

    @classmethod
    def create(cls, items: List[T], total: int, offset: int, limit: int):
        """Create a paginated response."""
        return cls(
            items=items,
            total=total,
            offset=offset,
            limit=limit,
            has_next=offset + limit < total,
            has_prev=offset > 0
        )


class BulkOperationResult(BaseModel):
    """Bulk operation result."""
    success: bool = Field(..., description="Operation success")
    processed: int = Field(..., description="Number of items processed")
    failed: int = Field(..., description="Number of items failed")
    errors: List[ErrorDetail] = Field(default_factory=list, description="List of errors")
