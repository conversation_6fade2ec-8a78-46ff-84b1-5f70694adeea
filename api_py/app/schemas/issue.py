from typing import Optional
from pydantic import BaseModel


class IssueBase(BaseModel):
    projectId: str
    title: str
    description: Optional[str] = None
    owner: Optional[str] = None
    status: str = "Open"
    priority: Optional[str] = None
    dueAt: Optional[str] = None
    severity: Optional[str] = None


class IssueCreate(IssueBase):
    pass


class IssueOut(IssueBase):
    id: str

    class Config:
        from_attributes = True


