[tool:pytest]
minversion = 6.0
addopts = -ra -q --strict-markers --strict-config
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow tests
    auth: Authentication tests
    database: Database tests
    api: API endpoint tests
    audit: Audit functionality tests
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
