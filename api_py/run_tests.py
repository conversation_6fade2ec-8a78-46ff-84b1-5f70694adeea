#!/usr/bin/env python3
"""Test runner script for RAID API."""

import argparse
import sys
import subprocess
from pathlib import Path


def run_command(cmd: list[str], description: str) -> bool:
    """Run a command and return success status."""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {' '.join(cmd)}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        print(f"✅ {description} completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed with exit code {e.returncode}")
        return False


def run_unit_tests(coverage: bool = False, verbose: bool = False) -> bool:
    """Run unit tests."""
    cmd = ["python", "-m", "pytest", "tests/unit/", "-m", "unit"]
    
    if coverage:
        cmd.extend(["--cov=app", "--cov-report=html", "--cov-report=term"])
    
    if verbose:
        cmd.append("-v")
    
    cmd.extend(["--tb=short"])
    
    return run_command(cmd, "Unit Tests")


def run_integration_tests(verbose: bool = False) -> bool:
    """Run integration tests."""
    cmd = ["python", "-m", "pytest", "tests/integration/", "-m", "integration"]
    
    if verbose:
        cmd.append("-v")
    
    cmd.extend(["--tb=short"])
    
    return run_command(cmd, "Integration Tests")


def run_auth_tests(verbose: bool = False) -> bool:
    """Run authentication tests."""
    cmd = ["python", "-m", "pytest", "-m", "auth"]
    
    if verbose:
        cmd.append("-v")
    
    cmd.extend(["--tb=short"])
    
    return run_command(cmd, "Authentication Tests")


def run_audit_tests(verbose: bool = False) -> bool:
    """Run audit tests."""
    cmd = ["python", "-m", "pytest", "-m", "audit"]
    
    if verbose:
        cmd.append("-v")
    
    cmd.extend(["--tb=short"])
    
    return run_command(cmd, "Audit Tests")


def run_api_tests(verbose: bool = False) -> bool:
    """Run API tests."""
    cmd = ["python", "-m", "pytest", "-m", "api"]
    
    if verbose:
        cmd.append("-v")
    
    cmd.extend(["--tb=short"])
    
    return run_command(cmd, "API Tests")


def run_slow_tests(verbose: bool = False) -> bool:
    """Run slow tests."""
    cmd = ["python", "-m", "pytest", "-m", "slow"]
    
    if verbose:
        cmd.append("-v")
    
    cmd.extend(["--tb=short"])
    
    return run_command(cmd, "Slow Tests")


def run_all_tests(coverage: bool = False, verbose: bool = False, parallel: bool = False) -> bool:
    """Run all tests."""
    cmd = ["python", "-m", "pytest", "tests/"]
    
    if coverage:
        cmd.extend(["--cov=app", "--cov-report=html", "--cov-report=term", "--cov-report=xml"])
    
    if verbose:
        cmd.append("-v")
    
    if parallel:
        cmd.extend(["-n", "auto"])
    
    cmd.extend(["--tb=short"])
    
    return run_command(cmd, "All Tests")


def run_linting() -> bool:
    """Run code linting."""
    success = True
    
    # Run flake8
    if not run_command(["python", "-m", "flake8", "app/", "tests/"], "Flake8 Linting"):
        success = False
    
    # Run black check
    if not run_command(["python", "-m", "black", "--check", "app/", "tests/"], "Black Formatting Check"):
        success = False
    
    # Run isort check
    if not run_command(["python", "-m", "isort", "--check-only", "app/", "tests/"], "Import Sorting Check"):
        success = False
    
    return success


def format_code() -> bool:
    """Format code with black and isort."""
    success = True
    
    # Run black
    if not run_command(["python", "-m", "black", "app/", "tests/"], "Black Formatting"):
        success = False
    
    # Run isort
    if not run_command(["python", "-m", "isort", "app/", "tests/"], "Import Sorting"):
        success = False
    
    return success


def generate_coverage_report() -> bool:
    """Generate coverage report."""
    return run_command(
        ["python", "-m", "pytest", "tests/", "--cov=app", "--cov-report=html", "--cov-report=term"],
        "Coverage Report Generation"
    )


def main():
    """Main CLI interface."""
    parser = argparse.ArgumentParser(description="RAID API Test Runner")
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Test commands
    unit_parser = subparsers.add_parser("unit", help="Run unit tests")
    unit_parser.add_argument("--coverage", action="store_true", help="Generate coverage report")
    unit_parser.add_argument("-v", "--verbose", action="store_true", help="Verbose output")
    
    integration_parser = subparsers.add_parser("integration", help="Run integration tests")
    integration_parser.add_argument("-v", "--verbose", action="store_true", help="Verbose output")
    
    auth_parser = subparsers.add_parser("auth", help="Run authentication tests")
    auth_parser.add_argument("-v", "--verbose", action="store_true", help="Verbose output")
    
    audit_parser = subparsers.add_parser("audit", help="Run audit tests")
    audit_parser.add_argument("-v", "--verbose", action="store_true", help="Verbose output")
    
    api_parser = subparsers.add_parser("api", help="Run API tests")
    api_parser.add_argument("-v", "--verbose", action="store_true", help="Verbose output")
    
    slow_parser = subparsers.add_parser("slow", help="Run slow tests")
    slow_parser.add_argument("-v", "--verbose", action="store_true", help="Verbose output")
    
    all_parser = subparsers.add_parser("all", help="Run all tests")
    all_parser.add_argument("--coverage", action="store_true", help="Generate coverage report")
    all_parser.add_argument("-v", "--verbose", action="store_true", help="Verbose output")
    all_parser.add_argument("-n", "--parallel", action="store_true", help="Run tests in parallel")
    
    # Code quality commands
    subparsers.add_parser("lint", help="Run code linting")
    subparsers.add_parser("format", help="Format code")
    subparsers.add_parser("coverage", help="Generate coverage report")
    
    # CI command
    ci_parser = subparsers.add_parser("ci", help="Run full CI pipeline")
    ci_parser.add_argument("--no-lint", action="store_true", help="Skip linting")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # Change to script directory
    script_dir = Path(__file__).parent
    import os
    os.chdir(script_dir)
    
    success = True
    
    if args.command == "unit":
        success = run_unit_tests(coverage=args.coverage, verbose=args.verbose)
    elif args.command == "integration":
        success = run_integration_tests(verbose=args.verbose)
    elif args.command == "auth":
        success = run_auth_tests(verbose=args.verbose)
    elif args.command == "audit":
        success = run_audit_tests(verbose=args.verbose)
    elif args.command == "api":
        success = run_api_tests(verbose=args.verbose)
    elif args.command == "slow":
        success = run_slow_tests(verbose=args.verbose)
    elif args.command == "all":
        success = run_all_tests(coverage=args.coverage, verbose=args.verbose, parallel=args.parallel)
    elif args.command == "lint":
        success = run_linting()
    elif args.command == "format":
        success = format_code()
    elif args.command == "coverage":
        success = generate_coverage_report()
    elif args.command == "ci":
        # Run full CI pipeline
        if not args.no_lint:
            success = run_linting()
        
        if success:
            success = run_all_tests(coverage=True, parallel=True)
    
    if success:
        print(f"\n🎉 {args.command.title()} completed successfully!")
        sys.exit(0)
    else:
        print(f"\n💥 {args.command.title()} failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
