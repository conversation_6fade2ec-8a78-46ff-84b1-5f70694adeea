#!/usr/bin/env python3
"""Database management script for RAID API."""

import argparse
import sys
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from alembic.config import Config
from alembic import command
from sqlalchemy import text

from app.db import engine, SessionLocal
from app.config import settings


def run_migrations():
    """Run all pending migrations."""
    print("Running database migrations...")
    alembic_cfg = Config("alembic.ini")
    command.upgrade(alembic_cfg, "head")
    print("✅ Migrations completed successfully!")


def create_migration(message: str):
    """Create a new migration."""
    print(f"Creating migration: {message}")
    alembic_cfg = Config("alembic.ini")
    command.revision(alembic_cfg, message=message, autogenerate=True)
    print("✅ Migration created successfully!")


def rollback_migration(revision: str = "-1"):
    """Rollback to a specific migration."""
    print(f"Rolling back to revision: {revision}")
    alembic_cfg = Config("alembic.ini")
    command.downgrade(alembic_cfg, revision)
    print("✅ Rollback completed successfully!")


def show_migration_history():
    """Show migration history."""
    print("Migration history:")
    alembic_cfg = Config("alembic.ini")
    command.history(alembic_cfg)


def show_current_revision():
    """Show current database revision."""
    print("Current database revision:")
    alembic_cfg = Config("alembic.ini")
    command.current(alembic_cfg)


def reset_database():
    """Reset database by dropping all tables and running migrations."""
    print("⚠️  WARNING: This will drop all tables and data!")
    confirm = input("Are you sure you want to continue? (yes/no): ")
    
    if confirm.lower() != "yes":
        print("Operation cancelled.")
        return
    
    print("Dropping all tables...")
    
    # Drop all tables
    with engine.begin() as conn:
        # For SQLite, we need to drop tables individually
        if settings.database_url.startswith("sqlite"):
            # Get all table names
            result = conn.execute(text(
                "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"
            ))
            tables = [row[0] for row in result]
            
            # Drop each table
            for table in tables:
                conn.execute(text(f"DROP TABLE IF EXISTS {table}"))
        else:
            # For PostgreSQL, we can drop all tables at once
            conn.execute(text("DROP SCHEMA public CASCADE"))
            conn.execute(text("CREATE SCHEMA public"))
    
    print("✅ All tables dropped!")
    
    # Run migrations to recreate tables
    run_migrations()


def check_database_connection():
    """Check if database connection is working."""
    try:
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        print("✅ Database connection successful!")
        print(f"Database URL: {settings.database_url}")
        return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False


def show_database_info():
    """Show database information."""
    print("Database Information:")
    print(f"URL: {settings.database_url}")
    print(f"Engine: {engine}")
    
    try:
        with engine.connect() as conn:
            if settings.database_url.startswith("sqlite"):
                result = conn.execute(text(
                    "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name"
                ))
                tables = [row[0] for row in result]
            else:
                result = conn.execute(text(
                    "SELECT tablename FROM pg_tables WHERE schemaname = 'public' ORDER BY tablename"
                ))
                tables = [row[0] for row in result]
            
            print(f"Tables ({len(tables)}):")
            for table in tables:
                print(f"  - {table}")
                
    except Exception as e:
        print(f"Error getting table info: {e}")


def create_indexes():
    """Create additional database indexes for performance."""
    print("Creating additional performance indexes...")
    
    with engine.begin() as conn:
        # Additional indexes that might not be in migrations
        indexes = [
            # Full-text search indexes (if supported)
            "CREATE INDEX IF NOT EXISTS idx_projects_name_fts ON projects(name)",
            "CREATE INDEX IF NOT EXISTS idx_risks_title_fts ON risks(title)",
            "CREATE INDEX IF NOT EXISTS idx_actions_title_fts ON actions(title)",
            "CREATE INDEX IF NOT EXISTS idx_issues_title_fts ON issues(title)",
            "CREATE INDEX IF NOT EXISTS idx_decisions_title_fts ON decisions(title)",
            
            # Performance indexes for common queries
            "CREATE INDEX IF NOT EXISTS idx_audit_recent ON audit_entries(changedAt DESC, itemType)",
        ]
        
        for index_sql in indexes:
            try:
                conn.execute(text(index_sql))
                print(f"✅ Created index: {index_sql.split('idx_')[1].split(' ')[0]}")
            except Exception as e:
                print(f"⚠️  Index creation failed: {e}")
    
    print("✅ Index creation completed!")


def main():
    """Main CLI interface."""
    parser = argparse.ArgumentParser(description="RAID API Database Management")
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Migration commands
    subparsers.add_parser("migrate", help="Run all pending migrations")
    
    create_parser = subparsers.add_parser("create-migration", help="Create a new migration")
    create_parser.add_argument("message", help="Migration message")
    
    rollback_parser = subparsers.add_parser("rollback", help="Rollback migration")
    rollback_parser.add_argument("--revision", default="-1", help="Revision to rollback to")
    
    # Info commands
    subparsers.add_parser("history", help="Show migration history")
    subparsers.add_parser("current", help="Show current revision")
    subparsers.add_parser("info", help="Show database information")
    subparsers.add_parser("check", help="Check database connection")
    
    # Maintenance commands
    subparsers.add_parser("reset", help="Reset database (DROP ALL TABLES)")
    subparsers.add_parser("create-indexes", help="Create additional performance indexes")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # Execute commands
    if args.command == "migrate":
        run_migrations()
    elif args.command == "create-migration":
        create_migration(args.message)
    elif args.command == "rollback":
        rollback_migration(args.revision)
    elif args.command == "history":
        show_migration_history()
    elif args.command == "current":
        show_current_revision()
    elif args.command == "info":
        show_database_info()
    elif args.command == "check":
        check_database_connection()
    elif args.command == "reset":
        reset_database()
    elif args.command == "create-indexes":
        create_indexes()


if __name__ == "__main__":
    main()
