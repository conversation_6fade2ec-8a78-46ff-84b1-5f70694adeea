# Analytics & Dashboard System

The Analytics & Dashboard System provides comprehensive data visualization, trend analysis, and customizable dashboard capabilities for the RAID application.

## Features

### 🎯 Core Capabilities
- **Customizable Dashboards**: Create and manage personalized dashboards with flexible layouts
- **Advanced Charts**: Support for pie, bar, line, scatter, heatmap, gauge, and more chart types
- **Real-time Analytics**: Live data processing with intelligent caching
- **Trend Analysis**: Statistical analysis, forecasting, and pattern detection
- **Data Sources**: Comprehensive analytics across risks, actions, issues, decisions, projects, and users
- **Access Control**: Role-based permissions with public/private dashboards

### 📊 Chart Types
- **Pie Charts**: Distribution analysis with customizable slices
- **Bar Charts**: Comparative analysis with sorting and grouping
- **Line Charts**: Time series analysis with trend visualization
- **Scatter Plots**: Correlation analysis with size and color mapping
- **Heatmaps**: Pattern visualization with intensity mapping
- **Gauge Charts**: KPI monitoring with threshold alerts

### 📈 Analytics Features
- **Custom Queries**: Flexible data querying with filters and aggregations
- **Trend Detection**: Automatic pattern recognition and forecasting
- **Statistical Analysis**: Correlation, regression, and variance analysis
- **Seasonality Detection**: Weekly and monthly pattern identification
- **Outlier Detection**: Automatic anomaly identification
- **Change Point Detection**: Significant trend shift identification

## API Endpoints

### Dashboard Management
```
POST   /api/v1/dashboards          # Create dashboard
GET    /api/v1/dashboards          # List dashboards
GET    /api/v1/dashboards/{id}     # Get dashboard
PUT    /api/v1/dashboards/{id}     # Update dashboard
DELETE /api/v1/dashboards/{id}     # Delete dashboard
GET    /api/v1/dashboards/{id}/widgets  # Get dashboard widgets
```

### Widget Management
```
POST   /api/v1/widgets             # Create widget
GET    /api/v1/widgets/{id}        # Get widget
PUT    /api/v1/widgets/{id}        # Update widget
DELETE /api/v1/widgets/{id}        # Delete widget
GET    /api/v1/widgets/{id}/data   # Get widget data
```

### Analytics Queries
```
POST   /api/v1/analytics/query     # Execute custom query
GET    /api/v1/analytics/summary   # Get dashboard summary
```

### Trend Analysis
```
POST   /api/v1/trends              # Create trend analysis
GET    /api/v1/trends              # List trend analyses
GET    /api/v1/trends/{id}         # Get trend analysis
DELETE /api/v1/trends/{id}         # Delete trend analysis
```

### Chart Processing
```
POST   /api/v1/charts/process/pie      # Process pie chart data
POST   /api/v1/charts/process/bar      # Process bar chart data
POST   /api/v1/charts/process/line     # Process line chart data
POST   /api/v1/charts/process/scatter  # Process scatter plot data
POST   /api/v1/charts/process/heatmap  # Process heatmap data
POST   /api/v1/charts/process/gauge    # Process gauge chart data
```

## Data Models

### Dashboard
```python
{
    "id": "string",
    "name": "string",
    "description": "string",
    "layout": {"columns": 12, "rows": 8},
    "theme": "light|dark",
    "refresh_interval": 300,
    "is_public": false,
    "is_default": false,
    "allowed_users": ["user_id"],
    "project_id": "string",
    "created_by": "string",
    "created_at": "datetime",
    "updated_at": "datetime"
}
```

### Dashboard Widget
```python
{
    "id": "string",
    "dashboard_id": "string",
    "name": "string",
    "description": "string",
    "widget_type": "chart|metric|table|text",
    "chart_type": "pie|bar|line|scatter|heatmap|gauge",
    "data_source": "risks|actions|issues|decisions|projects|users",
    "entity_filters": {"status": ["Open", "In progress"]},
    "date_range": {"period": "last_30_days"},
    "chart_config": {"show_legend": true},
    "position": {"x": 0, "y": 0, "width": 6, "height": 4},
    "cached_data": {},
    "last_cached": "datetime",
    "cache_duration": 3600,
    "is_active": true
}
```

### Trend Analysis
```python
{
    "id": "string",
    "name": "string",
    "description": "string",
    "analysis_type": "trend|forecast|correlation",
    "entity_type": "risks|actions|issues|decisions",
    "field_name": "score|progress|count",
    "time_period": {"start_date": "date", "end_date": "date"},
    "algorithm": "linear|moving_average|polynomial",
    "parameters": {"window_size": 7},
    "trend_direction": "increasing|decreasing|stable",
    "trend_strength": 0.85,
    "confidence_score": 0.92,
    "correlation_coefficient": 0.78,
    "r_squared": 0.61,
    "data_points_count": 30,
    "results": {}
}
```

## Usage Examples

### Creating a Dashboard
```python
dashboard_data = {
    "name": "Risk Analytics Dashboard",
    "description": "Comprehensive risk analysis and monitoring",
    "layout": {"columns": 12, "rows": 8},
    "theme": "light",
    "refresh_interval": 300,
    "is_public": False,
    "project_id": "project-123"
}

response = requests.post("/api/v1/dashboards", json=dashboard_data)
dashboard = response.json()
```

### Adding a Chart Widget
```python
widget_data = {
    "dashboard_id": dashboard["id"],
    "name": "Risk Status Distribution",
    "widget_type": "chart",
    "chart_type": "pie",
    "data_source": "risks",
    "entity_filters": {"priority": ["High", "Medium"]},
    "date_range": {"period": "last_30_days"},
    "chart_config": {
        "show_legend": True,
        "show_labels": True,
        "max_slices": 8
    },
    "position": {"x": 0, "y": 0, "width": 6, "height": 4}
}

response = requests.post("/api/v1/widgets", json=widget_data)
widget = response.json()
```

### Executing Analytics Query
```python
query_data = {
    "entity_type": "risks",
    "metrics": ["count", "avg_score", "completion_rate"],
    "filters": {"status": ["Open", "In progress"]},
    "date_range": {"period": "last_90_days"},
    "group_by": ["priority", "status"],
    "aggregation_period": "weekly"
}

response = requests.post("/api/v1/analytics/query", json=query_data)
results = response.json()
```

### Creating Trend Analysis
```python
trend_data = {
    "name": "Risk Score Trend Analysis",
    "description": "Analyze risk score trends over the last quarter",
    "analysis_type": "trend",
    "entity_type": "risks",
    "field_name": "score",
    "time_period": {
        "start_date": "2024-10-01T00:00:00Z",
        "end_date": "2024-12-31T23:59:59Z"
    },
    "algorithm": "linear",
    "parameters": {"confidence_level": 0.95},
    "project_id": "project-123"
}

response = requests.post("/api/v1/trends", json=trend_data)
analysis = response.json()
```

## Performance Optimization

### Caching Strategy
- **Widget-level caching**: Configurable cache duration per widget
- **Automatic refresh**: Background cache updates based on data changes
- **Smart invalidation**: Cache invalidation on relevant data updates

### Query Optimization
- **Indexed queries**: Optimized database queries with proper indexing
- **Aggregation pipelines**: Efficient data aggregation at the database level
- **Pagination**: Large result set handling with cursor-based pagination

### Processing Optimization
- **Parallel processing**: Concurrent chart data processing
- **Memory management**: Efficient memory usage for large datasets
- **Background tasks**: Asynchronous trend analysis execution

## Security & Access Control

### Dashboard Access
- **Owner permissions**: Full control over owned dashboards
- **Public dashboards**: Read-only access for all users
- **Shared dashboards**: Explicit user access lists
- **Project-based access**: Automatic access based on project membership

### Data Security
- **Row-level security**: Users only see data they have access to
- **Query filtering**: Automatic security filters applied to all queries
- **Audit logging**: Comprehensive access and modification logging

## Integration Points

### Data Sources
- **RAID Entities**: Risks, Actions, Issues, Decisions
- **Project Data**: Project metrics and statistics
- **User Activity**: User engagement and activity metrics
- **Custom Data**: Extensible data source framework

### External Systems
- **Export capabilities**: Dashboard and chart export functionality
- **API integration**: RESTful API for external system integration
- **Webhook support**: Real-time data updates via webhooks

## Development Guidelines

### Adding New Chart Types
1. Extend `ChartType` enum in schemas
2. Implement processing logic in `ChartProcessor`
3. Add API endpoint in `charts_router`
4. Update frontend chart rendering

### Custom Analytics
1. Define new metrics in `AnalyticsService`
2. Implement data aggregation logic
3. Add query parameters to `AnalyticsQuery` schema
4. Update API documentation

### Performance Monitoring
- Monitor query execution times
- Track cache hit rates
- Analyze memory usage patterns
- Monitor API response times

## Testing

Run the analytics test suite:
```bash
pytest api_py/tests/test_analytics.py -v
```

Test coverage includes:
- Dashboard CRUD operations
- Widget management and data processing
- Chart data processing utilities
- Trend analysis algorithms
- API endpoint functionality
- Security and access control
