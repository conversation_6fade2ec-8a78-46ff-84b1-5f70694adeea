# FastAPI backend (api_py)

## Run locally (without Docker)

1. Create venv and install deps

```
python3 -m venv .venv
./.venv/bin/pip install -r requirements.txt
```

2. Set database URL (optional; defaults to SQLite file raid.db)

```
export DATABASE_URL="postgresql://postgres:postgres@localhost:5432/raid"
```

3. Start server (Uvicorn)

```
./.venv/bin/python -m uvicorn api_py.app.main:app --host 0.0.0.0 --port 5000
```

API will be available at http://localhost:5000

---

## Run with Docker Compose

From the repo root:

```
docker compose -f docker-compose.fastapi.yml up --build
```

Services:
- db: Postgres on localhost:5432 (user postgres / pass postgres / db raid)
- fastapi: FastAPI on http://localhost:5000

Stop and clean:

```
docker compose -f docker-compose.fastapi.yml down -v
```
