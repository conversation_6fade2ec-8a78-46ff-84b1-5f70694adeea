"""Tests for analytics and dashboard functionality."""

import pytest
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from fastapi.testclient import TestClient

from app.main import app
from app.db import get_db
from app.models.user import User
from app.models.project import Project
from app.models.risk import Risk
from app.models.analytics import Dashboard, DashboardWidget, TrendAnalysis
from app.services.analytics_service import AnalyticsService
from app.services.chart_processor import ChartProcessor
from app.services.trend_analyzer import TrendAnalyzer
from app.schemas.analytics import (
    DashboardCreate, DashboardWidgetCreate, TrendAnalysisCreate,
    AnalyticsQuery, DataSource, ChartType, WidgetType, AggregationPeriod,
    WidgetPosition, DateRange
)
from tests.conftest import TestingSessionLocal, override_get_db


app.dependency_overrides[get_db] = override_get_db
client = TestClient(app)


@pytest.fixture
def db_session():
    """Create a test database session."""
    session = TestingSessionLocal()
    try:
        yield session
    finally:
        session.close()


@pytest.fixture
def test_user(db_session: Session):
    """Create a test user."""
    user = User(
        id="test-user-id",
        username="testuser",
        email="<EMAIL>",
        hashed_password="hashed_password",
        full_name="Test User",
        role="contributor"
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


@pytest.fixture
def test_project(db_session: Session, test_user: User):
    """Create a test project."""
    project = Project(
        id="test-project-id",
        name="Test Project",
        description="A test project",
        cadence="weekly",
        categories=["risks", "actions"],
        status="active",
        owner=test_user.id
    )
    db_session.add(project)
    db_session.commit()
    db_session.refresh(project)
    return project


@pytest.fixture
def test_risks(db_session: Session, test_project: Project, test_user: User):
    """Create test risks for analytics."""
    risks = []
    base_date = datetime.utcnow() - timedelta(days=30)
    
    for i in range(10):
        risk = Risk(
            id=f"risk-{i}",
            projectId=test_project.id,
            title=f"Test Risk {i}",
            description=f"Description for risk {i}",
            status="Open" if i % 3 == 0 else "In progress" if i % 3 == 1 else "Resolved",
            priority="High" if i % 4 == 0 else "Medium" if i % 4 == 1 else "Low",
            probability=0.1 + (i * 0.1),
            impact=1 + (i % 5),
            score=(0.1 + (i * 0.1)) * (1 + (i % 5)),
            createdBy=test_user.id,
            createdAt=base_date + timedelta(days=i * 3)
        )
        risks.append(risk)
        db_session.add(risk)
    
    db_session.commit()
    return risks


@pytest.fixture
def analytics_service(db_session: Session):
    """Create an analytics service instance."""
    return AnalyticsService(db_session)


class TestAnalyticsService:
    """Test the AnalyticsService class."""

    def test_create_dashboard(self, analytics_service: AnalyticsService, test_user: User, test_project: Project):
        """Test creating a dashboard."""
        dashboard_data = DashboardCreate(
            name="Test Dashboard",
            description="A test dashboard",
            layout={"columns": 12, "rows": 8},
            theme="light",
            refresh_interval=300,
            is_public=False,
            is_default=False,
            project_id=test_project.id
        )
        
        dashboard = analytics_service.create_dashboard(dashboard_data, test_user.id)
        
        assert dashboard.id is not None
        assert dashboard.name == "Test Dashboard"
        assert dashboard.created_by == test_user.id
        assert dashboard.project_id == test_project.id
        assert dashboard.theme == "light"
        assert dashboard.refresh_interval == 300

    def test_get_dashboards(self, analytics_service: AnalyticsService, test_user: User, test_project: Project):
        """Test getting dashboards for a user."""
        # Create test dashboards
        dashboard1_data = DashboardCreate(
            name="Dashboard 1",
            description="First dashboard",
            project_id=test_project.id
        )
        dashboard2_data = DashboardCreate(
            name="Dashboard 2",
            description="Second dashboard",
            is_public=True
        )
        
        dashboard1 = analytics_service.create_dashboard(dashboard1_data, test_user.id)
        dashboard2 = analytics_service.create_dashboard(dashboard2_data, test_user.id)
        
        # Get dashboards
        dashboards = analytics_service.get_dashboards(test_user.id)
        
        assert len(dashboards) >= 2
        dashboard_names = [d.name for d in dashboards]
        assert "Dashboard 1" in dashboard_names
        assert "Dashboard 2" in dashboard_names

    def test_create_widget(self, analytics_service: AnalyticsService, test_user: User, test_project: Project):
        """Test creating a dashboard widget."""
        # Create dashboard first
        dashboard_data = DashboardCreate(
            name="Widget Test Dashboard",
            project_id=test_project.id
        )
        dashboard = analytics_service.create_dashboard(dashboard_data, test_user.id)
        
        # Create widget
        widget_data = DashboardWidgetCreate(
            dashboard_id=dashboard.id,
            name="Risk Status Chart",
            description="Chart showing risk status distribution",
            widget_type=WidgetType.CHART,
            chart_type=ChartType.PIE,
            data_source=DataSource.RISKS,
            entity_filters={"status": ["Open", "In progress"]},
            date_range=DateRange(period="last_30_days"),
            chart_config={"show_legend": True, "show_labels": True},
            position=WidgetPosition(x=0, y=0, width=6, height=4)
        )
        
        widget = analytics_service.create_widget(widget_data, test_user.id)
        
        assert widget is not None
        assert widget.name == "Risk Status Chart"
        assert widget.widget_type == WidgetType.CHART
        assert widget.chart_type == ChartType.PIE
        assert widget.data_source == DataSource.RISKS
        assert widget.dashboard_id == dashboard.id

    def test_get_widget_data(self, analytics_service: AnalyticsService, test_user: User, 
                           test_project: Project, test_risks):
        """Test getting processed data for a widget."""
        # Create dashboard and widget
        dashboard_data = DashboardCreate(name="Data Test Dashboard", project_id=test_project.id)
        dashboard = analytics_service.create_dashboard(dashboard_data, test_user.id)
        
        widget_data = DashboardWidgetCreate(
            dashboard_id=dashboard.id,
            name="Risk Data Widget",
            widget_type=WidgetType.CHART,
            chart_type=ChartType.PIE,
            data_source=DataSource.RISKS,
            entity_filters={},
            date_range=DateRange(period="last_30_days"),
            position=WidgetPosition(x=0, y=0, width=6, height=4)
        )
        widget = analytics_service.create_widget(widget_data, test_user.id)
        
        # Get widget data
        data = analytics_service.get_widget_data(widget.id, test_user.id)
        
        assert data is not None
        assert "data" in data
        assert "metadata" in data
        assert data["metadata"]["chart_type"] == "pie"

    def test_execute_analytics_query(self, analytics_service: AnalyticsService, test_user: User, test_risks):
        """Test executing a custom analytics query."""
        query_data = AnalyticsQuery(
            entity_type=DataSource.RISKS,
            metrics=["count", "avg_score"],
            filters={"status": ["Open", "In progress"]},
            date_range=DateRange(period="last_30_days"),
            aggregation_period=AggregationPeriod.DAILY
        )
        
        result = analytics_service.execute_analytics_query(query_data, test_user.id)
        
        assert "data" in result
        assert "metadata" in result
        assert "total_count" in result
        assert result["metadata"]["entity_type"] == DataSource.RISKS

    def test_create_trend_analysis(self, analytics_service: AnalyticsService, test_user: User, 
                                 test_project: Project, test_risks):
        """Test creating a trend analysis."""
        analysis_data = TrendAnalysisCreate(
            name="Risk Score Trend",
            description="Trend analysis of risk scores over time",
            analysis_type="trend",
            entity_type=DataSource.RISKS,
            field_name="score",
            time_period={
                "start_date": (datetime.utcnow() - timedelta(days=30)).isoformat(),
                "end_date": datetime.utcnow().isoformat()
            },
            algorithm="linear",
            project_id=test_project.id
        )
        
        analysis = analytics_service.create_trend_analysis(analysis_data, test_user.id)
        
        assert analysis.id is not None
        assert analysis.name == "Risk Score Trend"
        assert analysis.entity_type == DataSource.RISKS
        assert analysis.field_name == "score"
        assert analysis.algorithm == "linear"
        assert analysis.data_points_count >= 0


class TestChartProcessor:
    """Test the ChartProcessor utility class."""

    def test_process_pie_chart(self):
        """Test pie chart data processing."""
        data = [
            {"status": "Open", "count": 5},
            {"status": "In progress", "count": 3},
            {"status": "Resolved", "count": 2},
            {"status": "Closed", "count": 1}
        ]
        
        result = ChartProcessor.process_pie_chart(data, "count", "status")
        
        assert "data" in result
        assert "metadata" in result
        assert len(result["data"]) == 4
        assert result["metadata"]["total_value"] == 11
        assert result["metadata"]["chart_type"] == "pie"
        
        # Check that percentages add up to 100
        total_percentage = sum(item["percentage"] for item in result["data"])
        assert abs(total_percentage - 100.0) < 0.1

    def test_process_bar_chart(self):
        """Test bar chart data processing."""
        data = [
            {"priority": "High", "value": 10},
            {"priority": "Medium", "value": 15},
            {"priority": "Low", "value": 5},
            {"priority": "High", "value": 8}  # Duplicate to test aggregation
        ]
        
        result = ChartProcessor.process_bar_chart(data, "priority", "value")
        
        assert "data" in result
        assert "metadata" in result
        assert len(result["data"]) == 3  # Should aggregate duplicates
        assert result["metadata"]["chart_type"] == "bar"
        
        # Find High priority entry (should be aggregated)
        high_entry = next(item for item in result["data"] if item["x"] == "High")
        assert high_entry["y"] == 18  # 10 + 8

    def test_process_line_chart(self):
        """Test line chart data processing."""
        base_date = datetime.utcnow() - timedelta(days=7)
        data = [
            {"date": (base_date + timedelta(days=i)).isoformat(), "value": i * 2}
            for i in range(8)
        ]
        
        result = ChartProcessor.process_line_chart(data, "date", "value")
        
        assert "data" in result
        assert "metadata" in result
        assert result["metadata"]["chart_type"] == "line"
        assert len(result["data"]) == 8

    def test_process_scatter_chart(self):
        """Test scatter plot data processing."""
        data = [
            {"probability": 0.1, "impact": 2, "size": 5},
            {"probability": 0.3, "impact": 4, "size": 8},
            {"probability": 0.7, "impact": 3, "size": 6},
            {"probability": 0.9, "impact": 5, "size": 10}
        ]
        
        result = ChartProcessor.process_scatter_chart(data, "probability", "impact", "size")
        
        assert "data" in result
        assert "metadata" in result
        assert result["metadata"]["chart_type"] == "scatter"
        assert len(result["data"]) == 4
        assert result["metadata"]["has_size"] is True
        
        # Check that correlation is calculated
        assert "correlation" in result["metadata"]

    def test_process_gauge_chart(self):
        """Test gauge chart data processing."""
        result = ChartProcessor.process_gauge_chart(
            current_value=75,
            min_value=0,
            max_value=100,
            thresholds={"warning": 60, "critical": 80}
        )
        
        assert "data" in result
        assert "metadata" in result
        assert result["data"]["value"] == 75
        assert result["data"]["percentage"] == 75.0
        assert result["data"]["status"] == "warning"  # Between warning and critical
        assert result["metadata"]["chart_type"] == "gauge"


class TestTrendAnalyzer:
    """Test the TrendAnalyzer utility class."""

    def test_analyze_trend_patterns(self):
        """Test comprehensive trend pattern analysis."""
        # Create sample time series data with an upward trend
        base_date = datetime.utcnow() - timedelta(days=30)
        data_points = [
            (base_date + timedelta(days=i), 10 + i * 0.5 + (i % 7) * 2)  # Trend + weekly pattern
            for i in range(30)
        ]
        
        result = TrendAnalyzer.analyze_trend_patterns(data_points)
        
        assert "basic_stats" in result
        assert "trend" in result
        assert "seasonality" in result
        assert "volatility" in result
        
        # Check basic stats
        assert result["basic_stats"]["data_points"] == 30
        assert result["basic_stats"]["mean"] > 0
        
        # Check trend detection
        assert result["trend"]["direction"] in ["increasing", "decreasing", "stable"]
        assert 0 <= result["trend"]["strength"] <= 1
        assert 0 <= result["trend"]["confidence"] <= 1

    def test_detect_seasonality(self):
        """Test seasonality detection."""
        # Create data with weekly pattern
        base_date = datetime.utcnow() - timedelta(days=21)  # 3 weeks
        data_points = []
        
        for i in range(21):
            date = base_date + timedelta(days=i)
            # Higher values on weekdays, lower on weekends
            value = 20 if date.weekday() < 5 else 10
            data_points.append((date, value))
        
        result = TrendAnalyzer.analyze_trend_patterns(data_points)
        
        # Should detect weekly seasonality
        seasonality = result["seasonality"]
        assert seasonality["has_seasonality"] is True
        assert seasonality["period"] == "weekly"
        assert seasonality["strength"] > 0.3

    def test_detect_outliers(self):
        """Test outlier detection."""
        values = [10, 12, 11, 13, 10, 11, 50, 12, 11, 10]  # 50 is an outlier
        
        result = TrendAnalyzer._detect_outliers(values, method="iqr")
        
        assert len(result["outlier_indices"]) >= 1
        assert 50 in result["outlier_values"]
        assert result["method"] == "iqr"

    def test_generate_forecast(self):
        """Test forecast generation."""
        # Create linear trend data
        base_date = datetime.utcnow() - timedelta(days=10)
        dates = [base_date + timedelta(days=i) for i in range(10)]
        values = [i * 2 + 10 for i in range(10)]  # Linear trend
        
        result = TrendAnalyzer._generate_forecast(dates, values, periods=5)
        
        assert "forecast_points" in result
        assert len(result["forecast_points"]) == 5
        assert "method" in result
        assert result["method"] == "linear_regression"
        
        # Check that forecast values continue the trend
        forecast_values = [point["value"] for point in result["forecast_points"]]
        assert all(forecast_values[i] < forecast_values[i+1] for i in range(len(forecast_values)-1))


class TestAnalyticsAPI:
    """Test the analytics API endpoints."""

    def test_create_dashboard_endpoint(self, test_user: User, test_project: Project):
        """Test the create dashboard endpoint."""
        # This would require proper authentication setup
        # For now, just test the data structure
        dashboard_data = {
            "name": "API Test Dashboard",
            "description": "Dashboard created via API",
            "layout": {"columns": 12, "rows": 8},
            "theme": "dark",
            "is_public": False,
            "project_id": test_project.id
        }
        
        # Verify data structure is correct
        assert dashboard_data["name"] == "API Test Dashboard"
        assert dashboard_data["theme"] == "dark"
        assert dashboard_data["project_id"] == test_project.id

    def test_widget_data_structure(self):
        """Test widget data structure validation."""
        widget_data = {
            "dashboard_id": "test-dashboard-id",
            "name": "Test Widget",
            "widget_type": "chart",
            "chart_type": "pie",
            "data_source": "risks",
            "entity_filters": {"status": ["Open"]},
            "date_range": {"period": "last_30_days"},
            "position": {"x": 0, "y": 0, "width": 6, "height": 4}
        }
        
        # Verify required fields are present
        required_fields = ["dashboard_id", "name", "widget_type", "data_source", "position"]
        for field in required_fields:
            assert field in widget_data

    def test_analytics_query_structure(self):
        """Test analytics query data structure."""
        query_data = {
            "entity_type": "risks",
            "metrics": ["count", "avg_score"],
            "filters": {"priority": ["High", "Medium"]},
            "date_range": {"period": "last_7_days"},
            "aggregation_period": "daily"
        }
        
        # Verify structure
        assert query_data["entity_type"] == "risks"
        assert "count" in query_data["metrics"]
        assert "priority" in query_data["filters"]
