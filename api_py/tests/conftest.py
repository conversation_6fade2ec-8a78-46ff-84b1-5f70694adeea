"""Test configuration and fixtures."""

import os
import pytest
from typing import Generator
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool

from app.main import app
from app.db import get_db, Base
from app.models.user import User
from app.auth import get_password_hash, create_access_token
from app.config import settings


# Test database URL - use in-memory SQLite for tests
TEST_DATABASE_URL = "sqlite:///:memory:"

# Create test engine
test_engine = create_engine(
    TEST_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)

# Create test session factory
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_engine)


def override_get_db():
    """Override database dependency for tests."""
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


# Override the database dependency
app.dependency_overrides[get_db] = override_get_db


@pytest.fixture(scope="session")
def test_db():
    """Create test database tables."""
    Base.metadata.create_all(bind=test_engine)
    yield
    Base.metadata.drop_all(bind=test_engine)


@pytest.fixture(scope="function")
def db_session(test_db) -> Generator[Session, None, None]:
    """Create a fresh database session for each test."""
    connection = test_engine.connect()
    transaction = connection.begin()
    session = TestingSessionLocal(bind=connection)
    
    yield session
    
    session.close()
    transaction.rollback()
    connection.close()


@pytest.fixture(scope="function")
def client(db_session) -> TestClient:
    """Create a test client."""
    return TestClient(app)


@pytest.fixture
def test_user(db_session: Session) -> User:
    """Create a test user."""
    user = User(
        id="test-user-id",
        username="testuser",
        email="<EMAIL>",
        full_name="Test User",
        hashed_password=get_password_hash("testpassword123"),
        role="contributor",
        is_active=True,
        is_verified=True
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


@pytest.fixture
def admin_user(db_session: Session) -> User:
    """Create an admin test user."""
    user = User(
        id="admin-user-id",
        username="adminuser",
        email="<EMAIL>",
        full_name="Admin User",
        hashed_password=get_password_hash("adminpassword123"),
        role="admin",
        is_active=True,
        is_verified=True
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


@pytest.fixture
def viewer_user(db_session: Session) -> User:
    """Create a viewer test user."""
    user = User(
        id="viewer-user-id",
        username="vieweruser",
        email="<EMAIL>",
        full_name="Viewer User",
        hashed_password=get_password_hash("viewerpassword123"),
        role="viewer",
        is_active=True,
        is_verified=True
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


@pytest.fixture
def auth_headers(test_user: User) -> dict:
    """Create authentication headers for test user."""
    access_token = create_access_token(data={"sub": test_user.username})
    return {"Authorization": f"Bearer {access_token}"}


@pytest.fixture
def admin_auth_headers(admin_user: User) -> dict:
    """Create authentication headers for admin user."""
    access_token = create_access_token(data={"sub": admin_user.username})
    return {"Authorization": f"Bearer {access_token}"}


@pytest.fixture
def viewer_auth_headers(viewer_user: User) -> dict:
    """Create authentication headers for viewer user."""
    access_token = create_access_token(data={"sub": viewer_user.username})
    return {"Authorization": f"Bearer {access_token}"}


@pytest.fixture
def sample_project_data() -> dict:
    """Sample project data for testing."""
    return {
        "name": "Test Project",
        "description": "A test project for RAID management",
        "cadence": "weekly",
        "categories": ["risks", "actions"],
        "status": "active",
        "owner": "test-user-id"
    }


@pytest.fixture
def sample_risk_data() -> dict:
    """Sample risk data for testing."""
    return {
        "title": "Test Risk",
        "description": "A test risk",
        "category": "Technical",
        "probability": "Medium",
        "impact": "High",
        "status": "Open",
        "owner": "test-user-id",
        "priority": "High"
    }


@pytest.fixture
def sample_action_data() -> dict:
    """Sample action data for testing."""
    return {
        "title": "Test Action",
        "description": "A test action",
        "category": "Process",
        "status": "Open",
        "priority": "Medium",
        "owner": "test-user-id",
        "assignee": "test-user-id",
        "progress": 0
    }


@pytest.fixture
def sample_issue_data() -> dict:
    """Sample issue data for testing."""
    return {
        "title": "Test Issue",
        "description": "A test issue",
        "category": "Bug",
        "severity": "Medium",
        "status": "Open",
        "owner": "test-user-id",
        "priority": "Medium"
    }


@pytest.fixture
def sample_decision_data() -> dict:
    """Sample decision data for testing."""
    return {
        "title": "Test Decision",
        "description": "A test decision",
        "rationale": "Test rationale",
        "status": "Proposed",
        "priority": "Medium",
        "category": "Strategic",
        "decidedBy": "test-user-id"
    }


# Environment setup for tests
@pytest.fixture(autouse=True)
def setup_test_env():
    """Set up test environment variables."""
    os.environ["TESTING"] = "true"
    os.environ["DATABASE_URL"] = TEST_DATABASE_URL
    yield
    # Cleanup
    if "TESTING" in os.environ:
        del os.environ["TESTING"]
