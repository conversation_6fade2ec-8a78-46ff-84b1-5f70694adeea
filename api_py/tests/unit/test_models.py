"""Unit tests for database models."""

import pytest
from datetime import datetime
from sqlalchemy.orm import Session

from app.models.user import User
from app.models.project import Project
from app.models.risk import Risk
from app.models.action import Action
from app.models.issue import Issue
from app.models.decision import Decision
from app.models.audit import AuditEntry


@pytest.mark.unit
class TestUserModel:
    """Test User model functionality."""

    def test_user_creation(self, db_session: Session):
        """Test creating a user."""
        user = User(
            id="test-id",
            username="testuser",
            email="<EMAIL>",
            full_name="Test User",
            hashed_password="hashed_password",
            role="contributor"
        )
        
        db_session.add(user)
        db_session.commit()
        
        assert user.id == "test-id"
        assert user.username == "testuser"
        assert user.email == "<EMAIL>"
        assert user.role == "contributor"
        assert user.is_active is True
        assert user.is_verified is False
        assert user.createdAt is not None

    def test_user_permissions(self):
        """Test user permission checking."""
        admin_user = User(role="admin")
        pm_user = User(role="pm")
        contributor_user = User(role="contributor")
        viewer_user = User(role="viewer")
        
        # Admin should have all permissions
        assert admin_user.has_permission("admin")
        assert admin_user.has_permission("pm")
        assert admin_user.has_permission("contributor")
        assert admin_user.has_permission("viewer")
        
        # PM should have pm, contributor, and viewer permissions
        assert not pm_user.has_permission("admin")
        assert pm_user.has_permission("pm")
        assert pm_user.has_permission("contributor")
        assert pm_user.has_permission("viewer")
        
        # Contributor should have contributor and viewer permissions
        assert not contributor_user.has_permission("admin")
        assert not contributor_user.has_permission("pm")
        assert contributor_user.has_permission("contributor")
        assert contributor_user.has_permission("viewer")
        
        # Viewer should only have viewer permissions
        assert not viewer_user.has_permission("admin")
        assert not viewer_user.has_permission("pm")
        assert not viewer_user.has_permission("contributor")
        assert viewer_user.has_permission("viewer")

    def test_user_repr(self):
        """Test user string representation."""
        user = User(id="test-id", username="testuser", email="<EMAIL>")
        assert "testuser" in repr(user)
        assert "<EMAIL>" in repr(user)


@pytest.mark.unit
class TestProjectModel:
    """Test Project model functionality."""

    def test_project_creation(self, db_session: Session):
        """Test creating a project."""
        project = Project(
            id="project-id",
            name="Test Project",
            description="Test description",
            cadence="weekly",
            categories=["risks", "actions"],
            status="active",
            owner="user-id"
        )
        
        db_session.add(project)
        db_session.commit()
        
        assert project.id == "project-id"
        assert project.name == "Test Project"
        assert project.cadence == "weekly"
        assert project.categories == ["risks", "actions"]
        assert project.status == "active"
        assert project.createdAt is not None
        assert project.updatedAt is not None

    def test_project_repr(self):
        """Test project string representation."""
        project = Project(id="test-id", name="Test Project", status="active")
        assert "Test Project" in repr(project)
        assert "active" in repr(project)


@pytest.mark.unit
class TestRiskModel:
    """Test Risk model functionality."""

    def test_risk_creation(self, db_session: Session):
        """Test creating a risk."""
        # First create a project
        project = Project(
            id="project-id",
            name="Test Project",
            owner="user-id"
        )
        db_session.add(project)
        db_session.commit()
        
        risk = Risk(
            id="risk-id",
            projectId="project-id",
            title="Test Risk",
            description="Test description",
            category="Technical",
            probability="Medium",
            impact="High",
            status="Open",
            owner="user-id",
            priority="High"
        )
        
        db_session.add(risk)
        db_session.commit()
        
        assert risk.id == "risk-id"
        assert risk.projectId == "project-id"
        assert risk.title == "Test Risk"
        assert risk.probability == "Medium"
        assert risk.impact == "High"
        assert risk.status == "Open"
        assert risk.priority == "High"

    def test_risk_score_calculation(self):
        """Test risk score calculation."""
        risk = Risk(probability="High", impact="High")
        # Note: The actual score calculation would depend on implementation
        # This is a placeholder for when score calculation is implemented
        assert hasattr(risk, 'score')


@pytest.mark.unit
class TestActionModel:
    """Test Action model functionality."""

    def test_action_creation(self, db_session: Session):
        """Test creating an action."""
        # First create a project
        project = Project(
            id="project-id",
            name="Test Project",
            owner="user-id"
        )
        db_session.add(project)
        db_session.commit()
        
        action = Action(
            id="action-id",
            projectId="project-id",
            title="Test Action",
            description="Test description",
            category="Process",
            status="Open",
            priority="Medium",
            owner="user-id",
            assignee="user-id",
            progress=25
        )
        
        db_session.add(action)
        db_session.commit()
        
        assert action.id == "action-id"
        assert action.projectId == "project-id"
        assert action.title == "Test Action"
        assert action.status == "Open"
        assert action.priority == "Medium"
        assert action.progress == 25


@pytest.mark.unit
class TestIssueModel:
    """Test Issue model functionality."""

    def test_issue_creation(self, db_session: Session):
        """Test creating an issue."""
        # First create a project
        project = Project(
            id="project-id",
            name="Test Project",
            owner="user-id"
        )
        db_session.add(project)
        db_session.commit()
        
        issue = Issue(
            id="issue-id",
            projectId="project-id",
            title="Test Issue",
            description="Test description",
            category="Bug",
            severity="Medium",
            status="Open",
            owner="user-id",
            priority="Medium"
        )
        
        db_session.add(issue)
        db_session.commit()
        
        assert issue.id == "issue-id"
        assert issue.projectId == "project-id"
        assert issue.title == "Test Issue"
        assert issue.severity == "Medium"
        assert issue.status == "Open"


@pytest.mark.unit
class TestDecisionModel:
    """Test Decision model functionality."""

    def test_decision_creation(self, db_session: Session):
        """Test creating a decision."""
        # First create a project
        project = Project(
            id="project-id",
            name="Test Project",
            owner="user-id"
        )
        db_session.add(project)
        db_session.commit()
        
        decision = Decision(
            id="decision-id",
            projectId="project-id",
            title="Test Decision",
            description="Test description",
            rationale="Test rationale",
            status="Proposed",
            priority="Medium",
            category="Strategic",
            decidedBy="user-id"
        )
        
        db_session.add(decision)
        db_session.commit()
        
        assert decision.id == "decision-id"
        assert decision.projectId == "project-id"
        assert decision.title == "Test Decision"
        assert decision.status == "Proposed"
        assert decision.category == "Strategic"


@pytest.mark.unit
class TestAuditModel:
    """Test Audit model functionality."""

    def test_audit_entry_creation(self, db_session: Session):
        """Test creating an audit entry."""
        audit_entry = AuditEntry(
            id="audit-id",
            itemType="project",
            itemId="project-id",
            action="create",
            changeSet={"name": {"to": "Test Project"}},
            changedBy="user-id",
            reason="Project created"
        )
        
        db_session.add(audit_entry)
        db_session.commit()
        
        assert audit_entry.id == "audit-id"
        assert audit_entry.itemType == "project"
        assert audit_entry.itemId == "project-id"
        assert audit_entry.action == "create"
        assert audit_entry.changedBy == "user-id"
        assert audit_entry.changedAt is not None
