"""Unit tests for service layer."""

import pytest
from unittest.mock import Mo<PERSON>, <PERSON><PERSON><PERSON>
from sqlalchemy.orm import Session

from app.services.audit import AuditService
from app.models.audit import AuditEntry
from app.models.user import User
from app.models.project import Project


@pytest.mark.unit
class TestAuditService:
    """Test AuditService functionality."""

    def test_calculate_change_set_creation(self):
        """Test change set calculation for creation."""
        old_values = None
        new_values = {"name": "Test Project", "status": "active"}
        
        change_set = AuditService._calculate_change_set(old_values, new_values)
        
        assert change_set == new_values

    def test_calculate_change_set_update(self):
        """Test change set calculation for update."""
        old_values = {"name": "Old Project", "status": "active", "description": "Old desc"}
        new_values = {"name": "New Project", "status": "inactive", "description": "Old desc"}
        
        change_set = AuditService._calculate_change_set(old_values, new_values)
        
        expected = {
            "name": {"from": "Old Project", "to": "New Project"},
            "status": {"from": "active", "to": "inactive"}
        }
        assert change_set == expected

    def test_calculate_change_set_deletion(self):
        """Test change set calculation for deletion."""
        old_values = {"name": "Test Project", "status": "active"}
        new_values = None
        
        change_set = AuditService._calculate_change_set(old_values, new_values)
        
        expected = {
            "name": {"from": "Test Project", "to": None},
            "status": {"from": "active", "to": None}
        }
        assert change_set == expected

    def test_calculate_change_set_no_changes(self):
        """Test change set calculation when no changes."""
        old_values = {"name": "Test Project", "status": "active"}
        new_values = {"name": "Test Project", "status": "active"}
        
        change_set = AuditService._calculate_change_set(old_values, new_values)
        
        assert change_set == {}

    def test_model_to_dict(self):
        """Test converting model to dictionary."""
        project = Project(
            id="test-id",
            name="Test Project",
            status="active",
            owner="user-id"
        )
        
        result = AuditService._model_to_dict(project)
        
        assert result["id"] == "test-id"
        assert result["name"] == "Test Project"
        assert result["status"] == "active"
        assert result["owner"] == "user-id"
        assert "_sa_instance_state" not in result

    def test_get_client_ip_forwarded_for(self):
        """Test extracting client IP from X-Forwarded-For header."""
        mock_request = Mock()
        mock_request.headers = {"x-forwarded-for": "***********, ********"}
        
        ip = AuditService._get_client_ip(mock_request)
        
        assert ip == "***********"

    def test_get_client_ip_real_ip(self):
        """Test extracting client IP from X-Real-IP header."""
        mock_request = Mock()
        mock_request.headers = {"x-real-ip": "***********"}
        
        ip = AuditService._get_client_ip(mock_request)
        
        assert ip == "***********"

    def test_get_client_ip_direct(self):
        """Test extracting client IP from request.client."""
        mock_request = Mock()
        mock_request.headers = {}
        mock_request.client = Mock()
        mock_request.client.host = "***********"
        
        ip = AuditService._get_client_ip(mock_request)
        
        assert ip == "***********"

    def test_get_client_ip_none(self):
        """Test when no client IP is available."""
        mock_request = Mock()
        mock_request.headers = {}
        mock_request.client = None
        
        ip = AuditService._get_client_ip(mock_request)
        
        assert ip is None

    def test_create_audit_entry(self, db_session: Session):
        """Test creating an audit entry."""
        audit_entry = AuditService.create_audit_entry(
            db=db_session,
            item_type="project",
            item_id="project-id",
            action="create",
            changed_by="user-id",
            new_values={"name": "Test Project", "status": "active"},
            reason="Project created"
        )
        
        assert audit_entry.itemType == "project"
        assert audit_entry.itemId == "project-id"
        assert audit_entry.action == "create"
        assert audit_entry.changedBy == "user-id"
        assert audit_entry.reason == "Project created"
        assert audit_entry.changeSet == {"name": "Test Project", "status": "active"}

    def test_audit_create(self, db_session: Session):
        """Test audit_create helper method."""
        user = User(id="user-id", username="testuser", email="<EMAIL>")
        project = Project(
            id="project-id",
            name="Test Project",
            status="active",
            owner="user-id"
        )
        
        audit_entry = AuditService.audit_create(
            db=db_session,
            item_type="project",
            item=project,
            user=user,
            reason="Project created"
        )
        
        assert audit_entry.action == "create"
        assert audit_entry.itemType == "project"
        assert audit_entry.itemId == "project-id"
        assert audit_entry.changedBy == "user-id"

    def test_audit_update(self, db_session: Session):
        """Test audit_update helper method."""
        user = User(id="user-id", username="testuser", email="<EMAIL>")
        old_values = {"name": "Old Project", "status": "active"}
        new_project = Project(
            id="project-id",
            name="New Project",
            status="inactive",
            owner="user-id"
        )
        
        audit_entry = AuditService.audit_update(
            db=db_session,
            item_type="project",
            item_id="project-id",
            old_values=old_values,
            new_item=new_project,
            user=user,
            reason="Project updated"
        )
        
        assert audit_entry.action == "update"
        assert audit_entry.itemType == "project"
        assert audit_entry.itemId == "project-id"
        assert audit_entry.changedBy == "user-id"
        assert "name" in audit_entry.changeSet

    def test_audit_delete(self, db_session: Session):
        """Test audit_delete helper method."""
        user = User(id="user-id", username="testuser", email="<EMAIL>")
        old_values = {"name": "Test Project", "status": "active"}
        
        audit_entry = AuditService.audit_delete(
            db=db_session,
            item_type="project",
            item_id="project-id",
            old_values=old_values,
            user=user,
            reason="Project deleted"
        )
        
        assert audit_entry.action == "delete"
        assert audit_entry.itemType == "project"
        assert audit_entry.itemId == "project-id"
        assert audit_entry.changedBy == "user-id"
        assert audit_entry.oldValues == old_values

    def test_get_audit_history(self, db_session: Session):
        """Test getting audit history."""
        # Create some audit entries
        audit1 = AuditEntry(
            id="audit-1",
            itemType="project",
            itemId="project-1",
            action="create",
            changeSet={},
            changedBy="user-1"
        )
        audit2 = AuditEntry(
            id="audit-2",
            itemType="project",
            itemId="project-2",
            action="update",
            changeSet={},
            changedBy="user-2"
        )
        
        db_session.add_all([audit1, audit2])
        db_session.commit()
        
        # Test getting all history
        history = AuditService.get_audit_history(db=db_session)
        assert len(history) == 2
        
        # Test filtering by item type
        history = AuditService.get_audit_history(db=db_session, item_type="project")
        assert len(history) == 2
        
        # Test filtering by item ID
        history = AuditService.get_audit_history(db=db_session, item_id="project-1")
        assert len(history) == 1
        assert history[0].itemId == "project-1"
        
        # Test filtering by user
        history = AuditService.get_audit_history(db=db_session, user_id="user-1")
        assert len(history) == 1
        assert history[0].changedBy == "user-1"
        
        # Test filtering by action
        history = AuditService.get_audit_history(db=db_session, action="create")
        assert len(history) == 1
        assert history[0].action == "create"
        
        # Test limit and offset
        history = AuditService.get_audit_history(db=db_session, limit=1, offset=0)
        assert len(history) == 1
        
        history = AuditService.get_audit_history(db=db_session, limit=1, offset=1)
        assert len(history) == 1
