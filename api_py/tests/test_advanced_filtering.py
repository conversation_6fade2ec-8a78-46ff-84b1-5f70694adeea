"""Tests for advanced filtering functionality."""

import pytest
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

from app.models import Risk, Action, Issue, Decision, SavedFilter, User, Project
from app.services.filtering import AdvancedFilterService
from app.schemas.common import FilterParams


@pytest.fixture
def sample_project(db_session: Session, test_user: User):
    """Create a sample project for testing."""
    project = Project(
        id="test-project-id",
        name="Test Project",
        description="A test project",
        cadence="weekly",
        categories=["assumptions", "dependencies"],
        owner=test_user.id,
        status="active"
    )
    db_session.add(project)
    db_session.commit()
    db_session.refresh(project)
    return project


@pytest.fixture
def sample_risks(db_session: Session, sample_project: Project):
    """Create sample risks for testing."""
    risks = [
        Risk(
            id="risk-1",
            title="High Priority Technical Risk",
            description="A critical technical risk",
            projectId=sample_project.id,
            priority="High",
            status="Open",
            severity="Critical",
            owner="<EMAIL>",
            probability=0.8,
            impact=4,
            score=3.2
        ),
        Risk(
            id="risk-2",
            title="Low Priority Risk",
            description="A minor risk",
            projectId=sample_project.id,
            priority="Low",
            status="Closed",
            severity="Low",
            owner="<EMAIL>",
            probability=0.2,
            impact=2,
            score=0.4
        ),
        Risk(
            id="risk-3",
            title="Medium Risk",
            description="A moderate risk with technical issues",
            projectId=sample_project.id,
            priority="Medium",
            status="In progress",
            severity="Medium",
            owner="<EMAIL>",
            probability=0.5,
            impact=3,
            score=1.5
        )
    ]
    
    for risk in risks:
        db_session.add(risk)
    db_session.commit()

    for risk in risks:
        db_session.refresh(risk)
    
    return risks


class TestAdvancedFilterService:
    """Test cases for AdvancedFilterService."""
    
    def test_text_search_contains(self, db_session: Session, sample_risks):
        """Test text search with contains mode."""
        filters = FilterParams(q="technical", search_mode="contains")
        query = db_session.query(Risk).filter(Risk.projectId == sample_risks[0].projectId)
        
        filtered_query = AdvancedFilterService.apply_advanced_filters(query, Risk, filters)
        results = filtered_query.all()
        
        assert len(results) == 2  # "High Priority Technical Risk" and "Medium Risk"
        titles = [r.title for r in results]
        assert "High Priority Technical Risk" in titles
        assert "Medium Risk" in titles
    
    def test_text_search_exact(self, db_session: Session, sample_risks):
        """Test text search with exact mode."""
        filters = FilterParams(q="technical", search_mode="exact")
        query = db_session.query(Risk).filter(Risk.projectId == sample_risks[0].projectId)

        filtered_query = AdvancedFilterService.apply_advanced_filters(query, Risk, filters)
        results = filtered_query.all()

        assert len(results) == 2  # Both have category="technical"

    def test_text_search_case_sensitive(self, db_session: Session, sample_risks):
        """Test case-sensitive text search."""
        filters = FilterParams(q="Technical", case_sensitive=True)
        query = db_session.query(Risk).filter(Risk.projectId == sample_risks[0].projectId)

        filtered_query = AdvancedFilterService.apply_advanced_filters(query, Risk, filters)
        results = filtered_query.all()

        assert len(results) == 0  # No exact case match

    def test_multi_value_filters(self, db_session: Session, sample_risks):
        """Test filtering with multiple values."""
        filters = FilterParams(
            status=["Open", "In progress"],
            priority=["High", "Medium"]
        )
        query = db_session.query(Risk).filter(Risk.projectId == sample_risks[0].projectId)
        
        filtered_query = AdvancedFilterService.apply_advanced_filters(query, Risk, filters)
        results = filtered_query.all()
        
        assert len(results) == 2  # High priority (Open) and Medium (In progress)
        for result in results:
            assert result.status in ["Open", "In progress"]
            assert result.priority in ["High", "Medium"]
    
    def test_score_range_filter(self, db_session: Session, sample_risks):
        """Test score range filtering."""
        filters = FilterParams(score_min=1.0, score_max=2.0)
        query = db_session.query(Risk).filter(Risk.projectId == sample_risks[0].projectId)

        filtered_query = AdvancedFilterService.apply_advanced_filters(query, Risk, filters)
        results = filtered_query.all()

        assert len(results) == 1  # Only "Medium Risk" with score=1.5
        assert results[0].title == "Medium Risk"

    def test_date_range_filter(self, db_session: Session, sample_risks):
        """Test date range filtering."""
        # Set specific dates for testing
        yesterday = datetime.now() - timedelta(days=1)
        tomorrow = datetime.now() + timedelta(days=1)

        filters = FilterParams(
            created_after=yesterday.isoformat(),
            created_before=tomorrow.isoformat()
        )
        query = db_session.query(Risk).filter(Risk.projectId == sample_risks[0].projectId)

        filtered_query = AdvancedFilterService.apply_advanced_filters(query, Risk, filters)
        results = filtered_query.all()

        assert len(results) == 3  # All risks created today

    def test_sorting(self, db_session: Session, sample_risks):
        """Test sorting functionality."""
        # Test ascending sort by score
        filters = FilterParams(sort_by="score", sort_order="asc")
        query = db_session.query(Risk).filter(Risk.projectId == sample_risks[0].projectId)

        filtered_query = AdvancedFilterService.apply_advanced_filters(query, Risk, filters)
        results = filtered_query.all()

        scores = [r.score for r in results]
        assert scores == sorted(scores)  # Should be in ascending order

        # Test descending sort by score
        filters = FilterParams(sort_by="score", sort_order="desc")
        query = db_session.query(Risk).filter(Risk.projectId == sample_risks[0].projectId)

        filtered_query = AdvancedFilterService.apply_advanced_filters(query, Risk, filters)
        results = filtered_query.all()

        scores = [r.score for r in results]
        assert scores == sorted(scores, reverse=True)  # Should be in descending order
    
    def test_get_filter_options(self, db_session: Session, sample_risks, sample_project):
        """Test getting available filter options."""
        options = AdvancedFilterService.get_filter_options(db_session, sample_project.id, Risk)

        assert "open" in options["status"]
        assert "closed" in options["status"]
        assert "in_progress" in options["status"]

        assert "high" in options["priority"]
        assert "medium" in options["priority"]
        assert "low" in options["priority"]

        assert "technical" in options["category"]
        assert "business" in options["category"]

    def test_get_search_suggestions(self, db_session: Session, sample_risks, sample_project):
        """Test getting search suggestions."""
        suggestions = AdvancedFilterService.get_search_suggestions(
            db_session, sample_project.id, "tech", Risk, limit=5
        )

        # Should find suggestions containing "tech"
        suggestion_texts = [s.text for s in suggestions]
        assert any("technical" in text.lower() for text in suggestion_texts)

    def test_save_and_load_filter(self, db_session: Session, test_user, sample_project):
        """Test saving and loading filters."""
        filter_data = {
            "status": ["open", "in_progress"],
            "priority": ["high"],
            "search": "technical"
        }

        # Save filter
        saved_filter = AdvancedFilterService.save_filter(
            db=db_session,
            user_id=test_user.id,
            name="My Technical Risks",
            description="Filter for technical risks",
            filter_data=filter_data,
            project_id=sample_project.id,
            is_public=False
        )

        assert saved_filter.name == "My Technical Risks"
        assert saved_filter.filter_data == filter_data
        assert saved_filter.created_by == test_user.id
        assert saved_filter.project_id == sample_project.id

        # Load filters
        loaded_filters = AdvancedFilterService.get_saved_filters(
            db=db_session,
            user_id=test_user.id,
            project_id=sample_project.id
        )

        assert len(loaded_filters) == 1
        assert loaded_filters[0].name == "My Technical Risks"
    
    def test_public_filter_visibility(self, db_session: Session, test_user, sample_project):
        """Test public filter visibility."""
        # Create another user
        other_user = User(
            username="otheruser",
            email="<EMAIL>",
            hashedPassword="hashed",
            role="contributor"
        )
        db_session.add(other_user)
        db_session.commit()
        db_session.refresh(other_user)

        # Create a public filter
        public_filter = AdvancedFilterService.save_filter(
            db=db_session,
            user_id=test_user.id,
            name="Public Filter",
            filter_data={"status": ["open"]},
            project_id=sample_project.id,
            is_public=True
        )

        # Create a private filter
        private_filter = AdvancedFilterService.save_filter(
            db=db_session,
            user_id=test_user.id,
            name="Private Filter",
            filter_data={"status": ["closed"]},
            project_id=sample_project.id,
            is_public=False
        )

        # Other user should see public filter but not private
        other_user_filters = AdvancedFilterService.get_saved_filters(
            db=db_session,
            user_id=other_user.id,
            project_id=sample_project.id
        )

        filter_names = [f.name for f in other_user_filters]
        assert "Public Filter" in filter_names
        assert "Private Filter" not in filter_names
