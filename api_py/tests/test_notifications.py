"""Tests for notification system."""

import pytest
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

from app.models.user import User
from app.models.project import Project
from app.models.risk import Risk
from app.models.notification import Notification, NotificationTemplate, NotificationPreference
from app.services.notification_service import NotificationService
from app.services.notification_events import NotificationEventHandler
from app.services.notification_templates import create_default_templates
from app.schemas.notification import (
    NotificationEventType, NotificationPriority, NotificationType, NotificationStatus
)


@pytest.fixture
def notification_service(db_session: Session):
    """Create notification service."""
    return NotificationService(db_session)


@pytest.fixture
def notification_handler(db_session: Session):
    """Create notification event handler."""
    return NotificationEventHandler(db_session)


@pytest.fixture
def sample_template(db_session: Session):
    """Create a sample notification template."""
    template = NotificationTemplate(
        name="Test Template",
        type=NotificationType.BOTH.value,
        event_type=NotificationEventType.CREATED.value,
        subject_template="Test: {{ title }}",
        body_template="A test notification for {{ title }}",
        html_template="<h3>Test</h3><p>{{ title }}</p>",
        priority=NotificationPriority.NORMAL.value,
        variables=["title"]
    )
    db_session.add(template)
    db_session.commit()
    db_session.refresh(template)
    return template


@pytest.fixture
def sample_preference(db_session: Session, test_user: User):
    """Create a sample notification preference."""
    preference = NotificationPreference(
        user_id=test_user.id,
        event_type=NotificationEventType.CREATED.value,
        email_enabled=True,
        in_app_enabled=True,
        immediate=True,
        priority_threshold=NotificationPriority.LOW.value
    )
    db_session.add(preference)
    db_session.commit()
    db_session.refresh(preference)
    return preference


class TestNotificationService:
    """Test cases for NotificationService."""

    def test_create_notification(self, notification_service: NotificationService, test_user: User, sample_project: Project):
        """Test creating a notification."""
        notification = notification_service.create_notification(
            recipient_id=test_user.id,
            event_type=NotificationEventType.CREATED,
            subject="Test Notification",
            message="This is a test notification",
            project_id=sample_project.id,
            priority=NotificationPriority.NORMAL
        )

        assert notification is not None
        assert notification.recipient_id == test_user.id
        assert notification.subject == "Test Notification"
        assert notification.message == "This is a test notification"
        assert notification.project_id == sample_project.id
        assert notification.priority == NotificationPriority.NORMAL.value
        assert notification.status == NotificationStatus.PENDING.value

    def test_create_from_template(
        self, 
        notification_service: NotificationService, 
        test_user: User, 
        sample_template: NotificationTemplate
    ):
        """Test creating notification from template."""
        variables = {"title": "Test Item"}
        
        notification = notification_service.create_from_template(
            template_id=sample_template.id,
            recipient_id=test_user.id,
            variables=variables
        )

        assert notification is not None
        assert notification.subject == "Test: Test Item"
        assert notification.message == "A test notification for Test Item"
        assert notification.html_content == "<h3>Test</h3><p>Test Item</p>"

    def test_mark_as_read(self, notification_service: NotificationService, test_user: User):
        """Test marking notification as read."""
        notification = notification_service.create_notification(
            recipient_id=test_user.id,
            event_type=NotificationEventType.CREATED,
            subject="Test",
            message="Test message"
        )

        success = notification_service.mark_as_read(notification.id, test_user.id)
        assert success is True

        # Refresh notification
        notification_service.db.refresh(notification)
        assert notification.status == NotificationStatus.READ.value
        assert notification.read_at is not None

    def test_mark_all_as_read(self, notification_service: NotificationService, test_user: User, sample_project: Project):
        """Test marking all notifications as read."""
        # Create multiple notifications
        for i in range(3):
            notification_service.create_notification(
                recipient_id=test_user.id,
                event_type=NotificationEventType.CREATED,
                subject=f"Test {i}",
                message=f"Test message {i}",
                project_id=sample_project.id
            )

        count = notification_service.mark_all_as_read(test_user.id, sample_project.id)
        assert count == 3

    def test_get_user_notifications(self, notification_service: NotificationService, test_user: User):
        """Test getting user notifications."""
        # Create notifications
        for i in range(5):
            notification_service.create_notification(
                recipient_id=test_user.id,
                event_type=NotificationEventType.CREATED,
                subject=f"Test {i}",
                message=f"Test message {i}"
            )

        notifications = notification_service.get_user_notifications(test_user.id, limit=3)
        assert len(notifications) == 3

        # Test unread only
        notification_service.mark_as_read(notifications[0].id, test_user.id)
        unread_notifications = notification_service.get_user_notifications(test_user.id, unread_only=True)
        assert len(unread_notifications) == 4

    def test_user_preferences(self, notification_service: NotificationService, test_user: User, sample_preference: NotificationPreference):
        """Test getting user preferences."""
        preference = notification_service.get_user_preferences(
            test_user.id, 
            NotificationEventType.CREATED
        )

        assert preference is not None
        assert preference.user_id == test_user.id
        assert preference.event_type == NotificationEventType.CREATED.value
        assert preference.email_enabled is True
        assert preference.in_app_enabled is True


class TestNotificationEventHandler:
    """Test cases for NotificationEventHandler."""

    def test_on_risk_created(self, notification_handler: NotificationEventHandler, db_session: Session, test_user: User, sample_project: Project):
        """Test risk creation event handling."""
        risk = Risk(
            id="test-risk-id",
            title="Test Risk",
            description="A test risk",
            projectId=sample_project.id,
            priority="High",
            status="Open",
            severity="Critical",
            owner=test_user.id,
            probability=0.8,
            impact=4,
            score=3.2
        )
        db_session.add(risk)
        db_session.commit()

        notification_handler.on_risk_created(risk, test_user.id)

        # Check that notifications were created
        notifications = db_session.query(Notification).filter(
            Notification.entity_type == "risk",
            Notification.entity_id == risk.id
        ).all()

        assert len(notifications) >= 0  # May be 0 if no other users or preferences filter them out

    def test_on_risk_updated(self, notification_handler: NotificationEventHandler, db_session: Session, test_user: User, sample_project: Project):
        """Test risk update event handling."""
        risk = Risk(
            id="test-risk-id",
            title="Test Risk",
            description="A test risk",
            projectId=sample_project.id,
            priority="High",
            status="Open",
            severity="Critical",
            owner=test_user.id,
            probability=0.8,
            impact=4,
            score=3.2
        )
        db_session.add(risk)
        db_session.commit()

        changes = {"status": "Closed", "priority": "Medium"}
        notification_handler.on_risk_updated(risk, test_user.id, changes)

        # Check that notifications were created for status and priority changes
        notifications = db_session.query(Notification).filter(
            Notification.entity_type == "risk",
            Notification.entity_id == risk.id
        ).all()

        # Should have notifications for status and priority changes
        event_types = [n.event_type for n in notifications]
        assert NotificationEventType.STATUS_CHANGED.value in event_types or len(notifications) == 0
        assert NotificationEventType.PRIORITY_CHANGED.value in event_types or len(notifications) == 0


class TestNotificationTemplates:
    """Test cases for notification templates."""

    def test_create_default_templates(self, db_session: Session):
        """Test creating default templates."""
        create_default_templates(db_session)

        templates = db_session.query(NotificationTemplate).all()
        assert len(templates) > 0

        # Check specific templates exist
        risk_created = db_session.query(NotificationTemplate).filter(
            NotificationTemplate.name == "Risk Created"
        ).first()
        assert risk_created is not None
        assert risk_created.event_type == NotificationEventType.CREATED.value

        action_overdue = db_session.query(NotificationTemplate).filter(
            NotificationTemplate.name == "Action Overdue"
        ).first()
        assert action_overdue is not None
        assert action_overdue.priority == NotificationPriority.URGENT.value

    def test_template_rendering(self, notification_service: NotificationService, test_user: User, sample_template: NotificationTemplate):
        """Test template variable rendering."""
        variables = {"title": "My Test Item"}
        
        notification = notification_service.create_from_template(
            template_id=sample_template.id,
            recipient_id=test_user.id,
            variables=variables
        )

        assert "My Test Item" in notification.subject
        assert "My Test Item" in notification.message
        assert "My Test Item" in notification.html_content


class TestNotificationPreferences:
    """Test cases for notification preferences."""

    def test_preference_filtering(self, notification_service: NotificationService, db_session: Session, test_user: User):
        """Test that preferences filter notifications correctly."""
        # Create preference that disables email notifications
        preference = NotificationPreference(
            user_id=test_user.id,
            event_type=NotificationEventType.CREATED.value,
            email_enabled=False,
            in_app_enabled=True,
            priority_threshold=NotificationPriority.HIGH.value  # Only high priority and above
        )
        db_session.add(preference)
        db_session.commit()

        # Try to create low priority notification - should be filtered out
        notification = notification_service.create_notification(
            recipient_id=test_user.id,
            event_type=NotificationEventType.CREATED,
            subject="Low Priority Test",
            message="This should be filtered",
            priority=NotificationPriority.LOW,
            notification_type=NotificationType.EMAIL
        )

        # Should be None due to priority threshold
        assert notification is None

        # Try to create high priority notification - should be created
        notification = notification_service.create_notification(
            recipient_id=test_user.id,
            event_type=NotificationEventType.CREATED,
            subject="High Priority Test",
            message="This should be created",
            priority=NotificationPriority.HIGH,
            notification_type=NotificationType.IN_APP
        )

        assert notification is not None
