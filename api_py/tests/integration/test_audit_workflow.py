"""End-to-end tests for audit workflow."""

import pytest
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.orm import Session

from app.models.project import Project


@pytest.mark.integration
@pytest.mark.audit
@pytest.mark.slow
class TestAuditWorkflow:
    """Test complete audit workflow from CRUD operations to audit history."""

    def test_project_lifecycle_audit(self, client: TestClient, auth_headers: dict, 
                                    admin_auth_headers: dict, sample_project_data: dict):
        """Test complete project lifecycle with audit tracking."""
        
        # 1. Create project and verify audit entry
        create_response = client.post("/api/v1/projects/", 
                                    json=sample_project_data, headers=auth_headers)
        assert create_response.status_code == 200
        project_data = create_response.json()
        project_id = project_data["id"]
        
        # Check audit entry for creation
        audit_response = client.get(f"/api/v1/audit/item/project/{project_id}", 
                                  headers=auth_headers)
        assert audit_response.status_code == 200
        audit_data = audit_response.json()
        assert len(audit_data) >= 1
        
        create_audit = audit_data[0]
        assert create_audit["action"] == "create"
        assert create_audit["itemType"] == "project"
        assert create_audit["itemId"] == project_id
        assert create_audit["reason"] == "Project created"
        
        # 2. Update project and verify audit entry
        update_data = {
            "name": "Updated Project Name",
            "description": "Updated description"
        }
        update_response = client.put(f"/api/v1/projects/{project_id}", 
                                   json=update_data, headers=auth_headers)
        assert update_response.status_code == 200
        
        # Check audit entry for update
        audit_response = client.get(f"/api/v1/audit/item/project/{project_id}", 
                                  headers=auth_headers)
        assert audit_response.status_code == 200
        audit_data = audit_response.json()
        assert len(audit_data) >= 2
        
        # Find the update audit entry (should be first due to ordering)
        update_audit = next((entry for entry in audit_data if entry["action"] == "update"), None)
        assert update_audit is not None
        assert update_audit["action"] == "update"
        assert update_audit["reason"] == "Project updated"
        assert "name" in update_audit["changeSet"]
        assert update_audit["changeSet"]["name"]["from"] == sample_project_data["name"]
        assert update_audit["changeSet"]["name"]["to"] == "Updated Project Name"
        
        # 3. Delete project and verify audit entry
        delete_response = client.delete(f"/api/v1/projects/{project_id}", 
                                      headers=admin_auth_headers)
        assert delete_response.status_code == 200
        
        # Check audit entry for deletion
        audit_response = client.get(f"/api/v1/audit/item/project/{project_id}", 
                                  headers=auth_headers)
        assert audit_response.status_code == 200
        audit_data = audit_response.json()
        assert len(audit_data) >= 3
        
        # Find the delete audit entry
        delete_audit = next((entry for entry in audit_data if entry["action"] == "delete"), None)
        assert delete_audit is not None
        assert delete_audit["action"] == "delete"
        assert delete_audit["reason"] == "Project deleted"
        assert delete_audit["oldValues"] is not None

    def test_audit_change_tracking(self, client: TestClient, auth_headers: dict, 
                                 sample_project_data: dict):
        """Test detailed change tracking in audit entries."""
        
        # Create project
        create_response = client.post("/api/v1/projects/", 
                                    json=sample_project_data, headers=auth_headers)
        assert create_response.status_code == 200
        project_id = create_response.json()["id"]
        
        # Update multiple fields
        update_data = {
            "name": "New Name",
            "description": "New Description",
            "status": "inactive",
            "cadence": "monthly"
        }
        update_response = client.put(f"/api/v1/projects/{project_id}", 
                                   json=update_data, headers=auth_headers)
        assert update_response.status_code == 200
        
        # Get audit history
        audit_response = client.get(f"/api/v1/audit/item/project/{project_id}", 
                                  headers=auth_headers)
        assert audit_response.status_code == 200
        audit_data = audit_response.json()
        
        # Find update audit entry
        update_audit = next((entry for entry in audit_data if entry["action"] == "update"), None)
        assert update_audit is not None
        
        # Verify all changed fields are tracked
        change_set = update_audit["changeSet"]
        assert "name" in change_set
        assert "description" in change_set
        assert "status" in change_set
        assert "cadence" in change_set
        
        # Verify change details
        assert change_set["name"]["from"] == sample_project_data["name"]
        assert change_set["name"]["to"] == "New Name"
        assert change_set["status"]["from"] == sample_project_data["status"]
        assert change_set["status"]["to"] == "inactive"

    def test_audit_user_tracking(self, client: TestClient, auth_headers: dict, 
                               admin_auth_headers: dict, sample_project_data: dict):
        """Test user tracking in audit entries."""
        
        # Create project with regular user
        create_response = client.post("/api/v1/projects/", 
                                    json=sample_project_data, headers=auth_headers)
        assert create_response.status_code == 200
        project_id = create_response.json()["id"]
        
        # Update project with admin user
        update_data = {"description": "Updated by admin"}
        update_response = client.put(f"/api/v1/projects/{project_id}", 
                                   json=update_data, headers=admin_auth_headers)
        assert update_response.status_code == 200
        
        # Get audit history
        audit_response = client.get(f"/api/v1/audit/item/project/{project_id}", 
                                  headers=auth_headers)
        assert audit_response.status_code == 200
        audit_data = audit_response.json()
        
        # Verify different users are tracked
        create_audit = next((entry for entry in audit_data if entry["action"] == "create"), None)
        update_audit = next((entry for entry in audit_data if entry["action"] == "update"), None)
        
        assert create_audit["changedBy"] == "test-user-id"
        assert update_audit["changedBy"] == "admin-user-id"

    def test_audit_filtering_and_search(self, client: TestClient, auth_headers: dict, 
                                      admin_auth_headers: dict, sample_project_data: dict):
        """Test audit filtering and search capabilities."""
        
        # Create multiple projects with different users
        projects = []
        for i in range(3):
            project_data = sample_project_data.copy()
            project_data["name"] = f"Test Project {i}"
            
            headers = auth_headers if i % 2 == 0 else admin_auth_headers
            response = client.post("/api/v1/projects/", json=project_data, headers=headers)
            assert response.status_code == 200
            projects.append(response.json())
        
        # Test filtering by item type
        response = client.get("/api/v1/audit/?item_type=project", headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert len(data) >= 3
        for entry in data:
            assert entry["itemType"] == "project"
        
        # Test filtering by action
        response = client.get("/api/v1/audit/?action=create", headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert len(data) >= 3
        for entry in data:
            assert entry["action"] == "create"
        
        # Test filtering by user
        response = client.get("/api/v1/audit/?user_id=test-user-id", headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert len(data) >= 2  # At least 2 projects created by test user
        for entry in data:
            assert entry["changedBy"] == "test-user-id"
        
        # Test user-specific audit history
        response = client.get("/api/v1/audit/user/admin-user-id", headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert len(data) >= 1  # At least 1 project created by admin
        for entry in data:
            assert entry["changedBy"] == "admin-user-id"

    def test_audit_recent_changes(self, client: TestClient, auth_headers: dict, 
                                sample_project_data: dict):
        """Test recent changes functionality."""
        
        # Create and update projects to generate recent activity
        projects = []
        for i in range(3):
            project_data = sample_project_data.copy()
            project_data["name"] = f"Recent Project {i}"
            
            response = client.post("/api/v1/projects/", json=project_data, headers=auth_headers)
            assert response.status_code == 200
            projects.append(response.json())
        
        # Update one project
        update_data = {"description": "Recently updated"}
        response = client.put(f"/api/v1/projects/{projects[0]['id']}", 
                            json=update_data, headers=auth_headers)
        assert response.status_code == 200
        
        # Get recent changes
        response = client.get("/api/v1/audit/recent?limit=10", headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        
        # Should have at least 4 entries (3 creates + 1 update)
        assert len(data) >= 4
        
        # Verify ordering (most recent first)
        timestamps = [entry["changedAt"] for entry in data]
        assert timestamps == sorted(timestamps, reverse=True)

    def test_audit_pagination_consistency(self, client: TestClient, auth_headers: dict, 
                                        sample_project_data: dict):
        """Test audit pagination consistency."""
        
        # Create multiple projects to generate audit entries
        for i in range(10):
            project_data = sample_project_data.copy()
            project_data["name"] = f"Pagination Project {i}"
            
            response = client.post("/api/v1/projects/", json=project_data, headers=auth_headers)
            assert response.status_code == 200
        
        # Get first page
        response1 = client.get("/api/v1/audit/?limit=5&offset=0", headers=auth_headers)
        assert response1.status_code == 200
        page1 = response1.json()
        assert len(page1) <= 5
        
        # Get second page
        response2 = client.get("/api/v1/audit/?limit=5&offset=5", headers=auth_headers)
        assert response2.status_code == 200
        page2 = response2.json()
        assert len(page2) <= 5
        
        # Verify no overlap between pages
        page1_ids = {entry["id"] for entry in page1}
        page2_ids = {entry["id"] for entry in page2}
        assert page1_ids.isdisjoint(page2_ids)
