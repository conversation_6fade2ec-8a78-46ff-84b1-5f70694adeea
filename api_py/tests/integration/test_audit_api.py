"""Integration tests for audit API."""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.models.audit import AuditEntry
from app.models.project import Project


@pytest.mark.integration
@pytest.mark.audit
class TestAuditAPI:
    """Test audit API endpoints."""

    def test_get_audit_history(self, client: TestClient, auth_headers: dict, 
                             db_session: Session):
        """Test getting audit history."""
        # Create test audit entries
        audit_entries = [
            AuditEntry(
                id="audit-1",
                itemType="project",
                itemId="project-1",
                action="create",
                changeSet={"name": {"to": "Project 1"}},
                changedBy="test-user-id"
            ),
            AuditEntry(
                id="audit-2",
                itemType="project",
                itemId="project-2",
                action="update",
                changeSet={"name": {"from": "Old Name", "to": "New Name"}},
                changedBy="test-user-id"
            ),
        ]
        db_session.add_all(audit_entries)
        db_session.commit()
        
        response = client.get("/api/v1/audit/", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 2

    def test_get_audit_history_with_filters(self, client: TestClient, auth_headers: dict, 
                                          db_session: Session):
        """Test getting audit history with filters."""
        # Create test audit entries
        audit_entries = [
            AuditEntry(
                id="audit-1",
                itemType="project",
                itemId="project-1",
                action="create",
                changeSet={},
                changedBy="user-1"
            ),
            AuditEntry(
                id="audit-2",
                itemType="risk",
                itemId="risk-1",
                action="update",
                changeSet={},
                changedBy="user-2"
            ),
        ]
        db_session.add_all(audit_entries)
        db_session.commit()
        
        # Filter by item type
        response = client.get("/api/v1/audit/?item_type=project", headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert len(data) >= 1
        for entry in data:
            assert entry["itemType"] == "project"
        
        # Filter by action
        response = client.get("/api/v1/audit/?action=create", headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert len(data) >= 1
        for entry in data:
            assert entry["action"] == "create"
        
        # Filter by user
        response = client.get("/api/v1/audit/?user_id=user-1", headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert len(data) >= 1
        for entry in data:
            assert entry["changedBy"] == "user-1"

    def test_get_item_audit_history(self, client: TestClient, auth_headers: dict, 
                                  db_session: Session):
        """Test getting audit history for a specific item."""
        # Create test audit entries for specific item
        audit_entries = [
            AuditEntry(
                id="audit-1",
                itemType="project",
                itemId="project-1",
                action="create",
                changeSet={"name": {"to": "Project 1"}},
                changedBy="test-user-id"
            ),
            AuditEntry(
                id="audit-2",
                itemType="project",
                itemId="project-1",
                action="update",
                changeSet={"description": {"from": "Old", "to": "New"}},
                changedBy="test-user-id"
            ),
            AuditEntry(
                id="audit-3",
                itemType="project",
                itemId="project-2",
                action="create",
                changeSet={},
                changedBy="test-user-id"
            ),
        ]
        db_session.add_all(audit_entries)
        db_session.commit()
        
        response = client.get("/api/v1/audit/item/project/project-1", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        for entry in data:
            assert entry["itemType"] == "project"
            assert entry["itemId"] == "project-1"

    def test_get_user_audit_history(self, client: TestClient, auth_headers: dict, 
                                  db_session: Session):
        """Test getting audit history for a specific user."""
        # Create test audit entries
        audit_entries = [
            AuditEntry(
                id="audit-1",
                itemType="project",
                itemId="project-1",
                action="create",
                changeSet={},
                changedBy="target-user"
            ),
            AuditEntry(
                id="audit-2",
                itemType="risk",
                itemId="risk-1",
                action="update",
                changeSet={},
                changedBy="target-user"
            ),
            AuditEntry(
                id="audit-3",
                itemType="project",
                itemId="project-2",
                action="create",
                changeSet={},
                changedBy="other-user"
            ),
        ]
        db_session.add_all(audit_entries)
        db_session.commit()
        
        response = client.get("/api/v1/audit/user/target-user", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        for entry in data:
            assert entry["changedBy"] == "target-user"

    def test_get_recent_changes(self, client: TestClient, auth_headers: dict, 
                              db_session: Session):
        """Test getting recent changes."""
        # Create test audit entries
        audit_entries = [
            AuditEntry(
                id="audit-1",
                itemType="project",
                itemId="project-1",
                action="create",
                changeSet={},
                changedBy="test-user-id"
            ),
            AuditEntry(
                id="audit-2",
                itemType="risk",
                itemId="risk-1",
                action="update",
                changeSet={},
                changedBy="test-user-id"
            ),
        ]
        db_session.add_all(audit_entries)
        db_session.commit()
        
        response = client.get("/api/v1/audit/recent", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 2

    def test_audit_pagination(self, client: TestClient, auth_headers: dict, 
                            db_session: Session):
        """Test audit history pagination."""
        # Create multiple audit entries
        audit_entries = [
            AuditEntry(
                id=f"audit-{i}",
                itemType="project",
                itemId=f"project-{i}",
                action="create",
                changeSet={},
                changedBy="test-user-id"
            )
            for i in range(15)
        ]
        db_session.add_all(audit_entries)
        db_session.commit()
        
        # Test with limit and offset
        response = client.get("/api/v1/audit/?limit=5&offset=0", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) <= 5
        
        # Test second page
        response = client.get("/api/v1/audit/?limit=5&offset=5", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) <= 5

    def test_audit_unauthorized(self, client: TestClient):
        """Test accessing audit endpoints without authentication."""
        response = client.get("/api/v1/audit/")
        assert response.status_code == 401
        
        response = client.get("/api/v1/audit/item/project/project-1")
        assert response.status_code == 401
        
        response = client.get("/api/v1/audit/user/user-1")
        assert response.status_code == 401
        
        response = client.get("/api/v1/audit/recent")
        assert response.status_code == 401

    def test_audit_entry_structure(self, client: TestClient, auth_headers: dict, 
                                 db_session: Session):
        """Test audit entry response structure."""
        audit_entry = AuditEntry(
            id="audit-1",
            itemType="project",
            itemId="project-1",
            action="create",
            changeSet={"name": {"to": "Test Project"}},
            oldValues=None,
            newValues={"name": "Test Project", "status": "active"},
            changedBy="test-user-id",
            reason="Project created",
            ip_address="***********",
            user_agent="TestAgent/1.0"
        )
        db_session.add(audit_entry)
        db_session.commit()
        
        response = client.get("/api/v1/audit/", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) >= 1
        
        entry = data[0]
        assert "id" in entry
        assert "itemType" in entry
        assert "itemId" in entry
        assert "action" in entry
        assert "changeSet" in entry
        assert "changedBy" in entry
        assert "changedAt" in entry
        assert "reason" in entry
        assert "ip_address" in entry
        assert "user_agent" in entry
