"""Integration tests for authentication API."""

import pytest
from fastapi.testclient import Test<PERSON><PERSON>
from sqlalchemy.orm import Session

from app.models.user import User


@pytest.mark.integration
@pytest.mark.auth
class TestAuthAPI:
    """Test authentication API endpoints."""

    def test_register_user(self, client: TestClient):
        """Test user registration."""
        user_data = {
            "username": "newuser",
            "email": "<EMAIL>",
            "password": "newpassword123",
            "full_name": "New User",
            "role": "contributor"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["username"] == "newuser"
        assert data["email"] == "<EMAIL>"
        assert data["full_name"] == "New User"
        assert data["role"] == "contributor"
        assert "password" not in data
        assert "hashed_password" not in data

    def test_register_duplicate_username(self, client: TestClient, test_user: User):
        """Test registering with duplicate username."""
        user_data = {
            "username": test_user.username,
            "email": "<EMAIL>",
            "password": "password123",
            "full_name": "Different User",
            "role": "contributor"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == 400
        assert "already exists" in response.json()["detail"]

    def test_register_duplicate_email(self, client: TestClient, test_user: User):
        """Test registering with duplicate email."""
        user_data = {
            "username": "differentuser",
            "email": test_user.email,
            "password": "password123",
            "full_name": "Different User",
            "role": "contributor"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == 400
        assert "already exists" in response.json()["detail"]

    def test_login_success(self, client: TestClient, test_user: User):
        """Test successful login."""
        login_data = {
            "username": test_user.username,
            "password": "testpassword123"
        }
        
        response = client.post("/api/v1/auth/login", data=login_data)
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"
        assert "user" in data
        assert data["user"]["username"] == test_user.username

    def test_login_invalid_username(self, client: TestClient):
        """Test login with invalid username."""
        login_data = {
            "username": "nonexistent",
            "password": "password123"
        }
        
        response = client.post("/api/v1/auth/login", data=login_data)
        
        assert response.status_code == 401
        assert "Invalid credentials" in response.json()["detail"]

    def test_login_invalid_password(self, client: TestClient, test_user: User):
        """Test login with invalid password."""
        login_data = {
            "username": test_user.username,
            "password": "wrongpassword"
        }
        
        response = client.post("/api/v1/auth/login", data=login_data)
        
        assert response.status_code == 401
        assert "Invalid credentials" in response.json()["detail"]

    def test_login_inactive_user(self, client: TestClient, db_session: Session):
        """Test login with inactive user."""
        # Create inactive user
        inactive_user = User(
            id="inactive-user",
            username="inactiveuser",
            email="<EMAIL>",
            full_name="Inactive User",
            hashed_password="hashed_password",
            role="contributor",
            is_active=False
        )
        db_session.add(inactive_user)
        db_session.commit()
        
        login_data = {
            "username": "inactiveuser",
            "password": "secret"
        }
        
        response = client.post("/api/v1/auth/login", data=login_data)
        
        assert response.status_code == 401
        assert "inactive" in response.json()["detail"].lower()

    def test_get_current_user(self, client: TestClient, auth_headers: dict):
        """Test getting current user information."""
        response = client.get("/api/v1/auth/me", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["username"] == "testuser"
        assert data["email"] == "<EMAIL>"

    def test_get_current_user_unauthorized(self, client: TestClient):
        """Test getting current user without authentication."""
        response = client.get("/api/v1/auth/me")
        
        assert response.status_code == 401

    def test_get_current_user_invalid_token(self, client: TestClient):
        """Test getting current user with invalid token."""
        headers = {"Authorization": "Bearer invalid_token"}
        response = client.get("/api/v1/auth/me", headers=headers)
        
        assert response.status_code == 401

    def test_refresh_token(self, client: TestClient, auth_headers: dict):
        """Test token refresh."""
        response = client.post("/api/v1/auth/refresh", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"

    def test_change_password(self, client: TestClient, auth_headers: dict):
        """Test password change."""
        password_data = {
            "current_password": "testpassword123",
            "new_password": "newtestpassword123"
        }
        
        response = client.post("/api/v1/auth/change-password", 
                             json=password_data, headers=auth_headers)
        
        assert response.status_code == 200
        assert response.json()["message"] == "Password updated successfully"

    def test_change_password_wrong_current(self, client: TestClient, auth_headers: dict):
        """Test password change with wrong current password."""
        password_data = {
            "current_password": "wrongpassword",
            "new_password": "newtestpassword123"
        }
        
        response = client.post("/api/v1/auth/change-password", 
                             json=password_data, headers=auth_headers)
        
        assert response.status_code == 400
        assert "current password" in response.json()["detail"].lower()

    def test_update_profile(self, client: TestClient, auth_headers: dict):
        """Test profile update."""
        profile_data = {
            "full_name": "Updated Test User",
            "email": "<EMAIL>"
        }
        
        response = client.put("/api/v1/auth/profile", 
                            json=profile_data, headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["full_name"] == "Updated Test User"
        assert data["email"] == "<EMAIL>"

    def test_list_users_admin(self, client: TestClient, admin_auth_headers: dict):
        """Test listing users as admin."""
        response = client.get("/api/v1/auth/users", headers=admin_auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1

    def test_list_users_non_admin(self, client: TestClient, auth_headers: dict):
        """Test listing users as non-admin (should fail)."""
        response = client.get("/api/v1/auth/users", headers=auth_headers)
        
        assert response.status_code == 403
