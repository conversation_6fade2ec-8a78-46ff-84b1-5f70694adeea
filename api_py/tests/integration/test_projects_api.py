"""Integration tests for projects API."""

import pytest
from fastapi.testclient import <PERSON><PERSON>lient
from sqlalchemy.orm import Session

from app.models.project import Project
from app.models.user import User


@pytest.mark.integration
@pytest.mark.api
class TestProjectsAPI:
    """Test projects API endpoints."""

    def test_create_project(self, client: TestClient, auth_headers: dict, sample_project_data: dict):
        """Test creating a project."""
        response = client.post("/api/v1/projects/", 
                             json=sample_project_data, headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == sample_project_data["name"]
        assert data["description"] == sample_project_data["description"]
        assert data["cadence"] == sample_project_data["cadence"]
        assert data["status"] == sample_project_data["status"]
        assert "id" in data
        assert "createdAt" in data

    def test_create_project_unauthorized(self, client: TestClient, sample_project_data: dict):
        """Test creating a project without authentication."""
        response = client.post("/api/v1/projects/", json=sample_project_data)
        
        assert response.status_code == 401

    def test_create_project_viewer_role(self, client: TestClient, viewer_auth_headers: dict, 
                                      sample_project_data: dict):
        """Test creating a project with viewer role (should fail)."""
        response = client.post("/api/v1/projects/", 
                             json=sample_project_data, headers=viewer_auth_headers)
        
        assert response.status_code == 403

    def test_create_project_duplicate_name(self, client: TestClient, auth_headers: dict, 
                                         sample_project_data: dict, db_session: Session):
        """Test creating a project with duplicate name."""
        # Create first project
        project = Project(**sample_project_data, id="existing-project")
        db_session.add(project)
        db_session.commit()
        
        # Try to create another with same name
        response = client.post("/api/v1/projects/", 
                             json=sample_project_data, headers=auth_headers)
        
        assert response.status_code == 400
        assert "already exists" in response.json()["detail"]

    def test_get_projects(self, client: TestClient, auth_headers: dict, db_session: Session):
        """Test getting list of projects."""
        # Create test projects
        projects = [
            Project(id="project-1", name="Project 1", owner="test-user-id"),
            Project(id="project-2", name="Project 2", owner="test-user-id"),
        ]
        db_session.add_all(projects)
        db_session.commit()
        
        response = client.get("/api/v1/projects/", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 2

    def test_get_projects_with_filters(self, client: TestClient, auth_headers: dict, 
                                     db_session: Session):
        """Test getting projects with filters."""
        # Create test projects with different statuses
        projects = [
            Project(id="project-1", name="Active Project", status="active", owner="test-user-id"),
            Project(id="project-2", name="Inactive Project", status="inactive", owner="test-user-id"),
        ]
        db_session.add_all(projects)
        db_session.commit()
        
        # Filter by status
        response = client.get("/api/v1/projects/?status=active", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) >= 1
        for project in data:
            assert project["status"] == "active"

    def test_get_project_by_id(self, client: TestClient, auth_headers: dict, 
                             db_session: Session):
        """Test getting a specific project."""
        project = Project(
            id="test-project-id",
            name="Test Project",
            description="Test description",
            owner="test-user-id"
        )
        db_session.add(project)
        db_session.commit()
        
        response = client.get("/api/v1/projects/test-project-id", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == "test-project-id"
        assert data["name"] == "Test Project"
        assert data["description"] == "Test description"

    def test_get_project_not_found(self, client: TestClient, auth_headers: dict):
        """Test getting a non-existent project."""
        response = client.get("/api/v1/projects/non-existent", headers=auth_headers)
        
        assert response.status_code == 404

    def test_update_project(self, client: TestClient, auth_headers: dict, 
                          db_session: Session):
        """Test updating a project."""
        project = Project(
            id="test-project-id",
            name="Original Name",
            description="Original description",
            owner="test-user-id"
        )
        db_session.add(project)
        db_session.commit()
        
        update_data = {
            "name": "Updated Name",
            "description": "Updated description"
        }
        
        response = client.put("/api/v1/projects/test-project-id", 
                            json=update_data, headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "Updated Name"
        assert data["description"] == "Updated description"

    def test_update_project_not_found(self, client: TestClient, auth_headers: dict):
        """Test updating a non-existent project."""
        update_data = {"name": "Updated Name"}
        
        response = client.put("/api/v1/projects/non-existent", 
                            json=update_data, headers=auth_headers)
        
        assert response.status_code == 404

    def test_update_project_duplicate_name(self, client: TestClient, auth_headers: dict, 
                                         db_session: Session):
        """Test updating project with duplicate name."""
        # Create two projects
        projects = [
            Project(id="project-1", name="Project 1", owner="test-user-id"),
            Project(id="project-2", name="Project 2", owner="test-user-id"),
        ]
        db_session.add_all(projects)
        db_session.commit()
        
        # Try to update project-2 to have same name as project-1
        update_data = {"name": "Project 1"}
        
        response = client.put("/api/v1/projects/project-2", 
                            json=update_data, headers=auth_headers)
        
        assert response.status_code == 400
        assert "already exists" in response.json()["detail"]

    def test_delete_project(self, client: TestClient, admin_auth_headers: dict, 
                          db_session: Session):
        """Test deleting a project."""
        project = Project(
            id="test-project-id",
            name="Test Project",
            owner="admin-user-id"
        )
        db_session.add(project)
        db_session.commit()
        
        response = client.delete("/api/v1/projects/test-project-id", 
                               headers=admin_auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "deleted successfully" in data["message"]

    def test_delete_project_not_found(self, client: TestClient, admin_auth_headers: dict):
        """Test deleting a non-existent project."""
        response = client.delete("/api/v1/projects/non-existent", 
                               headers=admin_auth_headers)
        
        assert response.status_code == 404

    def test_delete_project_insufficient_permissions(self, client: TestClient, 
                                                   auth_headers: dict, db_session: Session):
        """Test deleting a project without sufficient permissions."""
        project = Project(
            id="test-project-id",
            name="Test Project",
            owner="test-user-id"
        )
        db_session.add(project)
        db_session.commit()
        
        response = client.delete("/api/v1/projects/test-project-id", 
                               headers=auth_headers)
        
        assert response.status_code == 403

    def test_project_with_stats(self, client: TestClient, auth_headers: dict, 
                              db_session: Session):
        """Test getting project with statistics."""
        project = Project(
            id="test-project-id",
            name="Test Project",
            owner="test-user-id"
        )
        db_session.add(project)
        db_session.commit()
        
        response = client.get("/api/v1/projects/test-project-id", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "id" in data
        assert "name" in data
        # Stats should be included in the response
        # Note: The actual stats structure depends on the ProjectWithStats schema

    def test_projects_pagination(self, client: TestClient, auth_headers: dict, 
                               db_session: Session):
        """Test projects pagination."""
        # Create multiple projects
        projects = [
            Project(id=f"project-{i}", name=f"Project {i}", owner="test-user-id")
            for i in range(15)
        ]
        db_session.add_all(projects)
        db_session.commit()
        
        # Test with limit and skip
        response = client.get("/api/v1/projects/?limit=5&skip=0", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) <= 5
        
        # Test second page
        response = client.get("/api/v1/projects/?limit=5&skip=5", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) <= 5
