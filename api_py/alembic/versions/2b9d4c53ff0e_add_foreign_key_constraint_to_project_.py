"""Add foreign key constraint to project owner

Revision ID: 2b9d4c53ff0e
Revises: 3ceca898b518
Create Date: 2025-09-08 12:50:30.417363

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2b9d4c53ff0e'
down_revision: Union[str, None] = '3ceca898b518'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('projects', schema=None) as batch_op:
        batch_op.create_foreign_key('fk_projects_owner_users', 'users', ['owner'], ['id'])

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('projects', schema=None) as batch_op:
        batch_op.drop_constraint('fk_projects_owner_users', type_='foreignkey')

    # ### end Alembic commands ###
