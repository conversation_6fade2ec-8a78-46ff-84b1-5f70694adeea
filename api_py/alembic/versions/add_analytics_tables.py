"""Add analytics tables

Revision ID: add_analytics_tables
Revises: add_scheduling_tables
Create Date: 2025-01-08 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_analytics_tables'
down_revision = 'add_scheduling_tables'
branch_labels = None
depends_on = None


def upgrade():
    # Create dashboards table
    op.create_table('dashboards',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('name', sa.String(length=200), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('layout', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('theme', sa.String(length=50), nullable=False),
        sa.Column('refresh_interval', sa.Integer(), nullable=True),
        sa.Column('is_public', sa.<PERSON>(), nullable=False),
        sa.Column('is_default', sa.<PERSON>(), nullable=False),
        sa.Column('allowed_users', postgresql.ARRAY(sa.String()), nullable=True),
        sa.Column('project_id', sa.String(), nullable=True),
        sa.Column('created_by', sa.String(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['project_id'], ['projects.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_dashboards_created_by'), 'dashboards', ['created_by'], unique=False)
    op.create_index(op.f('ix_dashboards_is_public'), 'dashboards', ['is_public'], unique=False)
    op.create_index(op.f('ix_dashboards_name'), 'dashboards', ['name'], unique=False)
    op.create_index(op.f('ix_dashboards_project_id'), 'dashboards', ['project_id'], unique=False)

    # Create dashboard_widgets table
    op.create_table('dashboard_widgets',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('dashboard_id', sa.String(), nullable=False),
        sa.Column('name', sa.String(length=200), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('widget_type', sa.String(length=50), nullable=False),
        sa.Column('chart_type', sa.String(length=50), nullable=True),
        sa.Column('data_source', sa.String(length=50), nullable=False),
        sa.Column('entity_filters', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('date_range', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('chart_config', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('position', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('cached_data', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('last_cached', sa.DateTime(), nullable=True),
        sa.Column('cache_duration', sa.Integer(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['dashboard_id'], ['dashboards.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_dashboard_widgets_dashboard_id'), 'dashboard_widgets', ['dashboard_id'], unique=False)
    op.create_index(op.f('ix_dashboard_widgets_data_source'), 'dashboard_widgets', ['data_source'], unique=False)
    op.create_index(op.f('ix_dashboard_widgets_widget_type'), 'dashboard_widgets', ['widget_type'], unique=False)

    # Create analytics_configs table
    op.create_table('analytics_configs',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('name', sa.String(length=200), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('config_type', sa.String(length=50), nullable=False),
        sa.Column('entity_type', sa.String(length=50), nullable=False),
        sa.Column('filters', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('aggregations', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('schedule', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('project_id', sa.String(), nullable=True),
        sa.Column('created_by', sa.String(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['project_id'], ['projects.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_analytics_configs_config_type'), 'analytics_configs', ['config_type'], unique=False)
    op.create_index(op.f('ix_analytics_configs_created_by'), 'analytics_configs', ['created_by'], unique=False)
    op.create_index(op.f('ix_analytics_configs_entity_type'), 'analytics_configs', ['entity_type'], unique=False)

    # Create chart_data table
    op.create_table('chart_data',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('widget_id', sa.String(), nullable=True),
        sa.Column('config_id', sa.String(), nullable=True),
        sa.Column('chart_type', sa.String(length=50), nullable=False),
        sa.Column('data_source', sa.String(length=50), nullable=False),
        sa.Column('raw_data', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('processed_data', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('processing_time_ms', sa.Integer(), nullable=True),
        sa.Column('generated_at', sa.DateTime(), nullable=False),
        sa.Column('expires_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['config_id'], ['analytics_configs.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['widget_id'], ['dashboard_widgets.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_chart_data_chart_type'), 'chart_data', ['chart_type'], unique=False)
    op.create_index(op.f('ix_chart_data_config_id'), 'chart_data', ['config_id'], unique=False)
    op.create_index(op.f('ix_chart_data_data_source'), 'chart_data', ['data_source'], unique=False)
    op.create_index(op.f('ix_chart_data_generated_at'), 'chart_data', ['generated_at'], unique=False)
    op.create_index(op.f('ix_chart_data_widget_id'), 'chart_data', ['widget_id'], unique=False)

    # Create trend_analyses table
    op.create_table('trend_analyses',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('name', sa.String(length=200), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('analysis_type', sa.String(length=50), nullable=False),
        sa.Column('entity_type', sa.String(length=50), nullable=False),
        sa.Column('field_name', sa.String(length=100), nullable=False),
        sa.Column('time_period', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('algorithm', sa.String(length=50), nullable=False),
        sa.Column('parameters', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('trend_direction', sa.String(length=20), nullable=True),
        sa.Column('trend_strength', sa.Float(), nullable=True),
        sa.Column('confidence_score', sa.Float(), nullable=True),
        sa.Column('correlation_coefficient', sa.Float(), nullable=True),
        sa.Column('r_squared', sa.Float(), nullable=True),
        sa.Column('data_points_count', sa.Integer(), nullable=True),
        sa.Column('analysis_duration_ms', sa.Integer(), nullable=True),
        sa.Column('results', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('project_id', sa.String(), nullable=True),
        sa.Column('created_by', sa.String(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['project_id'], ['projects.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_trend_analyses_analysis_type'), 'trend_analyses', ['analysis_type'], unique=False)
    op.create_index(op.f('ix_trend_analyses_created_by'), 'trend_analyses', ['created_by'], unique=False)
    op.create_index(op.f('ix_trend_analyses_entity_type'), 'trend_analyses', ['entity_type'], unique=False)
    op.create_index(op.f('ix_trend_analyses_project_id'), 'trend_analyses', ['project_id'], unique=False)


def downgrade():
    # Drop tables in reverse order
    op.drop_table('trend_analyses')
    op.drop_table('chart_data')
    op.drop_table('analytics_configs')
    op.drop_table('dashboard_widgets')
    op.drop_table('dashboards')
