"""Initial migration with all models and indexes

Revision ID: 1e7f7cccb631
Revises: 
Create Date: 2025-09-08 12:06:24.890565

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy import Text
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '1e7f7cccb631'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('audit_entries',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('itemType', sa.String(), nullable=False),
    sa.Column('itemId', sa.String(), nullable=False),
    sa.Column('action', sa.String(), nullable=False),
    sa.Column('changeSet', sa.JSON().with_variant(postgresql.JSONB(astext_type=Text()), 'postgresql'), nullable=False),
    sa.Column('oldValues', sa.JSON().with_variant(postgresql.JSONB(astext_type=Text()), 'postgresql'), nullable=True),
    sa.Column('newValues', sa.JSON().with_variant(postgresql.JSONB(astext_type=Text()), 'postgresql'), nullable=True),
    sa.Column('changedBy', sa.String(), nullable=False),
    sa.Column('changedAt', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('reason', sa.Text(), nullable=True),
    sa.Column('ip_address', sa.String(), nullable=True),
    sa.Column('user_agent', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('audit_entries', schema=None) as batch_op:
        batch_op.create_index('ix_audit_changed_at_desc', ['changedAt'], unique=False)
        batch_op.create_index(batch_op.f('ix_audit_entries_action'), ['action'], unique=False)
        batch_op.create_index(batch_op.f('ix_audit_entries_changedAt'), ['changedAt'], unique=False)
        batch_op.create_index(batch_op.f('ix_audit_entries_changedBy'), ['changedBy'], unique=False)
        batch_op.create_index(batch_op.f('ix_audit_entries_itemId'), ['itemId'], unique=False)
        batch_op.create_index(batch_op.f('ix_audit_entries_itemType'), ['itemType'], unique=False)
        batch_op.create_index('ix_audit_item_changed_at', ['itemType', 'itemId', 'changedAt'], unique=False)
        batch_op.create_index('ix_audit_item_type_id', ['itemType', 'itemId'], unique=False)
        batch_op.create_index('ix_audit_user_action', ['changedBy', 'action'], unique=False)

    with op.batch_alter_table('actions', schema=None) as batch_op:
        batch_op.create_index('ix_action_assignee_status', ['assignee', 'status'], unique=False)
        batch_op.create_index('ix_action_due_status', ['dueAt', 'status'], unique=False)
        batch_op.create_index('ix_action_owner_status', ['owner', 'status'], unique=False)
        batch_op.create_index('ix_action_progress_status', ['progress', 'status'], unique=False)
        batch_op.create_index('ix_action_project_priority', ['projectId', 'priority'], unique=False)
        batch_op.create_index('ix_action_project_status', ['projectId', 'status'], unique=False)

    with op.batch_alter_table('decisions', schema=None) as batch_op:
        batch_op.create_index('ix_decision_category_status', ['category', 'status'], unique=False)
        batch_op.create_index('ix_decision_decided_by_status', ['decidedBy', 'status'], unique=False)
        batch_op.create_index('ix_decision_decided_on', ['decidedOn'], unique=False)
        batch_op.create_index('ix_decision_project_priority', ['projectId', 'priority'], unique=False)
        batch_op.create_index('ix_decision_project_status', ['projectId', 'status'], unique=False)
        batch_op.create_index('ix_decision_status_decided', ['status', 'decidedOn'], unique=False)

    with op.batch_alter_table('issues', schema=None) as batch_op:
        batch_op.create_index('ix_issue_due_status', ['dueAt', 'status'], unique=False)
        batch_op.create_index('ix_issue_owner_status', ['owner', 'status'], unique=False)
        batch_op.create_index('ix_issue_project_severity', ['projectId', 'severity'], unique=False)
        batch_op.create_index('ix_issue_project_status', ['projectId', 'status'], unique=False)
        batch_op.create_index('ix_issue_severity_status', ['severity', 'status'], unique=False)

    with op.batch_alter_table('projects', schema=None) as batch_op:
        batch_op.create_index('ix_project_created_status', ['createdAt', 'status'], unique=False)
        batch_op.create_index('ix_project_status_owner', ['status', 'owner'], unique=False)

    with op.batch_alter_table('risks', schema=None) as batch_op:
        batch_op.create_index('ix_risk_due_status', ['dueAt', 'status'], unique=False)
        batch_op.create_index('ix_risk_owner_status', ['owner', 'status'], unique=False)
        batch_op.create_index('ix_risk_project_priority', ['projectId', 'priority'], unique=False)
        batch_op.create_index('ix_risk_project_status', ['projectId', 'status'], unique=False)
        batch_op.create_index('ix_risk_score_status', ['score', 'status'], unique=False)

    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.create_index('ix_user_active_verified', ['is_active', 'is_verified'], unique=False)
        batch_op.create_index('ix_user_created_role', ['createdAt', 'role'], unique=False)
        batch_op.create_index('ix_user_role_active', ['role', 'is_active'], unique=False)

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.drop_index('ix_user_role_active')
        batch_op.drop_index('ix_user_created_role')
        batch_op.drop_index('ix_user_active_verified')

    with op.batch_alter_table('risks', schema=None) as batch_op:
        batch_op.drop_index('ix_risk_score_status')
        batch_op.drop_index('ix_risk_project_status')
        batch_op.drop_index('ix_risk_project_priority')
        batch_op.drop_index('ix_risk_owner_status')
        batch_op.drop_index('ix_risk_due_status')

    with op.batch_alter_table('projects', schema=None) as batch_op:
        batch_op.drop_index('ix_project_status_owner')
        batch_op.drop_index('ix_project_created_status')

    with op.batch_alter_table('issues', schema=None) as batch_op:
        batch_op.drop_index('ix_issue_severity_status')
        batch_op.drop_index('ix_issue_project_status')
        batch_op.drop_index('ix_issue_project_severity')
        batch_op.drop_index('ix_issue_owner_status')
        batch_op.drop_index('ix_issue_due_status')

    with op.batch_alter_table('decisions', schema=None) as batch_op:
        batch_op.drop_index('ix_decision_status_decided')
        batch_op.drop_index('ix_decision_project_status')
        batch_op.drop_index('ix_decision_project_priority')
        batch_op.drop_index('ix_decision_decided_on')
        batch_op.drop_index('ix_decision_decided_by_status')
        batch_op.drop_index('ix_decision_category_status')

    with op.batch_alter_table('actions', schema=None) as batch_op:
        batch_op.drop_index('ix_action_project_status')
        batch_op.drop_index('ix_action_project_priority')
        batch_op.drop_index('ix_action_progress_status')
        batch_op.drop_index('ix_action_owner_status')
        batch_op.drop_index('ix_action_due_status')
        batch_op.drop_index('ix_action_assignee_status')

    with op.batch_alter_table('audit_entries', schema=None) as batch_op:
        batch_op.drop_index('ix_audit_user_action')
        batch_op.drop_index('ix_audit_item_type_id')
        batch_op.drop_index('ix_audit_item_changed_at')
        batch_op.drop_index(batch_op.f('ix_audit_entries_itemType'))
        batch_op.drop_index(batch_op.f('ix_audit_entries_itemId'))
        batch_op.drop_index(batch_op.f('ix_audit_entries_changedBy'))
        batch_op.drop_index(batch_op.f('ix_audit_entries_changedAt'))
        batch_op.drop_index(batch_op.f('ix_audit_entries_action'))
        batch_op.drop_index('ix_audit_changed_at_desc')

    op.drop_table('audit_entries')
    # ### end Alembic commands ###
