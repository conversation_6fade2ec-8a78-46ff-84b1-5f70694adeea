"""Add saved filters table

Revision ID: 3ceca898b518
Revises: 1e7f7cccb631
Create Date: 2025-09-08 12:40:34.921538

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3ceca898b518'
down_revision: Union[str, None] = '1e7f7cccb631'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('saved_filters',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('filter_data', sa.JSON(), nullable=False),
    sa.Column('is_public', sa.<PERSON>an(), nullable=False),
    sa.Column('project_id', sa.String(), nullable=True),
    sa.Column('created_by', sa.String(), nullable=False),
    sa.Column('createdAt', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=False),
    sa.Column('updatedAt', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=False),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['project_id'], ['projects.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('saved_filters', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_saved_filters_createdAt'), ['createdAt'], unique=False)
        batch_op.create_index(batch_op.f('ix_saved_filters_created_by'), ['created_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_saved_filters_is_public'), ['is_public'], unique=False)
        batch_op.create_index(batch_op.f('ix_saved_filters_name'), ['name'], unique=False)
        batch_op.create_index(batch_op.f('ix_saved_filters_project_id'), ['project_id'], unique=False)

    with op.batch_alter_table('actions', schema=None) as batch_op:
        batch_op.drop_index('idx_actions_title_fts')

    with op.batch_alter_table('audit_entries', schema=None) as batch_op:
        batch_op.drop_index('idx_audit_recent')

    with op.batch_alter_table('decisions', schema=None) as batch_op:
        batch_op.drop_index('idx_decisions_title_fts')

    with op.batch_alter_table('issues', schema=None) as batch_op:
        batch_op.drop_index('idx_issues_title_fts')

    with op.batch_alter_table('projects', schema=None) as batch_op:
        batch_op.drop_index('idx_projects_name_fts')

    with op.batch_alter_table('risks', schema=None) as batch_op:
        batch_op.drop_index('idx_risks_title_fts')

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('risks', schema=None) as batch_op:
        batch_op.create_index('idx_risks_title_fts', ['title'], unique=False)

    with op.batch_alter_table('projects', schema=None) as batch_op:
        batch_op.create_index('idx_projects_name_fts', ['name'], unique=False)

    with op.batch_alter_table('issues', schema=None) as batch_op:
        batch_op.create_index('idx_issues_title_fts', ['title'], unique=False)

    with op.batch_alter_table('decisions', schema=None) as batch_op:
        batch_op.create_index('idx_decisions_title_fts', ['title'], unique=False)

    with op.batch_alter_table('audit_entries', schema=None) as batch_op:
        batch_op.create_index('idx_audit_recent', ['changedAt', 'itemType'], unique=False)

    with op.batch_alter_table('actions', schema=None) as batch_op:
        batch_op.create_index('idx_actions_title_fts', ['title'], unique=False)

    with op.batch_alter_table('saved_filters', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_saved_filters_project_id'))
        batch_op.drop_index(batch_op.f('ix_saved_filters_name'))
        batch_op.drop_index(batch_op.f('ix_saved_filters_is_public'))
        batch_op.drop_index(batch_op.f('ix_saved_filters_created_by'))
        batch_op.drop_index(batch_op.f('ix_saved_filters_createdAt'))

    op.drop_table('saved_filters')
    # ### end Alembic commands ###
