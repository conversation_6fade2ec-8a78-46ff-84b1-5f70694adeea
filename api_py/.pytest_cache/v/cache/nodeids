["tests/test_advanced_filtering.py::TestAdvancedFilterService::test_date_range_filter", "tests/test_advanced_filtering.py::TestAdvancedFilterService::test_get_filter_options", "tests/test_advanced_filtering.py::TestAdvancedFilterService::test_get_search_suggestions", "tests/test_advanced_filtering.py::TestAdvancedFilterService::test_multi_value_filters", "tests/test_advanced_filtering.py::TestAdvancedFilterService::test_public_filter_visibility", "tests/test_advanced_filtering.py::TestAdvancedFilterService::test_save_and_load_filter", "tests/test_advanced_filtering.py::TestAdvancedFilterService::test_score_range_filter", "tests/test_advanced_filtering.py::TestAdvancedFilterService::test_sorting", "tests/test_advanced_filtering.py::TestAdvancedFilterService::test_text_search_case_sensitive", "tests/test_advanced_filtering.py::TestAdvancedFilterService::test_text_search_contains", "tests/test_advanced_filtering.py::TestAdvancedFilterService::test_text_search_exact", "tests/test_notifications.py::TestNotificationService::test_create_from_template", "tests/test_notifications.py::TestNotificationService::test_create_notification", "tests/test_notifications.py::TestNotificationService::test_get_user_notifications", "tests/test_notifications.py::TestNotificationService::test_mark_all_as_read", "tests/test_notifications.py::TestNotificationService::test_mark_as_read", "tests/test_notifications.py::TestNotificationService::test_user_preferences"]