# RAID App (Next.js + NestJS + Prisma)

A full-stack RAID (Risks, Actions, Issues, Decisions) log web application scaffold.

## Stack
- **Frontend:** Next.js (App Router) + TypeScript + Tailwind + shadcn-style UI (local components) + TanStack Query
- **Backend:** NestJS + Prisma + PostgreSQL
- **Jobs:** (stub) ready to add BullMQ/Redis
- **Infra:** Docker Compose for Postgres

## Quick Start

### 1) Start Postgres
```bash
docker compose up -d
```

### 2) Backend
```bash
cd api
cp .env.example .env            # or set your own DATABASE_URL
npm i
npm run prisma:generate
npm run prisma:migrate          # creates tables
npm run db:seed                 # sample data
npm run dev                     # web on http://localhost:3000
```

### 3) Frontend
```bash
cd ../web
cp .env.local.example .env.local  # set NEXT_PUBLIC_API_URL=http://localhost:5050
npm i
npm run dev                      # http://localhost:3000
```

## Screens implemented
- **Project Home:** list projects, quick stats
- **Project Detail:** tabs for **Board**, **List**, and **Meeting Mode**
- **Risk Detail:** dedicated page with inline actions

> This is an MVP scaffold. You can extend schemas, add RBAC, auth, notifications, and background jobs.

## API (selected)
- `GET /projects`
- `POST /projects`
- `GET /projects/:id` (with summary aggregates)
- `GET /projects/:id/raid?category=risks|actions|issues|decisions&status=&owner=&severity=&q=`
- `POST /projects/:id/risks`
- `PATCH /risks/:id`

## Prisma Models
See `api/prisma/schema.prisma`.

## Notes
- Seed data is intentionally small and realistic.
- UI uses accessible Tailwind components and light shadcn-style wrappers (copied locally).


---

## New: Auth, Notifications, Persistence, Import/Export, Scheduling

### Auth + Roles
- Backend verifies JWTs via JWKS if `AUTH_ISSUER`/`AUTH_JWKS_URI` are set (works for Clerk or Auth0).
- Frontend supports Clerk or Auth0 (set `NEXT_PUBLIC_AUTH_PROVIDER=clerk|auth0`).
- RBAC roles: Viewer, Contributor, PM, Admin. Protected endpoints use `@Roles(...)` guard.
- Dev mode: leave auth envs empty; backend treats requests as Viewer. Seeded admin user `seed-admin` exists for role demos.

### Slack/Teams notifications
- Set `SLACK_WEBHOOK_URL` and/or `TEAMS_WEBHOOK_URL`. 
- Creating/updating a Risk above `HIGH_SCORE_THRESHOLD` (default 3) posts an alert.

### Real drag-and-drop persistence
- Moving a risk across columns issues `PATCH /risks/:id { status }` automatically.

### CSV/XLSX import & export
- `GET /projects/:id/export?format=xlsx` returns a workbook with 4 sheets.
- `GET /projects/:id/export?format=csv&category=risks` returns CSV for a category.
- `POST /projects/:id/import?category=risks` accepts JSON array to import Risks (basic).

### Weekly summaries
- CRON: `WEEKLY_SUMMARY_CRON` (default Monday 09:00 ET). Posts summary lines via Slack/Teams webhooks.


## Clerk setup
1. Sign up at https://clerk.com and create an application.
2. In **Web** env vars (frontend):
   - `NEXT_PUBLIC_AUTH_PROVIDER=clerk`
   - `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_...`
   - `CLERK_SECRET_KEY=sk_test_...` (server-side only, used by Next if you add API routes; our backend API is separate)
3. In **API** env vars (backend verification):
   - `AUTH_ISSUER=https://YOUR_SUBDOMAIN.clerk.accounts.dev` (or your custom domain)
   - `AUTH_JWKS_URI=https://YOUR_SUBDOMAIN.clerk.accounts.dev/.well-known/jwks.json`
   - (Optional) `AUTH_AUDIENCE=...` if you configured one
4. Start both apps. Visit `/admin/roles` after signing in to elevate your role to **Admin**.
5. Once you set your role, protected actions (e.g., creating risks) will be allowed.

### Teams Adaptive Cards
- Put your Teams **Incoming Webhook** URL into `TEAMS_WEBHOOK_URL` in `api/.env`. The weekly summary job will post an **Adaptive Card** with counts and top risks.


## Clerk webhooks (auto-provision users)
1. In Clerk Dashboard → **Webhooks**, create an endpoint pointing to your API:
   - URL: `http://localhost:5050/webhooks/clerk`
   - Events: `user.created`, `user.updated` (you can add more later)
2. Copy the signing secret (`whsec_...`) into `api/.env` as `CLERK_WEBHOOK_SECRET`.
3. Start the API; when a user is created/updated in Clerk, they’ll be upserted in the app DB with role `Viewer`.
4. Visit `/admin/roles` to elevate roles.

### Teams Adaptive Card buttons
- The weekly summary card now includes **Open Project** and **View Risks** buttons.
- Configure `FRONTEND_BASE_URL` (default `http://localhost:3000`) so links open your web app.


## Risk creation modal
- In **Project → Board (Risks)** click **New Risk** to open a modal.
- Edit *probability* and *impact* to see a **live score** and whether it meets the `HIGH_SCORE_THRESHOLD`.
- A **Slack text** and **Teams Adaptive Card** preview render inline (the real notifications send automatically on create if the score is above threshold).


## Notify anyway (force alerts)
- The risk creation modal includes a **Notify anyway** checkbox. When enabled, Slack/Teams alerts are sent even if the score is below `HIGH_SCORE_THRESHOLD`.

## Convert Risk → Issue
- On a Risk’s detail page, click **Convert Risk → Issue**.
- This creates an **Issue** linked to the Risk (copies title/owner, derives severity from score, status Open) and sets the Risk status to *In progress*.


## Toasts
- Lightweight toast notifications are built-in. Use the `useToast()` hook to show messages:
```tsx
const { addToast } = useToast();
addToast({ variant: 'success', title: 'Saved', description: 'Your changes are live.' });
```
- Already wired into: Risk create, Convert to Issue, and Kanban update failures.

## Issues panel with filters
- In a project page, the **Issues** section includes severity/status/owner filters and colored severity badges.


## Sorting & preferences
- **Issues, Actions, Decisions** panels now have client-side sorting controls:
  - Issues: sort by **Severity**, **Owner**, **Due date**.
  - Actions: sort by **Owner**, **Due date**.
  - Decisions: sort by **Date**, **Decider**.
- Your last chosen sort and direction per project are saved to **localStorage** and restored on reload.


## Clickable header sorting & multi-sort
- Click **column headers** to sort. Click again to flip direction. Click a third time to remove.
- **Shift+Click** other columns to add **secondary** (or tertiary) sorts (e.g., Severity ↓ then Due ↑).
- Active sorts are shown as small badges (with order numbers) and are saved to **localStorage** per project.


## Column reordering & View presets
- **Drag column headers** to reorder columns. The order persists per project and table.
- **Save current view**: Name a preset capturing your current tab, density, each panel’s filters, multi-sort order, and column widths/order. Apply later from the dropdown.
- Presets are stored locally (`localStorage:viewpresets:<projectId>`) and broadcast to panels without reloading.
