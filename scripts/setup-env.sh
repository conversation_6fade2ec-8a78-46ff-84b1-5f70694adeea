#!/bin/bash

# =============================================================================
# Environment Setup Script
# =============================================================================
# This script helps set up environment files for different environments

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Check if we're in the right directory
if [ ! -f "web/package.json" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

print_header "RAID App Environment Setup"

# Create environment files from templates
setup_environment_files() {
    print_status "Setting up environment files..."
    
    # Production environment
    if [ ! -f "web/.env.production" ]; then
        if [ -f "web/.env.production.example" ]; then
            cp web/.env.production.example web/.env.production
            print_status "Created web/.env.production from template"
        else
            print_error "Template file web/.env.production.example not found"
            exit 1
        fi
    else
        print_warning "web/.env.production already exists, skipping..."
    fi
    
    # Staging environment
    if [ ! -f "web/.env.staging" ]; then
        if [ -f "web/.env.staging.example" ]; then
            cp web/.env.staging.example web/.env.staging
            print_status "Created web/.env.staging from template"
        else
            print_error "Template file web/.env.staging.example not found"
            exit 1
        fi
    else
        print_warning "web/.env.staging already exists, skipping..."
    fi
}

# Update .gitignore to exclude environment files
update_gitignore() {
    print_status "Updating .gitignore..."
    
    if [ ! -f ".gitignore" ]; then
        touch .gitignore
    fi
    
    # Check if environment files are already ignored
    if ! grep -q "\.env\.production" .gitignore; then
        echo "" >> .gitignore
        echo "# Environment files (contain secrets)" >> .gitignore
        echo ".env.production" >> .gitignore
        echo ".env.staging" >> .gitignore
        print_status "Added environment files to .gitignore"
    else
        print_warning ".gitignore already contains environment file exclusions"
    fi
}

# Validate environment file structure
validate_env_file() {
    local env_file=$1
    local env_name=$2
    
    print_status "Validating $env_name environment file..."
    
    if [ ! -f "$env_file" ]; then
        print_error "$env_file not found"
        return 1
    fi
    
    # Check for required variables
    local required_vars=(
        "NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY"
        "CLERK_SECRET_KEY"
        "NEXT_PUBLIC_API_URL"
        "NODE_ENV"
        "NEXT_PUBLIC_APP_ENV"
    )
    
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if ! grep -q "^$var=" "$env_file"; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -eq 0 ]; then
        print_status "$env_name environment file validation passed"
        return 0
    else
        print_error "$env_name environment file is missing required variables:"
        for var in "${missing_vars[@]}"; do
            echo "  - $var"
        done
        return 1
    fi
}

# Check for placeholder values
check_placeholders() {
    local env_file=$1
    local env_name=$2
    
    print_status "Checking for placeholder values in $env_name..."
    
    local placeholders=(
        "YOUR_"
        "your-"
        "abc123"
        "def456"
        "pk_live_YOUR_"
        "sk_live_YOUR_"
        "phc_YOUR_"
    )
    
    local found_placeholders=()
    
    for placeholder in "${placeholders[@]}"; do
        if grep -q "$placeholder" "$env_file"; then
            found_placeholders+=("$placeholder")
        fi
    done
    
    if [ ${#found_placeholders[@]} -eq 0 ]; then
        print_status "$env_name appears to have real values (no placeholders found)"
    else
        print_warning "$env_name still contains placeholder values:"
        for placeholder in "${found_placeholders[@]}"; do
            echo "  - Lines containing '$placeholder':"
            grep -n "$placeholder" "$env_file" | head -3
        done
        echo ""
        print_warning "Please replace these with your actual service credentials"
    fi
}

# Main execution
main() {
    setup_environment_files
    update_gitignore
    
    echo ""
    print_header "Environment File Validation"
    
    # Validate environment files
    validate_env_file "web/.env.production" "Production"
    validate_env_file "web/.env.staging" "Staging"
    
    echo ""
    print_header "Placeholder Check"
    
    # Check for placeholders
    check_placeholders "web/.env.production" "Production"
    check_placeholders "web/.env.staging" "Staging"
    
    echo ""
    print_header "Next Steps"
    print_status "Environment files have been created!"
    print_status "Please follow these steps:"
    echo ""
    echo "1. Edit web/.env.production with your production credentials"
    echo "2. Edit web/.env.staging with your staging credentials"
    echo "3. Review docs/ENVIRONMENT_SETUP.md for detailed setup instructions"
    echo "4. Test your configuration with: npm run build"
    echo ""
    print_warning "Remember: Never commit .env.production or .env.staging files!"
    print_status "Setup complete! 🚀"
}

# Run main function
main
