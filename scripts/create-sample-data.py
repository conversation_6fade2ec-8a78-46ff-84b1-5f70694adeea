#!/usr/bin/env python3
"""
Create sample data for testing the RAID application analytics
"""

import sys
import os
import requests
import json
from datetime import datetime, timedelta
import random

# Add the API directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'api_py'))

API_BASE_URL = "http://localhost:8000/api/v1"

def create_sample_project():
    """Create a sample project"""
    project_data = {
        "id": "sample-project-1",
        "name": "Sample Analytics Project",
        "description": "A sample project to demonstrate analytics functionality",
        "owner": "demo-user",
        "status": "active",
        "priority": "high",
        "start_date": (datetime.now() - timedelta(days=30)).isoformat(),
        "target_date": (datetime.now() + timedelta(days=60)).isoformat()
    }
    
    try:
        response = requests.post(f"{API_BASE_URL}/projects", json=project_data)
        if response.status_code in [200, 201]:
            print(f"✅ Created project: {project_data['name']}")
            return project_data["id"]
        else:
            print(f"❌ Failed to create project: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ Error creating project: {e}")
        return None

def create_sample_risks(project_id):
    """Create sample risks"""
    risks = [
        {
            "id": f"risk-{i}",
            "projectId": project_id,
            "title": f"Sample Risk {i}",
            "description": f"This is a sample risk for testing analytics - Risk {i}",
            "probability": random.uniform(0.1, 0.9),
            "impact": random.randint(1, 5),
            "status": random.choice(["Open", "Mitigated", "Closed"]),
            "priority": random.choice(["Low", "Medium", "High", "Critical"]),
            "owner": "demo-user"
        }
        for i in range(1, 16)  # Create 15 risks
    ]
    
    created_count = 0
    for risk in risks:
        try:
            response = requests.post(f"{API_BASE_URL}/risks", json=risk)
            if response.status_code in [200, 201]:
                created_count += 1
            else:
                print(f"❌ Failed to create risk {risk['title']}: {response.status_code}")
        except Exception as e:
            print(f"❌ Error creating risk {risk['title']}: {e}")
    
    print(f"✅ Created {created_count} risks")
    return created_count

def create_sample_actions(project_id):
    """Create sample actions"""
    actions = [
        {
            "id": f"action-{i}",
            "projectId": project_id,
            "title": f"Sample Action {i}",
            "description": f"This is a sample action for testing analytics - Action {i}",
            "status": random.choice(["Open", "In Progress", "Completed", "Cancelled"]),
            "priority": random.choice(["Low", "Medium", "High", "Critical"]),
            "progress": random.randint(0, 100),
            "owner": "demo-user",
            "assignee": "demo-user"
        }
        for i in range(1, 21)  # Create 20 actions
    ]
    
    created_count = 0
    for action in actions:
        try:
            response = requests.post(f"{API_BASE_URL}/actions", json=action)
            if response.status_code in [200, 201]:
                created_count += 1
            else:
                print(f"❌ Failed to create action {action['title']}: {response.status_code}")
        except Exception as e:
            print(f"❌ Error creating action {action['title']}: {e}")
    
    print(f"✅ Created {created_count} actions")
    return created_count

def create_sample_issues(project_id):
    """Create sample issues"""
    issues = [
        {
            "id": f"issue-{i}",
            "projectId": project_id,
            "title": f"Sample Issue {i}",
            "description": f"This is a sample issue for testing analytics - Issue {i}",
            "severity": random.choice(["Low", "Medium", "High", "Critical"]),
            "status": random.choice(["Open", "In Progress", "Resolved", "Closed"]),
            "priority": random.choice(["Low", "Medium", "High", "Critical"]),
            "owner": "demo-user",
            "reporter": "demo-user"
        }
        for i in range(1, 11)  # Create 10 issues
    ]
    
    created_count = 0
    for issue in issues:
        try:
            response = requests.post(f"{API_BASE_URL}/issues", json=issue)
            if response.status_code in [200, 201]:
                created_count += 1
            else:
                print(f"❌ Failed to create issue {issue['title']}: {response.status_code}")
        except Exception as e:
            print(f"❌ Error creating issue {issue['title']}: {e}")
    
    print(f"✅ Created {created_count} issues")
    return created_count

def create_sample_decisions(project_id):
    """Create sample decisions"""
    decisions = [
        {
            "id": f"decision-{i}",
            "projectId": project_id,
            "title": f"Sample Decision {i}",
            "description": f"This is a sample decision for testing analytics - Decision {i}",
            "status": random.choice(["Proposed", "Approved", "Rejected", "Implemented"]),
            "priority": random.choice(["Low", "Medium", "High", "Critical"]),
            "category": random.choice(["Strategic", "Tactical", "Operational"]),
            "decidedBy": "demo-user"
        }
        for i in range(1, 13)  # Create 12 decisions
    ]
    
    created_count = 0
    for decision in decisions:
        try:
            response = requests.post(f"{API_BASE_URL}/decisions", json=decision)
            if response.status_code in [200, 201]:
                created_count += 1
            else:
                print(f"❌ Failed to create decision {decision['title']}: {response.status_code}")
        except Exception as e:
            print(f"❌ Error creating decision {decision['title']}: {e}")
    
    print(f"✅ Created {created_count} decisions")
    return created_count

def main():
    """Main function to create all sample data"""
    print("🚀 Creating sample data for RAID Analytics...")
    print("=" * 50)
    
    # Test API connectivity
    try:
        response = requests.get(f"{API_BASE_URL.replace('/api/v1', '')}/health")
        if response.status_code != 200:
            print("❌ API is not responding. Make sure the backend is running on localhost:8000")
            return
        print("✅ API is responding")
    except Exception as e:
        print(f"❌ Cannot connect to API: {e}")
        print("Make sure the backend is running on localhost:8000")
        return
    
    # Create sample project
    project_id = create_sample_project()
    if not project_id:
        print("❌ Cannot proceed without a project")
        return
    
    # Create sample RAID items
    risks_count = create_sample_risks(project_id)
    actions_count = create_sample_actions(project_id)
    issues_count = create_sample_issues(project_id)
    decisions_count = create_sample_decisions(project_id)
    
    print("=" * 50)
    print("📊 Sample Data Summary:")
    print(f"   Project: 1")
    print(f"   Risks: {risks_count}")
    print(f"   Actions: {actions_count}")
    print(f"   Issues: {issues_count}")
    print(f"   Decisions: {decisions_count}")
    print(f"   Total RAID Items: {risks_count + actions_count + issues_count + decisions_count}")
    print("=" * 50)
    print("🎉 Sample data creation complete!")
    print("You can now test the analytics functionality with real data.")

if __name__ == "__main__":
    main()
