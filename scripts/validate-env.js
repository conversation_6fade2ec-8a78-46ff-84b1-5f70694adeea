#!/usr/bin/env node

/**
 * Environment Validation Script
 * Validates that all required environment variables are properly configured
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function printHeader(text) {
  console.log(colorize(`\n=== ${text} ===`, 'blue'));
}

function printSuccess(text) {
  console.log(colorize(`✅ ${text}`, 'green'));
}

function printWarning(text) {
  console.log(colorize(`⚠️  ${text}`, 'yellow'));
}

function printError(text) {
  console.log(colorize(`❌ ${text}`, 'red'));
}

function printInfo(text) {
  console.log(colorize(`ℹ️  ${text}`, 'cyan'));
}

// Required environment variables
const requiredVars = {
  authentication: [
    'NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY',
    'CLERK_SECRET_KEY'
  ],
  api: [
    'NEXT_PUBLIC_API_URL'
  ],
  environment: [
    'NODE_ENV',
    'NEXT_PUBLIC_APP_ENV',
    'NEXT_PUBLIC_APP_VERSION'
  ],
  monitoring: [
    'NEXT_PUBLIC_SENTRY_DSN',
    'SENTRY_ORG',
    'SENTRY_PROJECT',
    'NEXT_PUBLIC_LOGROCKET_APP_ID',
    'NEXT_PUBLIC_POSTHOG_KEY',
    'NEXT_PUBLIC_POSTHOG_HOST'
  ]
};

// Optional but recommended variables
const optionalVars = [
  'SENTRY_AUTH_TOKEN',
  'NEXT_PUBLIC_VERCEL_ANALYTICS_ID'
];

// Placeholder patterns to detect
const placeholderPatterns = [
  /YOUR_/,
  /your-/,
  /abc123/,
  /def456/,
  /pk_test_/,
  /sk_test_/,
  /phc_YOUR_/,
  /localhost/
];

function loadEnvFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const env = {};
    
    content.split('\n').forEach(line => {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('#')) {
        const [key, ...valueParts] = trimmed.split('=');
        if (key && valueParts.length > 0) {
          env[key] = valueParts.join('=');
        }
      }
    });
    
    return env;
  } catch (error) {
    return null;
  }
}

function validateEnvironment(envFile, envName) {
  printHeader(`Validating ${envName} Environment`);
  
  const env = loadEnvFile(envFile);
  if (!env) {
    printError(`Could not load ${envFile}`);
    return false;
  }
  
  printSuccess(`Loaded ${envFile}`);
  
  let isValid = true;
  let warningCount = 0;
  
  // Check required variables by category
  Object.entries(requiredVars).forEach(([category, vars]) => {
    console.log(colorize(`\n${category.toUpperCase()}:`, 'cyan'));
    
    vars.forEach(varName => {
      const value = env[varName];
      
      if (!value) {
        printError(`Missing required variable: ${varName}`);
        isValid = false;
      } else if (value.trim() === '') {
        printError(`Empty value for required variable: ${varName}`);
        isValid = false;
      } else {
        // Check for placeholders
        const hasPlaceholder = placeholderPatterns.some(pattern => pattern.test(value));
        
        if (hasPlaceholder) {
          printWarning(`${varName} appears to contain placeholder value: ${value.substring(0, 50)}...`);
          warningCount++;
        } else {
          printSuccess(`${varName} ✓`);
        }
      }
    });
  });
  
  // Check optional variables
  console.log(colorize('\nOPTIONAL VARIABLES:', 'cyan'));
  optionalVars.forEach(varName => {
    const value = env[varName];
    if (value && value.trim() !== '') {
      const hasPlaceholder = placeholderPatterns.some(pattern => pattern.test(value));
      if (hasPlaceholder) {
        printWarning(`${varName} appears to contain placeholder value`);
        warningCount++;
      } else {
        printSuccess(`${varName} ✓`);
      }
    } else {
      printInfo(`${varName} not set (optional)`);
    }
  });
  
  // Environment-specific validations
  if (envName === 'Production') {
    console.log(colorize('\nPRODUCTION-SPECIFIC CHECKS:', 'cyan'));
    
    // Check for production-appropriate values
    if (env.NODE_ENV !== 'production') {
      printWarning(`NODE_ENV should be 'production', got: ${env.NODE_ENV}`);
      warningCount++;
    } else {
      printSuccess('NODE_ENV is set to production');
    }
    
    if (env.NEXT_PUBLIC_APP_ENV !== 'production') {
      printWarning(`NEXT_PUBLIC_APP_ENV should be 'production', got: ${env.NEXT_PUBLIC_APP_ENV}`);
      warningCount++;
    } else {
      printSuccess('NEXT_PUBLIC_APP_ENV is set to production');
    }
    
    // Check for localhost URLs
    if (env.NEXT_PUBLIC_API_URL && env.NEXT_PUBLIC_API_URL.includes('localhost')) {
      printError('NEXT_PUBLIC_API_URL should not contain localhost in production');
      isValid = false;
    }
    
    // Check for test keys
    if (env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY && env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY.includes('pk_test_')) {
      printError('Using test Clerk key in production environment');
      isValid = false;
    }
    
    if (env.CLERK_SECRET_KEY && env.CLERK_SECRET_KEY.includes('sk_test_')) {
      printError('Using test Clerk secret in production environment');
      isValid = false;
    }
  }
  
  // Summary
  console.log(colorize('\nVALIDATION SUMMARY:', 'blue'));
  if (isValid) {
    if (warningCount === 0) {
      printSuccess(`${envName} environment is fully configured!`);
    } else {
      printWarning(`${envName} environment is valid but has ${warningCount} warnings`);
    }
  } else {
    printError(`${envName} environment has configuration errors`);
  }
  
  return isValid && warningCount === 0;
}

function validateServiceConnectivity() {
  printHeader('Service Connectivity Validation');
  
  printInfo('To fully validate your setup, test these service connections:');
  console.log('\n1. Sentry:');
  console.log('   - Visit your Sentry dashboard');
  console.log('   - Check that your project exists');
  console.log('   - Test DSN with: curl -X POST [YOUR_DSN]');
  
  console.log('\n2. LogRocket:');
  console.log('   - Visit LogRocket dashboard');
  console.log('   - Verify app exists');
  console.log('   - Check domain allowlist');
  
  console.log('\n3. PostHog:');
  console.log('   - Visit PostHog dashboard');
  console.log('   - Verify project exists');
  console.log('   - Test API key validity');
  
  console.log('\n4. Clerk:');
  console.log('   - Visit Clerk dashboard');
  console.log('   - Check instance configuration');
  console.log('   - Verify domain settings');
}

function main() {
  console.log(colorize('🔍 RAID App Environment Validation', 'blue'));
  console.log(colorize('=====================================', 'blue'));

  // Check if we're in the web directory or project root
  const isInWebDir = fs.existsSync('package.json') && fs.existsSync('next.config.js');
  const webDir = isInWebDir ? process.cwd() : path.join(process.cwd(), 'web');
  const productionEnv = path.join(webDir, '.env.production');
  const stagingEnv = path.join(webDir, '.env.staging');
  
  let allValid = true;
  
  // Validate production environment
  if (fs.existsSync(productionEnv)) {
    const prodValid = validateEnvironment(productionEnv, 'Production');
    allValid = allValid && prodValid;
  } else {
    printWarning('Production environment file not found: web/.env.production');
    printInfo('Run: cp web/.env.production.example web/.env.production');
    allValid = false;
  }
  
  // Validate staging environment
  if (fs.existsSync(stagingEnv)) {
    const stagingValid = validateEnvironment(stagingEnv, 'Staging');
    allValid = allValid && stagingValid;
  } else {
    printWarning('Staging environment file not found: web/.env.staging');
    printInfo('Run: cp web/.env.staging.example web/.env.staging');
  }
  
  validateServiceConnectivity();
  
  // Final summary
  printHeader('Final Summary');
  if (allValid) {
    printSuccess('All environment configurations are valid! 🚀');
    printInfo('You can now deploy your application with confidence.');
  } else {
    printError('Some environment configurations need attention.');
    printInfo('Please fix the issues above before deploying.');
  }
  
  process.exit(allValid ? 0 : 1);
}

// Run the validation
main();
