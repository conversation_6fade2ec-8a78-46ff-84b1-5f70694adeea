{"name": "raid-api", "version": "0.1.0", "private": true, "scripts": {"start": "nest start", "start:dev": "nest start --watch", "build": "nest build", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev --name init", "db:seed": "tsx prisma/seed.ts", "dev:bootstrap": "npm run prisma:generate && npm run prisma:migrate && npm run db:seed && npm run start:dev"}, "dependencies": {"@nestjs/axios": "^3.0.2", "@nestjs/common": "^10.4.0", "@nestjs/core": "^10.4.0", "@nestjs/platform-express": "^10.4.0", "@nestjs/schedule": "^4.1.0", "@prisma/client": "^5.18.0", "axios": "^1.7.2", "body-parser": "^1.20.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "dotenv": "^16.4.5", "exceljs": "^4.4.0", "jose": "^5.6.3", "json2csv": "^5.0.7", "node-fetch": "^3.3.2", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "svix": "^1.17.0"}, "devDependencies": {"@nestjs/cli": "^10.4.2", "@nestjs/schematics": "^10.0.6", "@nestjs/testing": "^10.4.0", "@types/body-parser": "^1.19.6", "@types/express": "^5.0.3", "@types/json2csv": "^5.0.7", "prisma": "^5.18.0", "tsx": "^4.19.1", "typescript": "^5.4.5"}}