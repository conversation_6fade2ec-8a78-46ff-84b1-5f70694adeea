async function testAPI() {
  const fetch = (await import('node-fetch')).default;
  try {
    console.log('Testing API...');
    
    // Test getting projects
    console.log('\n1. Getting projects...');
    const projectsResponse = await fetch('http://localhost:4000/projects');
    const projects = await projectsResponse.json();
    console.log('Projects:', projects);
    
    // Create a new demo project
    console.log('\n2. Creating demo project...');
    const newProject = {
      name: 'Demo RAID Project',
      cadence: 'weekly',
      categories: ['Security', 'Performance', 'Compliance']
    };
    
    const createResponse = await fetch('http://localhost:4000/projects', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(newProject)
    });
    
    const createdProject = await createResponse.json();
    console.log('Created project:', createdProject);
    
    // Create a risk for the project
    console.log('\n3. Creating a risk...');
    const newRisk = {
      title: 'Data Security Risk',
      trigger: 'Potential data breach due to weak authentication',
      probability: 0.3,
      impact: 4,
      response: 'Implement multi-factor authentication',
      owner: 'Security Team',
      status: 'Open',
      dueAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
    };
    
    const riskResponse = await fetch(`http://localhost:4000/projects/${createdProject.id}/risks`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(newRisk)
    });
    
    const createdRisk = await riskResponse.json();
    console.log('Created risk:', createdRisk);
    
    // Create an action
    console.log('\n4. Creating an action...');
    const newAction = {
      title: 'Implement MFA for all users',
      owner: 'Security Team',
      status: 'Open',
      dueAt: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(),
      relatedId: createdRisk.id,
      relatedType: 'risk'
    };
    
    const actionResponse = await fetch(`http://localhost:4000/projects/${createdProject.id}/actions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(newAction)
    });
    
    const createdAction = await actionResponse.json();
    console.log('Created action:', createdAction);
    
    // Create an issue
    console.log('\n5. Creating an issue...');
    const newIssue = {
      title: 'Authentication System Outage',
      description: 'Users unable to log in due to authentication service failure',
      owner: 'DevOps Team',
      severity: 'High',
      status: 'Open',
      relatedRiskId: createdRisk.id
    };
    
    const issueResponse = await fetch(`http://localhost:4000/projects/${createdProject.id}/issues`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(newIssue)
    });
    
    const createdIssue = await issueResponse.json();
    console.log('Created issue:', createdIssue);
    
    // Get the full RAID data
    console.log('\n6. Getting full RAID data...');
    const raidResponse = await fetch(`http://localhost:4000/projects/${createdProject.id}/raid`);
    const raidData = await raidResponse.json();
    console.log('RAID data:', JSON.stringify(raidData, null, 2));
    
    console.log('\n✅ API test completed successfully!');
    
  } catch (error) {
    console.error('❌ API test failed:', error.message);
  }
}

testAPI();
