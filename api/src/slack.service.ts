import { Injectable, Logger } from '@nestjs/common';
import axios from 'axios';

export interface SlackMessage {
  text?: string;
  blocks?: any[];
  attachments?: any[];
  channel?: string;
  username?: string;
  icon_emoji?: string;
  thread_ts?: string;
}

export interface SlackBlock {
  type: string;
  text?: {
    type: string;
    text: string;
  };
  elements?: any[];
  fields?: Array<{
    type: string;
    text: string;
  }>;
}

@Injectable()
export class SlackService {
  private readonly logger = new Logger(SlackService.name);
  private webhookUrl: string;
  private botToken: string;
  private defaultChannel: string;

  constructor() {
    this.webhookUrl = process.env.SLACK_WEBHOOK_URL || '';
    this.botToken = process.env.SLACK_BOT_TOKEN || '';
    this.defaultChannel = process.env.SLACK_DEFAULT_CHANNEL || '#raid-alerts';
  }

  /**
   * Send a simple text message to Slack
   */
  async sendMessage(text: string, channel?: string): Promise<boolean> {
    if (!this.webhookUrl) {
      this.logger.warn('SLACK_WEBHOOK_URL not configured');
      return false;
    }

    try {
      const message: SlackMessage = {
        text,
        channel: channel || this.defaultChannel,
        username: 'RAID Bot',
        icon_emoji: ':rotating_light:',
      };

      await axios.post(this.webhookUrl, message);
      this.logger.log(`Slack message sent: ${text.substring(0, 50)}...`);
      return true;
    } catch (error) {
      this.logger.error('Failed to send Slack message', error);
      return false;
    }
  }

  /**
   * Send a rich message with blocks to Slack
   */
  async sendRichMessage(blocks: SlackBlock[], channel?: string, text?: string): Promise<boolean> {
    if (!this.webhookUrl) {
      this.logger.warn('SLACK_WEBHOOK_URL not configured');
      return false;
    }

    try {
      const message: SlackMessage = {
        text: text || 'RAID Alert',
        blocks,
        channel: channel || this.defaultChannel,
        username: 'RAID Bot',
        icon_emoji: ':rotating_light:',
      };

      await axios.post(this.webhookUrl, message);
      this.logger.log('Rich Slack message sent');
      return true;
    } catch (error) {
      this.logger.error('Failed to send rich Slack message', error);
      return false;
    }
  }

  /**
   * Send a high-risk alert with rich formatting
   */
  async sendHighRiskAlert(
    projectName: string,
    riskTitle: string,
    score: number,
    owner?: string,
    dueDate?: Date,
    projectUrl?: string,
    channel?: string
  ): Promise<boolean> {
    const blocks: SlackBlock[] = [
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: '🚨 High Risk Alert',
        },
      },
      {
        type: 'section',
        fields: [
          {
            type: 'mrkdwn',
            text: `*Project:*\n${projectName}`,
          },
          {
            type: 'mrkdwn',
            text: `*Risk Score:*\n${score.toFixed(1)}`,
          },
          {
            type: 'mrkdwn',
            text: `*Risk Title:*\n${riskTitle}`,
          },
          {
            type: 'mrkdwn',
            text: `*Owner:*\n${owner || 'Unassigned'}`,
          },
        ],
      },
    ];

    if (dueDate) {
      blocks.push({
        type: 'section',
        fields: [
          {
            type: 'mrkdwn',
            text: `*Due Date:*\n${dueDate.toLocaleDateString()}`,
          },
        ],
      });
    }

    if (projectUrl) {
      blocks.push({
        type: 'actions',
        elements: [
          {
            type: 'button',
            text: {
              type: 'plain_text',
              text: 'View Project',
            },
            url: projectUrl,
            style: 'danger',
          },
        ],
      });
    }

    return this.sendRichMessage(blocks, channel);
  }

  /**
   * Send a weekly summary with project statistics
   */
  async sendWeeklySummary(
    projectName: string,
    stats: {
      totalRisks: number;
      highRisks: number;
      totalIssues: number;
      openIssues: number;
      totalActions: number;
      overdueActions: number;
      totalDecisions: number;
    },
    topRisks: Array<{ title: string; score: number; owner?: string }>,
    projectUrl?: string,
    channel?: string
  ): Promise<boolean> {
    const blocks: SlackBlock[] = [
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: `📊 Weekly RAID Summary - ${projectName}`,
        },
      },
      {
        type: 'section',
        fields: [
          {
            type: 'mrkdwn',
            text: `*Risks:*\n${stats.totalRisks} total, ${stats.highRisks} high-risk`,
          },
          {
            type: 'mrkdwn',
            text: `*Issues:*\n${stats.totalIssues} total, ${stats.openIssues} open`,
          },
          {
            type: 'mrkdwn',
            text: `*Actions:*\n${stats.totalActions} total, ${stats.overdueActions} overdue`,
          },
          {
            type: 'mrkdwn',
            text: `*Decisions:*\n${stats.totalDecisions} total`,
          },
        ],
      },
    ];

    if (topRisks.length > 0) {
      const riskList = topRisks
        .slice(0, 5)
        .map(risk => `• ${risk.title} (${risk.score.toFixed(1)})${risk.owner ? ` - ${risk.owner}` : ''}`)
        .join('\n');

      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*Top Risks:*\n${riskList}`,
        },
      });
    }

    if (projectUrl) {
      blocks.push({
        type: 'actions',
        elements: [
          {
            type: 'button',
            text: {
              type: 'plain_text',
              text: 'View Project',
            },
            url: projectUrl,
            style: 'primary',
          },
        ],
      });
    }

    return this.sendRichMessage(blocks, channel);
  }

  /**
   * Send a new issue notification
   */
  async sendNewIssueAlert(
    projectName: string,
    issueTitle: string,
    severity: string,
    owner?: string,
    projectUrl?: string,
    channel?: string
  ): Promise<boolean> {
    const severityEmoji = {
      Low: '🟢',
      Med: '🟡',
      High: '🟠',
      Critical: '🔴',
    }[severity] || '⚪';

    const blocks: SlackBlock[] = [
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: `${severityEmoji} New Issue: ${severity}`,
        },
      },
      {
        type: 'section',
        fields: [
          {
            type: 'mrkdwn',
            text: `*Project:*\n${projectName}`,
          },
          {
            type: 'mrkdwn',
            text: `*Issue:*\n${issueTitle}`,
          },
          {
            type: 'mrkdwn',
            text: `*Severity:*\n${severity}`,
          },
          {
            type: 'mrkdwn',
            text: `*Owner:*\n${owner || 'Unassigned'}`,
          },
        ],
      },
    ];

    if (projectUrl) {
      blocks.push({
        type: 'actions',
        elements: [
          {
            type: 'button',
            text: {
              type: 'plain_text',
              text: 'View Issue',
            },
            url: projectUrl,
            style: severity === 'Critical' ? 'danger' : 'primary',
          },
        ],
      });
    }

    return this.sendRichMessage(blocks, channel);
  }

  /**
   * Send a new action item notification
   */
  async sendNewActionAlert(
    projectName: string,
    actionTitle: string,
    owner?: string,
    dueDate?: Date,
    projectUrl?: string,
    channel?: string
  ): Promise<boolean> {
    const blocks: SlackBlock[] = [
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: '✅ New Action Item',
        },
      },
      {
        type: 'section',
        fields: [
          {
            type: 'mrkdwn',
            text: `*Project:*\n${projectName}`,
          },
          {
            type: 'mrkdwn',
            text: `*Action:*\n${actionTitle}`,
          },
          {
            type: 'mrkdwn',
            text: `*Owner:*\n${owner || 'Unassigned'}`,
          },
          {
            type: 'mrkdwn',
            text: `*Due Date:*\n${dueDate ? dueDate.toLocaleDateString() : 'No due date'}`,
          },
        ],
      },
    ];

    if (projectUrl) {
      blocks.push({
        type: 'actions',
        elements: [
          {
            type: 'button',
            text: {
              type: 'plain_text',
              text: 'View Action',
            },
            url: projectUrl,
            style: 'primary',
          },
        ],
      });
    }

    return this.sendRichMessage(blocks, channel);
  }

  /**
   * Send a decision notification
   */
  async sendDecisionAlert(
    projectName: string,
    decisionTitle: string,
    decidedBy?: string,
    rationale?: string,
    projectUrl?: string,
    channel?: string
  ): Promise<boolean> {
    const blocks: SlackBlock[] = [
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: '📋 New Decision',
        },
      },
      {
        type: 'section',
        fields: [
          {
            type: 'mrkdwn',
            text: `*Project:*\n${projectName}`,
          },
          {
            type: 'mrkdwn',
            text: `*Decision:*\n${decisionTitle}`,
          },
          {
            type: 'mrkdwn',
            text: `*Decided By:*\n${decidedBy || 'Unknown'}`,
          },
        ],
      },
    ];

    if (rationale) {
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*Rationale:*\n${rationale}`,
        },
      });
    }

    if (projectUrl) {
      blocks.push({
        type: 'actions',
        elements: [
          {
            type: 'button',
            text: {
              type: 'plain_text',
              text: 'View Decision',
            },
            url: projectUrl,
            style: 'primary',
          },
        ],
      });
    }

    return this.sendRichMessage(blocks, channel);
  }

  /**
   * Test Slack connectivity
   */
  async testConnection(): Promise<boolean> {
    if (!this.webhookUrl) {
      this.logger.warn('SLACK_WEBHOOK_URL not configured');
      return false;
    }

    try {
      await this.sendMessage('🧪 RAID Bot test message - Slack integration is working!');
      return true;
    } catch (error) {
      this.logger.error('Slack connection test failed', error);
      return false;
    }
  }
}
