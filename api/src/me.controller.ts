import { Controller, Get, Req } from '@nestjs/common';
import { PrismaService } from './prisma.service';
import { AuthService } from './auth.service';

@Controller('me')
export class MeController {
  constructor(private prisma: PrismaService, private auth: AuthService) {}

  @Get()
  async me(@Req() req: any) {
    const user = await this.auth.verifyBearer(req.headers['authorization']);
    if (!user?.sub) return { authenticated: false, role: 'Viewer' };
    const db = await this.prisma.user.upsert({
      where: { id: user.sub },
      update: {},
      create: { id: user.sub, email: user.email, name: user.name, role: 'Viewer' },
    });
    return { authenticated: true, sub: user.sub, email: user.email, name: user.name, role: db.role };
  }
}
