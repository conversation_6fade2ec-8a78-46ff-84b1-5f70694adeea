import { Modu<PERSON> } from '@nestjs/common';
import { PrismaService } from './prisma.service';
import { AuthService } from './auth.service';
import { APP_GUARD } from '@nestjs/core';
import { RolesGuard } from './roles.guard';
import { MeController } from './me.controller';
import { NotificationsService } from './notifications.service';
import { SlackService } from './slack.service';
import { SchedulerModule } from './scheduler.module';
import { ImportExportController } from './import-export.controller';
import { WebhooksController } from './webhooks.controller';
import { UsersController } from './users.controller';
import { NotificationsPreviewController } from './notifications-preview.controller';
import { SlackController } from './slack.controller';
import { ProjectsController } from './projects.controller';
import { RaidController } from './raid.controller';
import { RisksController } from './risks.controller';
import { IssuesController } from './issues.controller';
import { ActionsController } from './actions.controller';
import { DecisionsController } from './decisions.controller';

@Module({
  imports: [SchedulerModule],
  controllers: [ProjectsController, RaidController, RisksController, IssuesController, ActionsController, DecisionsController, MeController, ImportExportController, UsersController, WebhooksController, NotificationsPreviewController, SlackController],
  providers: [
    PrismaService,
    AuthService,
    NotificationsService,
    SlackService,
    // Temporarily disable global roles guard for development
    // { provide: APP_GUARD, useClass: RolesGuard },
  ],
})
export class AppModule {}
