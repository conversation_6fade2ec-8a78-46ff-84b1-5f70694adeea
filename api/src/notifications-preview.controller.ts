import { Body, Controller, Post } from '@nestjs/common';
import { PrismaService } from './prisma.service';

@Controller('notifications/preview')
export class NotificationsPreviewController {
  constructor(private prisma: PrismaService) {}

  @Post('high-risk')
  async previewHighRisk(@Body() body: any) {
    const { projectId, title = '', probability = 0, impact = 0 } = body || {};
    const project = await this.prisma.project.findUnique({ where: { id: projectId } });
    const score = Number(probability) * Number(impact);
    const threshold = Number(process.env.HIGH_SCORE_THRESHOLD || 3);
    const willNotify = score >= threshold;
    const web = process.env.FRONTEND_BASE_URL || 'http://localhost:3000';
    const slackText = `:rotating_light: High risk in *${project?.name || 'Project'}*: *${title}* (score ${score.toFixed(1)})\n${web}/projects/${projectId}`;
    const teamsCard = {
      $schema: 'http://adaptivecards.io/schemas/adaptive-card.json',
      type: 'AdaptiveCard',
      version: '1.5',
      body: [
        { type: 'TextBlock', text: 'High risk logged', weight: 'Bolder', size: 'Medium' },
        { type: 'TextBlock', text: `Project: ${project?.name || 'Project'}`, wrap: true },
        { type: 'TextBlock', text: `Title: ${title}`, wrap: true },
        { type: 'TextBlock', text: `Score: ${score.toFixed(1)}`, wrap: true }
      ],
      actions: [
        { type: 'Action.OpenUrl', title: 'Open Project', url: `${web}/projects/${projectId}` }
      ]
    };
    return { willNotify, threshold, score, slackText, teamsCard };
  }
}
