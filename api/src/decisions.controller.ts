import { Body, Controller, Get, Param, <PERSON>, Post } from '@nestjs/common';
import { PrismaService } from './prisma.service';
import { SlackService } from './slack.service';
import { Roles } from './roles.decorator';

@Controller()
export class DecisionsController {
  constructor(private prisma: PrismaService, private slack: SlackService) {}

  @Roles('Contributor','PM','Admin')
  @Post('projects/:projectId/decisions')
  async create(@Param('projectId') projectId: string, @Body() body: {
    title: string;
    description?: string;
    decidedBy?: string;
    decidedOn?: string;
    rationale?: string;
    relatedId?: string;
    relatedRiskId?: string;
  }) {
    const { title, description, decidedBy, decidedOn, rationale, relatedId, relatedRiskId } = body;
    
    const created = await this.prisma.decision.create({
      data: {
        projectId,
        title,
        description,
        decidedBy,
        decidedOn: decidedOn ? new Date(decidedOn) : new Date(),
        rationale,
      },
    });

    // Send Slack notification for new decision
    const project = await this.prisma.project.findUnique({ where: { id: projectId } });
    const projectUrl = `${process.env.FRONTEND_BASE_URL || 'http://localhost:3000'}/projects/${projectId}`;
    
    await this.slack.sendDecisionAlert(
      project?.name || 'Project',
      created.title,
      created.decidedBy || undefined,
      created.rationale || undefined,
      projectUrl
    );

    return created;
  }

  @Roles('Contributor','PM','Admin')
  @Patch('decisions/:id')
  async update(@Param('id') id: string, @Body() body: Partial<{
    title: string;
    description: string;
    decidedBy: string;
    decidedOn: string;
    rationale: string;
    relatedId: string;
    relatedRiskId: string;
  }>) {
    const data: any = { ...body };
    if (data.decidedOn) data.decidedOn = new Date(data.decidedOn);
    
    return this.prisma.decision.update({ where: { id }, data });
  }

  @Get('decisions/:id')
  async get(@Param('id') id: string) {
    return this.prisma.decision.findUnique({ where: { id } });
  }
}
