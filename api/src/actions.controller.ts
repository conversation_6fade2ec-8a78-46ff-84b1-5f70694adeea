import { Body, Controller, Get, Param, Patch, Post } from '@nestjs/common';
import { PrismaService } from './prisma.service';
import { SlackService } from './slack.service';
import { Roles } from './roles.decorator';

@Controller()
export class ActionsController {
  constructor(private prisma: PrismaService, private slack: SlackService) {}

  @Roles('Contributor','PM','Admin')
  @Post('projects/:projectId/actions')
  async create(@Param('projectId') projectId: string, @Body() body: {
    title: string;
    owner?: string;
    status?: string;
    dueAt?: string;
    completedAt?: string;
    relatedId?: string;
    relatedRiskId?: string;
  }) {
    const { title, owner, status = 'Open', dueAt, completedAt, relatedId, relatedRiskId } = body;
    
    const created = await this.prisma.action.create({
      data: {
        projectId,
        title,
        owner,
        status,
        dueAt: dueAt ? new Date(dueAt) : undefined,
        completedAt: completedAt ? new Date(completedAt) : undefined,
        relatedId,
        relatedType: relatedRiskId ? 'risk' : undefined,
      },
    });

    // Send Slack notification for new action
    const project = await this.prisma.project.findUnique({ where: { id: projectId } });
    const projectUrl = `${process.env.FRONTEND_BASE_URL || 'http://localhost:3000'}/projects/${projectId}`;
    
    await this.slack.sendNewActionAlert(
      project?.name || 'Project',
      created.title,
      created.owner || undefined,
      created.dueAt || undefined,
      projectUrl
    );

    return created;
  }

  @Roles('Contributor','PM','Admin')
  @Patch('actions/:id')
  async update(@Param('id') id: string, @Body() body: Partial<{
    title: string;
    description: string;
    owner: string;
    status: string;
    dueAt: string;
    completedAt: string;
    relatedId: string;
    relatedRiskId: string;
  }>) {
    const data: any = { ...body };
    if (data.dueAt) data.dueAt = new Date(data.dueAt);
    if (data.completedAt) data.completedAt = new Date(data.completedAt);
    
    const updated = await this.prisma.action.update({ where: { id }, data });
    
    // Send notification if action was completed
    if (body.status === 'Completed' && updated.status === 'Completed') {
      const project = await this.prisma.project.findUnique({ where: { id: updated.projectId } });
      const projectUrl = `${process.env.FRONTEND_BASE_URL || 'http://localhost:3000'}/projects/${updated.projectId}`;
      
      await this.slack.sendMessage(
        `✅ Action completed in *${project?.name || 'Project'}*: *${updated.title}*`,
        undefined
      );
    }
    
    return updated;
  }

  @Get('actions/:id')
  async get(@Param('id') id: string) {
    return this.prisma.action.findUnique({ where: { id } });
  }
}
