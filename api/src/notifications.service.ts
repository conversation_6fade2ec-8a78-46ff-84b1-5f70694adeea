import { Injectable } from '@nestjs/common';
import axios from 'axios';
import { SlackService } from './slack.service';

@Injectable()
export class NotificationsService {
  private slack = process.env.SLACK_WEBHOOK_URL;
  private teams = process.env.TEAMS_WEBHOOK_URL;
  private web = process.env.FRONTEND_BASE_URL || 'http://localhost:3000';

  constructor(private slackService: SlackService) {}

  async notifyHighRisk(projectName: string, title: string, score: number, url?: string, owner?: string, dueAt?: Date) {
    const promises: Promise<any>[] = [];
    
    // Use enhanced Slack service if available
    if (this.slack) {
      promises.push(this.slackService.sendHighRiskAlert(projectName, title, score, owner, dueAt, url));
    }
    
    // Fallback to simple Teams notification
    if (this.teams) {
      const text = `:rotating_light: High risk in *${projectName}*: *${title}* (score ${score.toFixed(1)})` + (url ? `\n${url}` : '');
      promises.push(axios.post(this.teams, { text }));
    }
    
    await Promise.allSettled(promises);
  }

  async notifyWeeklySummary(projectName: string, lines: string[], topRisks?: { title: string; score: number }[], projectId?: string) {
    const promises: Promise<any>[] = [];
    
    // Use enhanced Slack service if available
    if (this.slack) {
      // For now, use simple text format for weekly summary
      // In a real implementation, you'd want to pass more detailed stats
      const text = `*Weekly RAID Summary — ${projectName}*\n` + lines.join('\n');
      promises.push(axios.post(this.slack, { text }));
    }
    
    // Teams notification with adaptive cards
    if (this.teams) {
      try {
        const riskList = (topRisks || []).map(r => `• ${r.title} (score ${r.score.toFixed(1)})`).join('\n') || 'none';
        const card: any = {
          '$schema': 'http://adaptivecards.io/schemas/adaptive-card.json',
          'type': 'AdaptiveCard',
          'version': '1.5',
          'body': [
            { 'type': 'TextBlock', 'text': `Weekly RAID Summary — ${projectName}`, 'size': 'Medium', 'weight': 'Bolder' },
            { 'type': 'TextBlock', 'text': lines.join('\n'), 'wrap': true, 'spacing': 'Medium' },
            { 'type': 'TextBlock', 'text': 'Top risks', 'weight': 'Bolder', 'spacing': 'Medium' },
            { 'type': 'TextBlock', 'text': riskList, 'wrap': true }
          ],
          'actions': []
        };
        if (projectId) {
          card.actions.push({ 'type': 'Action.OpenUrl', 'title': 'Open Project', 'url': `${this.web}/projects/${projectId}` });
          card.actions.push({ 'type': 'Action.OpenUrl', 'title': 'View Risks', 'url': `${this.web}/projects/${projectId}#risks` });
        }
        promises.push(axios.post(this.teams, { type: 'message', attachments: [{ contentType: 'application/vnd.microsoft.card.adaptive', content: card }] }));
      } catch (e) {
        const text = `*Weekly RAID Summary — ${projectName}*\n` + lines.join('\n');
        promises.push(axios.post(this.teams, { text }));
      }
    }
    
    await Promise.allSettled(promises);
  }
}
