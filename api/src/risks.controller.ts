import { Body, Controller, Get, Param, Patch, Post } from '@nestjs/common';
import { PrismaService } from './prisma.service';
import { NotificationsService } from './notifications.service';
import { SlackService } from './slack.service';
import { Roles } from './roles.decorator';

@Controller()
export class RisksController {
  constructor(private prisma: PrismaService, private notify: NotificationsService, private slack: SlackService) {}

  @Roles('Contributor','PM','Admin')
  @Post('projects/:projectId/risks')
  async create(@Param('projectId') projectId: string, @Body() body: {
    title: string;
    trigger?: string;
    probability: number;
    impact: number;
    response?: string;
    owner?: string;
    status?: string;
    dueAt?: string;
    links?: string[];
    notifyAnyway?: boolean;
  }) {
    const { title, trigger, probability, impact, response, owner, status = 'Open', dueAt, links = [], notifyAnyway = false } = body;
    const score = Number(probability) * Number(impact);
    const created = await this.prisma.risk.create({
      data: { projectId, title, trigger, probability: Number(probability), impact: Number(impact), score, response, owner, status, dueAt, links },
    });
    const threshold = Number(process.env.HIGH_SCORE_THRESHOLD || 3);
    const web = process.env.FRONTEND_BASE_URL || 'http://localhost:3000';
    
    // Send notifications for high-risk items
    if (notifyAnyway || (created.score ?? 0) >= threshold) {
      const project = await this.prisma.project.findUnique({ where: { id: projectId } });
      const projectUrl = `${web}/projects/${projectId}`;
      
      // Send enhanced Slack notification
      await this.slack.sendHighRiskAlert(
        project?.name || 'Project',
        created.title,
        created.score,
        created.owner || undefined,
        created.dueAt || undefined,
        projectUrl
      );
      
      // Also send via legacy notification service
      await this.notify.notifyHighRisk(project?.name || 'Project', created.title, created.score, projectUrl, created.owner || undefined, created.dueAt || undefined);
    }
    return created;
  }

  @Roles('Contributor','PM','Admin')
  @Patch('risks/:id')
  async update(@Param('id') id: string, @Body() body: Partial<{
    title: string;
    trigger: string;
    probability: number;
    impact: number;
    response: string;
    owner: string;
    status: string;
    dueAt: string;
    links: string[];
  }>) {
    const data: any = { ...body };
    const threshold = Number(process.env.HIGH_SCORE_THRESHOLD || 3);
    const web = process.env.FRONTEND_BASE_URL || 'http://localhost:3000';
    
    if (data.probability != null || data.impact != null) {
      const risk = await this.prisma.risk.findUnique({ where: { id } });
      const p = data.probability != null ? Number(data.probability) : risk?.probability || 0;
      const i = data.impact != null ? Number(data.impact) : risk?.impact || 0;
      data.score = p * i;
      
      // Check if this update makes it a high-risk item
      if (data.score >= threshold && (risk?.score || 0) < threshold && risk) {
        const project = await this.prisma.project.findUnique({ where: { id: risk.projectId } });
        const projectUrl = `${web}/projects/${risk.projectId}`;
        
        // Send Slack notification for newly high-risk item
        await this.slack.sendHighRiskAlert(
          project?.name || 'Project',
          risk.title,
          data.score,
          risk.owner || undefined,
          risk.dueAt || undefined,
          projectUrl
        );
      }
    }
    
    return this.prisma.risk.update({ where: { id }, data });
  }

  @Get('risks/:id')
  async get(@Param('id') id: string) {
    return this.prisma.risk.findUnique({ where: { id } });
  }


  @Roles('Contributor','PM','Admin')
  @Post('risks/:id/convert-to-issue')
  async convertToIssue(@Param('id') id: string) {
    const risk = await this.prisma.risk.findUnique({ where: { id } });
    if (!risk) return null;
    // derive severity from score
    const score = (risk.score ?? 0);
    const severity = score >= 4 ? 'Critical' : score >= 3 ? 'High' : score >= 2 ? 'Med' : 'Low';
    const issue = await this.prisma.issue.create({
      data: {
        projectId: risk.projectId,
        title: risk.title,
        description: `Converted from risk. Trigger: ${risk.trigger || '-'}\nResponse: ${risk.response || '-'}`,
        owner: risk.owner || undefined,
        severity,
        status: 'Open',
        relatedRiskId: risk.id,
      },
    });
    // Optionally update risk status
    await this.prisma.risk.update({ where: { id: risk.id }, data: { status: 'In progress' } });
    
    // Send Slack notification for new issue
    const project = await this.prisma.project.findUnique({ where: { id: risk.projectId } });
    const projectUrl = `${process.env.FRONTEND_BASE_URL || 'http://localhost:3000'}/projects/${risk.projectId}`;
    
    await this.slack.sendNewIssueAlert(
      project?.name || 'Project',
      issue.title,
      issue.severity || 'Low',
      issue.owner || undefined,
      projectUrl
    );
    
    return { issue };
  }
}
