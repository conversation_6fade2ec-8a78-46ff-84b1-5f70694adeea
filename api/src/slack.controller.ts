import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { SlackService } from './slack.service';
import { PrismaService } from './prisma.service';

@Controller('slack')
export class SlackController {
  constructor(
    private slackService: SlackService,
    private prisma: PrismaService
  ) {}

  /**
   * Test Slack connection
   */
  @Get('test')
  async testConnection() {
    const isConnected = await this.slackService.testConnection();
    return {
      connected: isConnected,
      message: isConnected ? 'Slack integration is working!' : 'Slack integration failed. Check your webhook URL.',
    };
  }

  /**
   * Send a custom message to Slack
   */
  @Post('send-message')
  async sendCustomMessage(
    @Body() body: {
      text: string;
      channel?: string;
    }
  ) {
    const { text, channel } = body;
    const success = await this.slackService.sendMessage(text, channel);
    return {
      success,
      message: success ? 'Message sent successfully' : 'Failed to send message',
    };
  }

  /**
   * Send a high-risk alert for a specific risk
   */
  @Post('alert/high-risk')
  async sendHighRiskAlert(
    @Body() body: {
      riskId: string;
      channel?: string;
    }
  ) {
    const { riskId, channel } = body;
    
    try {
      const risk = await this.prisma.risk.findUnique({
        where: { id: riskId },
        include: { project: true },
      });

      if (!risk) {
        return { success: false, message: 'Risk not found' };
      }

      const projectUrl = `${process.env.FRONTEND_BASE_URL || 'http://localhost:3000'}/projects/${risk.projectId}`;
      
      const success = await this.slackService.sendHighRiskAlert(
        risk.project.name,
        risk.title,
        risk.score,
        risk.owner || undefined,
        risk.dueAt || undefined,
        projectUrl,
        channel
      );

      return {
        success,
        message: success ? 'High-risk alert sent' : 'Failed to send alert',
      };
    } catch (error) {
      return {
        success: false,
        message: 'Error sending alert: ' + (error instanceof Error ? error.message : String(error)),
      };
    }
  }

  /**
   * Send a weekly summary for a project
   */
  @Post('summary/weekly')
  async sendWeeklySummary(
    @Body() body: {
      projectId: string;
      channel?: string;
    }
  ) {
    const { projectId, channel } = body;
    
    try {
      const project = await this.prisma.project.findUnique({
        where: { id: projectId },
      });

      if (!project) {
        return { success: false, message: 'Project not found' };
      }

      // Get project statistics
      const [risks, issues, actions, decisions] = await Promise.all([
        this.prisma.risk.findMany({ where: { projectId } }),
        this.prisma.issue.findMany({ where: { projectId } }),
        this.prisma.action.findMany({ where: { projectId } }),
        this.prisma.decision.findMany({ where: { projectId } }),
      ]);

      const highRisks = risks.filter(r => r.score >= 3);
      const openIssues = issues.filter(i => i.status !== 'Resolved');
      const overdueActions = actions.filter(a => a.dueAt && a.dueAt < new Date() && !a.completedAt);
      const topRisks = risks
        .sort((a, b) => b.score - a.score)
        .slice(0, 5)
        .map(r => ({
          title: r.title,
          score: r.score,
          owner: r.owner || undefined,
        }));

      const stats = {
        totalRisks: risks.length,
        highRisks: highRisks.length,
        totalIssues: issues.length,
        openIssues: openIssues.length,
        totalActions: actions.length,
        overdueActions: overdueActions.length,
        totalDecisions: decisions.length,
      };

      const projectUrl = `${process.env.FRONTEND_BASE_URL || 'http://localhost:3000'}/projects/${projectId}`;
      
      const success = await this.slackService.sendWeeklySummary(
        project.name,
        stats,
        topRisks,
        projectUrl,
        channel
      );

      return {
        success,
        message: success ? 'Weekly summary sent' : 'Failed to send summary',
        stats,
      };
    } catch (error) {
      return {
        success: false,
        message: 'Error sending summary: ' + (error instanceof Error ? error.message : String(error)),
      };
    }
  }

  /**
   * Send a new issue alert
   */
  @Post('alert/new-issue')
  async sendNewIssueAlert(
    @Body() body: {
      issueId: string;
      channel?: string;
    }
  ) {
    const { issueId, channel } = body;
    
    try {
      const issue = await this.prisma.issue.findUnique({
        where: { id: issueId },
        include: { project: true },
      });

      if (!issue) {
        return { success: false, message: 'Issue not found' };
      }

      const projectUrl = `${process.env.FRONTEND_BASE_URL || 'http://localhost:3000'}/projects/${issue.projectId}`;
      
      const success = await this.slackService.sendNewIssueAlert(
        issue.project.name,
        issue.title,
        issue.severity || 'Unknown',
        issue.owner || undefined,
        projectUrl,
        channel
      );

      return {
        success,
        message: success ? 'Issue alert sent' : 'Failed to send alert',
      };
    } catch (error) {
      return {
        success: false,
        message: 'Error sending alert: ' + (error instanceof Error ? error.message : String(error)),
      };
    }
  }

  /**
   * Send a new action alert
   */
  @Post('alert/new-action')
  async sendNewActionAlert(
    @Body() body: {
      actionId: string;
      channel?: string;
    }
  ) {
    const { actionId, channel } = body;
    
    try {
      const action = await this.prisma.action.findUnique({
        where: { id: actionId },
        include: { project: true },
      });

      if (!action) {
        return { success: false, message: 'Action not found' };
      }

      const projectUrl = `${process.env.FRONTEND_BASE_URL || 'http://localhost:3000'}/projects/${action.projectId}`;
      
      const success = await this.slackService.sendNewActionAlert(
        action.project.name,
        action.title,
        action.owner || undefined,
        action.dueAt || undefined,
        projectUrl,
        channel
      );

      return {
        success,
        message: success ? 'Action alert sent' : 'Failed to send alert',
      };
    } catch (error) {
      return {
        success: false,
        message: 'Error sending alert: ' + (error instanceof Error ? error.message : String(error)),
      };
    }
  }

  /**
   * Send a decision alert
   */
  @Post('alert/new-decision')
  async sendDecisionAlert(
    @Body() body: {
      decisionId: string;
      channel?: string;
    }
  ) {
    const { decisionId, channel } = body;
    
    try {
      const decision = await this.prisma.decision.findUnique({
        where: { id: decisionId },
        include: { project: true },
      });

      if (!decision) {
        return { success: false, message: 'Decision not found' };
      }

      const projectUrl = `${process.env.FRONTEND_BASE_URL || 'http://localhost:3000'}/projects/${decision.projectId}`;
      
      const success = await this.slackService.sendDecisionAlert(
        decision.project.name,
        decision.title,
        decision.decidedBy || undefined,
        decision.rationale || undefined,
        projectUrl,
        channel
      );

      return {
        success,
        message: success ? 'Decision alert sent' : 'Failed to send alert',
      };
    } catch (error) {
      return {
        success: false,
        message: 'Error sending alert: ' + (error instanceof Error ? error.message : String(error)),
      };
    }
  }

  /**
   * Get Slack configuration status
   */
  @Get('config')
  async getConfig() {
    return {
      webhookConfigured: !!process.env.SLACK_WEBHOOK_URL,
      botTokenConfigured: !!process.env.SLACK_BOT_TOKEN,
      defaultChannel: process.env.SLACK_DEFAULT_CHANNEL || '#raid-alerts',
      frontendUrl: process.env.FRONTEND_BASE_URL || 'http://localhost:3000',
    };
  }
}
