import { Body, Controller, Get, Param, Patch, Post } from '@nestjs/common';
import { PrismaService } from './prisma.service';
import { SlackService } from './slack.service';
import { Roles } from './roles.decorator';

@Controller()
export class IssuesController {
  constructor(private prisma: PrismaService, private slack: SlackService) {}

  @Roles('Contributor','PM','Admin')
  @Post('projects/:projectId/issues')
  async create(@Param('projectId') projectId: string, @Body() body: {
    title: string;
    description?: string;
    owner?: string;
    severity?: string;
    status?: string;
    resolution?: string;
    relatedRiskId?: string;
  }) {
    const { title, description, owner, severity = 'Low', status = 'Open', resolution, relatedRiskId } = body;
    
    const created = await this.prisma.issue.create({
      data: {
        projectId,
        title,
        description,
        owner,
        severity,
        status,
        resolution,
        relatedRiskId,
      },
    });

    // Send Slack notification for new issue
    const project = await this.prisma.project.findUnique({ where: { id: projectId } });
    const projectUrl = `${process.env.FRONTEND_BASE_URL || 'http://localhost:3000'}/projects/${projectId}`;
    
    await this.slack.sendNewIssueAlert(
      project?.name || 'Project',
      created.title,
      created.severity || 'Low',
      created.owner || undefined,
      projectUrl
    );

    return created;
  }

  @Roles('Contributor','PM','Admin')
  @Patch('issues/:id')
  async update(@Param('id') id: string, @Body() body: Partial<{
    title: string;
    description: string;
    owner: string;
    severity: string;
    status: string;
    resolution: string;
    relatedRiskId: string;
  }>) {
    const updated = await this.prisma.issue.update({ where: { id }, data: body });
    
    // Send notification if status changed to resolved
    if (body.status === 'Resolved' && updated.status === 'Resolved') {
      const project = await this.prisma.project.findUnique({ where: { id: updated.projectId } });
      const projectUrl = `${process.env.FRONTEND_BASE_URL || 'http://localhost:3000'}/projects/${updated.projectId}`;
      
      await this.slack.sendMessage(
        `✅ Issue resolved in *${project?.name || 'Project'}*: *${updated.title}*`,
        undefined
      );
    }
    
    return updated;
  }

  @Get('issues/:id')
  async get(@Param('id') id: string) {
    return this.prisma.issue.findUnique({ where: { id } });
  }
}
