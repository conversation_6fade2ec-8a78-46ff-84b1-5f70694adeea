import { Injectable } from '@nestjs/common';
import { createRemoteJWKSet, jwtVerify, JWK, JWTPayload } from 'jose';
import axios from 'axios';

type AuthUser = { sub: string; email?: string; name?: string };

@Injectable()
export class AuthService {
  private jwks: ReturnType<typeof createRemoteJWKSet> | null = null;
  private issuer = process.env.AUTH_ISSUER;
  private audience = process.env.AUTH_AUDIENCE;
  private jwksUri = process.env.AUTH_JWKS_URI;
  private devSecret = process.env.DEV_JWT_SECRET || 'devsecret';

  constructor() {
    if (this.jwksUri) {
      this.jwks = createRemoteJWKSet(new URL(this.jwksUri));
    }
  }

  async verifyBearer(authorization?: string): Promise<AuthUser | null> {
    if (!authorization?.startsWith('Bearer ')) return null;
    const token = authorization.slice('Bearer '.length);

    // If configured for real verification (Clerk/Auth0)
    if (this.jwks && this.issuer) {
      const { payload } = await jwtVerify(token, this.jwks, {
        issuer: this.issuer,
        audience: this.audience,
      });
      return { sub: String(payload.sub), email: (payload as any).email, name: (payload as any).name };
    }

    // Dev fallback: accept unsigned JWT-like base64 or bypass
    try {
      const parts = token.split('.');
      if (parts.length === 3) {
        const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString('utf8')) as JWTPayload;
        return { sub: String(payload.sub || 'dev-user'), email: (payload as any).email, name: (payload as any).name };
      }
    } catch {}
    return null;
  }
}
