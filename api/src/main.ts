import 'dotenv/config';
import { NestFactory } from '@nestjs/core';
import * as bodyParser from 'body-parser';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule, { cors: true });
  const port = process.env.PORT || 4000;
  // Raw body for Clerk webhook signature verification
  app.use('/webhooks/clerk', bodyParser.raw({ type: 'application/json' }));
  await app.listen(port);
  console.log(`API listening on http://localhost:${port}`);
}
bootstrap();
