import { Module } from '@nestjs/common';
import { ScheduleModule, Cron } from '@nestjs/schedule';
import { PrismaService } from './prisma.service';
import { NotificationsService } from './notifications.service';
import { SlackService } from './slack.service';

class WeeklySummaryJob {
  private cron = process.env.WEEKLY_SUMMARY_CRON || '0 13 * * 1';
  private timezone = process.env.TIMEZONE || 'America/New_York';
  constructor(private prisma: PrismaService, private notify: NotificationsService, private slack: SlackService) {}

  @Cron('0 13 * * 1', { timeZone: 'America/New_York' })
  async handle() {
    const projects = await this.prisma.project.findMany();
    for (const p of projects) {
      const risks = await this.prisma.risk.findMany({ where: { projectId: p.id }, orderBy: [{ score: 'desc' }] });
      const issues = await this.prisma.issue.findMany({ where: { projectId: p.id, status: { not: 'Resolved' } } });
      const actions = await this.prisma.action.findMany({ where: { projectId: p.id } });
      const decisions = await this.prisma.decision.findMany({ where: { projectId: p.id } });
      const upcoming = actions.filter(a => a.dueAt && a.dueAt < new Date(Date.now() + 7*24*60*60*1000));
      const overdue = actions.filter(a => a.dueAt && a.dueAt < new Date() && !a.completedAt);

      // Calculate statistics
      const highRisks = risks.filter(r => r.score >= 3);
      const topRisks = risks.slice(0, 5).map(r => ({
        title: r.title,
        score: r.score,
        owner: r.owner || undefined,
      }));

      const stats = {
        totalRisks: risks.length,
        highRisks: highRisks.length,
        totalIssues: issues.length,
        openIssues: issues.length,
        totalActions: actions.length,
        overdueActions: overdue.length,
        totalDecisions: decisions.length,
      };

      const projectUrl = `${process.env.FRONTEND_BASE_URL || 'http://localhost:3000'}/projects/${p.id}`;

      // Send enhanced Slack weekly summary
      await this.slack.sendWeeklySummary(
        p.name,
        stats,
        topRisks,
        projectUrl
      );

      // Also send legacy notification
      const lines: string[] = [];
      lines.push(`• High risks: ${risks.slice(0, 5).map(r => `${r.title} (score ${r.score.toFixed(1)})`).join('; ') || 'none'}`);
      lines.push(`• Unresolved issues: ${issues.length}`);
      lines.push(`• Actions due next 7 days: ${upcoming.length}`);
      await this.notify.notifyWeeklySummary(p.name, lines, risks.slice(0, 5).map(r => ({ title: r.title, score: r.score })) as any, p.id);
    }
  }
}

@Module({
  imports: [ScheduleModule.forRoot()],
  providers: [PrismaService, NotificationsService, SlackService, WeeklySummaryJob],
})
export class SchedulerModule {}
