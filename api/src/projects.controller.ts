import { Body, Controller, Get, Param, Post } from '@nestjs/common';
import { Roles } from './roles.decorator';
import { PrismaService } from './prisma.service';

@Controller('projects')
export class ProjectsController {
  constructor(private prisma: PrismaService) {}

  @Get()
  async list() {
    const projects = await this.prisma.project.findMany({
      orderBy: { createdAt: 'desc' },
    });
    return projects;
  }

  @Post()
  async create(@Body() body: { name: string; cadence?: string; categories?: string[] }) {
    const { name, cadence = 'weekly', categories = ['assumptions','dependencies'] } = body;
    return this.prisma.project.create({ data: { name, cadence, categories } });
  }

  @Get(':id')
  async get(@Param('id') id: string) {
    const project = await this.prisma.project.findUnique({ where: { id } });
    if (!project) return null;

    const [riskCount, issueCount, actionCount, decisionCount] = await Promise.all([
      this.prisma.risk.count({ where: { projectId: id } }),
      this.prisma.issue.count({ where: { projectId: id } }),
      this.prisma.action.count({ where: { projectId: id } }),
      this.prisma.decision.count({ where: { projectId: id } }),
    ]);

    return { ...project, aggregates: { risks: riskCount, issues: issueCount, actions: actionCount, decisions: decisionCount } };
  }
}
