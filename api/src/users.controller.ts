import { Body, Controller, Get, Param, Patch } from '@nestjs/common';
import { PrismaService } from './prisma.service';
import { Roles } from './roles.decorator';

@Controller('users')
export class UsersController {
  constructor(private prisma: PrismaService) {}

  @Roles('Admin')
  @Get()
  async list() {
    return this.prisma.user.findMany({ orderBy: { createdAt: 'desc' }, select: { id: true, email: true, name: true, role: true, createdAt: true } });
  }

  @Roles('Admin')
  @Patch(':id')
  async setRole(@Param('id') id: string, @Body() body: any) {
    const role = String(body.role || 'Viewer');
    return this.prisma.user.update({ where: { id }, data: { role } });
  }
}
