import { Body, Controller, Get, Header, Param, Post, Query, Res } from '@nestjs/common';
import { PrismaService } from './prisma.service';
import { Parser as Json2Csv } from 'json2csv';
import ExcelJS from 'exceljs';
import { Response } from 'express';

@Controller('projects/:projectId')
export class ImportExportController {
  constructor(private prisma: PrismaService) {}

  @Get('export')
  @Header('Cache-Control', 'no-store')
  async export(@Param('projectId') projectId: string, @Query('format') format: string = 'xlsx', @Res() res: Response, @Query('category') category?: string) {
    if (format === 'csv') {
      const data = await this.fetchCategory(projectId, category || 'risks');
      const parser = new Json2Csv();
      const csv = parser.parse(data);
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="${category || 'risks'}.csv"`);
      return res.send(csv);
    }
    // xlsx (default)
    const wb = new ExcelJS.Workbook();
    const sheets = [
      ['Risks', await this.prisma.risk.findMany({ where: { projectId } })],
      ['Issues', await this.prisma.issue.findMany({ where: { projectId } })],
      ['Actions', await this.prisma.action.findMany({ where: { projectId } })],
      ['Decisions', await this.prisma.decision.findMany({ where: { projectId } })],
    ] as const;
    for (const [name, rows] of sheets) {
      const ws = wb.addWorksheet(name);
      if (rows.length === 0) continue;
      ws.columns = Object.keys(rows[0]).map((k) => ({ header: k, key: k }));
      rows.forEach((r: any) => ws.addRow(r));
    }
    const buffer = await wb.xlsx.writeBuffer();
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename="raid-export.xlsx"');
    return res.send(Buffer.from(buffer));
  }

  @Post('import')
  async import(@Param('projectId') projectId: string, @Body() body: any, @Query('category') category: string = 'risks') {
    // Expect JSON array (front-end transforms CSV/XLSX to JSON client-side for simplicity)
    const items = Array.isArray(body) ? body : body?.items || [];
    if (category === 'risks') {
      for (const r of items) {
        const score = Number(r.probability || 0) * Number(r.impact || 0);
        await this.prisma.risk.create({ data: { projectId, title: r.title, trigger: r.trigger, probability: Number(r.probability) || 0, impact: Number(r.impact) || 0, score, response: r.response, owner: r.owner, status: r.status || 'Open', dueAt: r.dueAt ? new Date(r.dueAt) : null, links: r.links || [] } as any });
      }
    }
    // (Similar blocks could be added for issues/actions/decisions)
    return { imported: items.length, category };
  }

  private async fetchCategory(projectId: string, category: string) {
    switch (category) {
      case 'risks': return this.prisma.risk.findMany({ where: { projectId } });
      case 'issues': return this.prisma.issue.findMany({ where: { projectId } });
      case 'actions': return this.prisma.action.findMany({ where: { projectId } });
      case 'decisions': return this.prisma.decision.findMany({ where: { projectId } });
      default: return [];
    }
  }
}
