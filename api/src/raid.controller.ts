import { Controller, Get, Param, Query } from '@nestjs/common';
import { PrismaService } from './prisma.service';

@Controller('projects/:projectId/raid')
export class RaidController {
  constructor(private prisma: PrismaService) {}

  @Get()
  async list(@Param('projectId') projectId: string, @Query() query: {
    category?: string;
    status?: string;
    owner?: string;
    severity?: string;
    q?: string;
  }) {
    const { category = 'risks', status, owner, severity, q } = query;
    const where: any = { projectId };

    if (status) where.status = status;
    if (owner) where.owner = owner;
    if (severity && category === 'issues') where.severity = severity;
    if (q) {
      where.OR = [
        { title: { contains: q, mode: 'insensitive' } },
        { description: { contains: q, mode: 'insensitive' } },
        { trigger: { contains: q, mode: 'insensitive' } },
      ];
    }

    switch (category) {
      case 'risks':
        return this.prisma.risk.findMany({ where, orderBy: [{ score: 'desc' }, { createdAt: 'desc' }] });
      case 'issues':
        return this.prisma.issue.findMany({ where, orderBy: [{ severity: 'desc' }] });
      case 'actions':
        return this.prisma.action.findMany({ where, orderBy: [{ dueAt: 'asc' }] });
      case 'decisions':
        return this.prisma.decision.findMany({ where, orderBy: [{ decidedOn: 'desc' }] });
      default:
        return [];
    }
  }
}
