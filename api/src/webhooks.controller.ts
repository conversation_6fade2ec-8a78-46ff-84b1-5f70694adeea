import { Body, Controller, Headers, HttpCode, Post, Req } from '@nestjs/common';
import { PrismaService } from './prisma.service';
import { Webhook } from 'svix';

@Controller('webhooks')
export class WebhooksController {
  constructor(private prisma: PrismaService) {}

  @Post('clerk')
  @HttpCode(200)
  async handleClerk(@Req() req: any, @Headers('svix-id') svixId: string, @Headers('svix-timestamp') svixTimestamp: string, @Headers('svix-signature') svixSignature: string) {
    const secret = process.env.CLERK_WEBHOOK_SECRET;
    const payload = req.body instanceof Buffer ? req.body.toString('utf8') : JSON.stringify(req.body || {});
    if (!secret) {
      // If not configured, accept and no-op to keep local dev easy
      return { ok: true, note: 'Missing CLERK_WEBHOOK_SECRET; skipped verification.' };
    }
    const wh = new Webhook(secret);
    const evt = wh.verify(payload, {
      'svix-id': svixId,
      'svix-timestamp': svixTimestamp,
      'svix-signature': svixSignature,
    }) as any;

    // We care about user.created / user.updated
    if (evt?.type?.startsWith('user.')) {
      const d = evt.data || {};
      const id = String(d.id);
      // Extract primary email if present
      let email: string | null = null;
      if (Array.isArray(d.email_addresses) && d.primary_email_address_id) {
        const found = d.email_addresses.find((e: any) => e.id === d.primary_email_address_id);
        email = found?.email_address || null;
      } else if (Array.isArray(d.email_addresses) && d.email_addresses.length) {
        email = d.email_addresses[0]?.email_address || null;
      }
      const name = [d.first_name, d.last_name].filter(Boolean).join(' ') || d.username || null;

      await this.prisma.user.upsert({
        where: { id },
        update: { email: email ?? undefined, name: name ?? undefined },
        create: { id, email: email ?? undefined, name: name ?? undefined, role: 'Viewer' },
      });
    }
    return { ok: true };
  }
}
