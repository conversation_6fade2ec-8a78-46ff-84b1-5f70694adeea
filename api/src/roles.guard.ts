import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { PrismaService } from './prisma.service';
import { AuthService } from './auth.service';
import { ROLES_KEY } from './roles.decorator';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector, private prisma: PrismaService, private auth: AuthService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const required = this.reflector.getAllAndOverride<string[]>(ROLES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);
    if (!required || required.length === 0) return true;

    // Skip authentication in development mode
    console.log('RolesGuard - NODE_ENV:', process.env.NODE_ENV);
    console.log('RolesGuard - DISABLE_AUTH:', process.env.DISABLE_AUTH);
    
    if (process.env.NODE_ENV === 'development' || process.env.DISABLE_AUTH === 'true') {
      console.log('RolesGuard - Skipping authentication for development');
      const req = context.switchToHttp().getRequest();
      (req as any).user = { sub: 'dev-user', role: 'Admin' };
      return true;
    }

    const req = context.switchToHttp().getRequest();
    const authz = req.headers['authorization'] as string | undefined;
    const user = await this.auth.verifyBearer(authz);

    // Lookup role in DB (default Viewer)
    const role = user?.sub ? (await this.prisma.user.findUnique({ where: { id: user.sub } }))?.role ?? 'Viewer' : 'Viewer';
    (req as any).user = { sub: user?.sub, role };

    return required.includes(role);
  }
}
