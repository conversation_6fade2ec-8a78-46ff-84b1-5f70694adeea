// Prisma schema for RAID
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Project {
  id           String    @id @default(cuid())
  name         String
  cadence      String    // 'weekly' | 'biweekly'
  categories   String[]
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  risks        Risk[]
  actions      Action[]
  issues       Issue[]
  decisions    Decision[]
}

model Risk {
  id          String    @id @default(cuid())
  projectId   String
  title       String
  trigger     String?
  probability Float
  impact      Int
  score       Float
  response    String?
  owner       String?
  status      String    // Open|In progress|Resolved
  dueAt       DateTime?
  links       String[]
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  project     Project   @relation(fields: [projectId], references: [id])
  actions     Action[]
  issues      Issue[]
}

model Action {
  id          String    @id @default(cuid())
  projectId   String
  title       String
  owner       String?
  dueAt       DateTime?
  completedAt DateTime?
  relatedId   String?
  relatedType String?   // 'risk' | 'issue' | 'decision'
  status      String    // Open|In progress|Done
  project     Project   @relation(fields: [projectId], references: [id])
  risk        Risk?     @relation(fields: [relatedId], references: [id], onDelete: SetNull)
}

model Issue {
  id           String   @id @default(cuid())
  projectId    String
  title        String
  description  String?
  owner        String?
  severity     String?  // Low|Med|High|Critical
  status       String
  resolution   String?
  relatedRiskId String?
  project      Project  @relation(fields: [projectId], references: [id])
  risk         Risk?    @relation(fields: [relatedRiskId], references: [id])
}

model Decision {
  id          String   @id @default(cuid())
  projectId   String
  title       String
  description String?
  decidedOn   DateTime?
  decidedBy   String?
  rationale   String?
  project     Project  @relation(fields: [projectId], references: [id])
}

model AuditEntry {
  id         String   @id @default(cuid())
  itemType   String   // project|risk|action|issue|decision
  itemId     String
  changeSet  Json
  changedBy  String
  changedAt  DateTime @default(now())
}


model User {
  id        String   @id              // auth subject (sub)
  email     String?
  name      String?
  role      String   @default("Viewer") // Viewer|Contributor|PM|Admin
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
