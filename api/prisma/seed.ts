import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  // Dev admin user for RBAC demos
  await prisma.user.upsert({
    where: { id: 'seed-admin' },
    update: { role: 'Admin', email: '<EMAIL>', name: 'Seed Admin' },
    create: { id: 'seed-admin', role: 'Admin', email: '<EMAIL>', name: 'Seed Admin' },
  });

  const project = await prisma.project.upsert({
    where: { id: 'seed-project' },
    update: {},
    create: {
      id: 'seed-project',
      name: 'Mobility Hubs Pilot',
      cadence: 'weekly',
      categories: ['assumptions', 'dependencies'],
    },
  });

  const risks = await prisma.risk.createMany({
    data: [
      {
        projectId: project.id,
        title: 'Data feed outages could delay analytics',
        trigger: 'Upstream API rate limits',
        probability: 0.4,
        impact: 4,
        score: 0.4 * 4,
        response: 'Cache results; implement backoff; add monitoring',
        owner: '<PERSON>riya',
        status: 'Open',
        dueAt: new Date(Date.now() + 1000 * 60 * 60 * 24 * 14),
        links: [],
      },
      {
        projectId: project.id,
        title: 'Stakeholder availability for weekly reviews',
        trigger: 'Conflicting schedules',
        probability: 0.3,
        impact: 3,
        score: 0.3 * 3,
        response: 'Send 3 options; async updates allowed',
        owner: 'Evan',
        status: 'In progress',
        dueAt: new Date(Date.now() + 1000 * 60 * 60 * 24 * 7),
        links: [],
      },
    ],
  });

  const issue = await prisma.issue.create({
    data: {
      projectId: project.id,
      title: 'Map tiles failing on zoom',
      description: 'Tiles 12–14 blank in Safari',
      owner: 'Liu',
      severity: 'High',
      status: 'Open',
    },
  });

  const decision = await prisma.decision.create({
    data: {
      projectId: project.id,
      title: 'Choose Postgres over SQLite for prod',
      description: 'Need JSONB and extensions',
      decidedOn: new Date(),
      decidedBy: 'PMO',
      rationale: 'Scalability + ecosystem',
    },
  });

  await prisma.action.createMany({
    data: [
      {
        projectId: project.id,
        title: 'Implement exponential backoff',
        owner: 'Noah',
        dueAt: new Date(Date.now() + 1000 * 60 * 60 * 24 * 5),
        status: 'Open',
        relatedType: 'risk',
      },
      {
        projectId: project.id,
        title: 'Safari tile bug repro',
        owner: 'Liu',
        dueAt: new Date(Date.now() + 1000 * 60 * 60 * 24 * 3),
        status: 'In progress',
        relatedType: 'issue',
      },
    ],
  });

  console.log('Seed complete.');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
