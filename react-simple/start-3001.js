const { spawn } = require('child_process');

// Set the PORT environment variable
process.env.PORT = '3001';

// Start the React app
const child = spawn('npx', ['react-scripts', 'start'], {
  stdio: 'inherit',
  env: { ...process.env, PORT: '3001' }
});

child.on('error', (err) => {
  console.error('Failed to start app:', err);
});

child.on('close', (code) => {
  console.log(`App exited with code ${code}`);
});
