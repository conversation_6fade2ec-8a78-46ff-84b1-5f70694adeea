# Root
.DS_Store
.env
.env.*
*.local
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Node
node_modules/

# Apps
react-simple/node_modules/
react-simple/.env*
react-simple/build/

react-web/node_modules/
react-web/.env*
react-web/dist/
react-web/.vite/

web/node_modules/
web/.next/
web/out/
web/.env*
web/.vercel/

api/node_modules/
api/dist/
api/.env*
api/prisma/dev.db*
api/prisma/*.db*

# TypeScript
*.tsbuildinfo

# IDE/editor
.vscode/
.idea/
*.swp

# Coverage
coverage/

# Misc
.tmp/
.temp/

# Environment files (contain secrets)
.env.production
.env.staging
