# RAID Application Testing Guide

This document provides comprehensive information about the testing infrastructure and practices for the RAID application.

## Overview

The RAID application uses a multi-layered testing approach:

- **Backend Testing**: Python with pytest for unit, integration, and API tests
- **Frontend Testing**: Jest with React Testing Library for component tests
- **End-to-End Testing**: Playwright for full user workflow testing
- **API Testing**: Integration tests for all API endpoints
- **Authentication Testing**: Comprehensive auth flow testing
- **Audit Testing**: Specialized tests for audit trail functionality

## Backend Testing (Python/FastAPI)

### Test Structure

```
api_py/tests/
├── conftest.py              # Test configuration and fixtures
├── unit/                    # Unit tests
│   ├── test_models.py       # Database model tests
│   └── test_services.py     # Service layer tests
├── integration/             # Integration tests
│   ├── test_auth_api.py     # Authentication API tests
│   ├── test_projects_api.py # Projects API tests
│   ├── test_audit_api.py    # Audit API tests
│   └── test_audit_workflow.py # End-to-end audit tests
└── utils/                   # Test utilities
```

### Running Backend Tests

```bash
cd api_py

# Run all tests
python run_tests.py all

# Run specific test categories
python run_tests.py unit
python run_tests.py integration
python run_tests.py auth
python run_tests.py audit
python run_tests.py api

# Run with coverage
python run_tests.py all --coverage

# Run tests in parallel
python run_tests.py all --parallel

# Run CI pipeline
python run_tests.py ci
```

### Test Markers

Tests are organized using pytest markers:

- `@pytest.mark.unit` - Unit tests
- `@pytest.mark.integration` - Integration tests
- `@pytest.mark.auth` - Authentication tests
- `@pytest.mark.audit` - Audit functionality tests
- `@pytest.mark.api` - API endpoint tests
- `@pytest.mark.database` - Database tests
- `@pytest.mark.slow` - Slow-running tests

### Test Configuration

Key configuration in `pytest.ini`:

```ini
[tool:pytest]
minversion = 6.0
addopts = -ra -q --strict-markers --strict-config
testpaths = tests
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow tests
    auth: Authentication tests
    database: Database tests
    api: API endpoint tests
    audit: Audit functionality tests
```

### Test Database

Tests use an in-memory SQLite database for isolation:

```python
TEST_DATABASE_URL = "sqlite:///:memory:"
test_engine = create_engine(
    TEST_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
```

### Key Test Fixtures

- `db_session`: Database session for tests
- `client`: FastAPI test client
- `test_user`: Standard test user
- `admin_user`: Admin test user
- `auth_headers`: Authentication headers
- `sample_project_data`: Sample project data

## Frontend Testing (React/Next.js)

### Test Structure

```
web/__tests__/
├── utils/
│   └── test-utils.tsx       # Test utilities and mock data
├── components/
│   ├── ProjectDashboard.test.tsx
│   └── RAIDItemCreateModal.test.tsx
└── pages/                   # Page component tests
```

### Running Frontend Tests

```bash
cd web

# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run end-to-end tests
npm run test:e2e

# Run end-to-end tests with UI
npm run test:e2e:ui

# Run all tests
npm run test:all
```

### Test Configuration

Jest configuration in `jest.config.js`:

```javascript
const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  testEnvironment: 'jsdom',
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/$1',
    // ... other mappings
  },
  collectCoverageFrom: [
    'components/**/*.{js,jsx,ts,tsx}',
    'lib/**/*.{js,jsx,ts,tsx}',
    'hooks/**/*.{js,jsx,ts,tsx}',
    'app/**/*.{js,jsx,ts,tsx}',
  ],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70,
    },
  },
}
```

### Test Utilities

Custom render function with providers:

```typescript
export function renderWithProviders(
  ui: ReactElement,
  options: CustomRenderOptions = {}
) {
  const { queryClient = createTestQueryClient(), ...renderOptions } = options

  function Wrapper({ children }: { children: React.ReactNode }) {
    return (
      <QueryClientProvider client={queryClient}>
        {children}
      </QueryClientProvider>
    )
  }

  return {
    ...render(ui, { wrapper: Wrapper, ...renderOptions }),
    queryClient,
  }
}
```

### Mock Data

Comprehensive mock data for testing:

- `mockProject`: Sample project data
- `mockRisk`: Sample risk data
- `mockAction`: Sample action data
- `mockIssue`: Sample issue data
- `mockDecision`: Sample decision data
- `mockUser`: Sample user data
- `mockAuditEntry`: Sample audit entry

## End-to-End Testing (Playwright)

### Test Structure

```
web/e2e/
├── global-setup.ts          # Global test setup
├── global-teardown.ts       # Global test cleanup
├── auth.spec.ts             # Authentication flow tests
└── project-management.spec.ts # Project management tests
```

### Running E2E Tests

```bash
cd web

# Run all E2E tests
npx playwright test

# Run specific test file
npx playwright test auth.spec.ts

# Run tests with UI mode
npx playwright test --ui

# Run tests in headed mode
npx playwright test --headed

# Run tests on specific browser
npx playwright test --project=chromium
```

### Test Configuration

Playwright configuration in `playwright.config.ts`:

```typescript
export default defineConfig({
  testDir: './e2e',
  fullyParallel: true,
  retries: process.env.CI ? 2 : 0,
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
  },
  projects: [
    { name: 'chromium', use: { ...devices['Desktop Chrome'] } },
    { name: 'firefox', use: { ...devices['Desktop Firefox'] } },
    { name: 'webkit', use: { ...devices['Desktop Safari'] } },
    { name: 'Mobile Chrome', use: { ...devices['Pixel 5'] } },
    { name: 'Mobile Safari', use: { ...devices['iPhone 12'] } },
  ],
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
  },
})
```

## Test Coverage

### Coverage Targets

- **Backend**: 85% overall coverage
  - Unit tests: 90% coverage
  - Integration tests: 80% coverage
- **Frontend**: 70% overall coverage
  - Components: 75% coverage
  - Utilities: 80% coverage

### Generating Coverage Reports

```bash
# Backend coverage
cd api_py
python run_tests.py coverage

# Frontend coverage
cd web
npm run test:coverage
```

Coverage reports are generated in:
- Backend: `api_py/htmlcov/index.html`
- Frontend: `web/coverage/lcov-report/index.html`

## Continuous Integration

### GitHub Actions Workflow

```yaml
name: Test Suite
on: [push, pull_request]

jobs:
  backend-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      - name: Install dependencies
        run: |
          cd api_py
          pip install -r requirements.txt
      - name: Run tests
        run: |
          cd api_py
          python run_tests.py ci

  frontend-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Install dependencies
        run: |
          cd web
          npm ci
      - name: Run tests
        run: |
          cd web
          npm run test:coverage
      - name: Run E2E tests
        run: |
          cd web
          npx playwright install
          npm run test:e2e
```

## Best Practices

### Writing Tests

1. **Test Structure**: Follow AAA pattern (Arrange, Act, Assert)
2. **Test Isolation**: Each test should be independent
3. **Descriptive Names**: Use clear, descriptive test names
4. **Mock External Dependencies**: Mock API calls, databases, etc.
5. **Test Edge Cases**: Include error conditions and edge cases

### Test Data

1. **Use Factories**: Create test data using factories or fixtures
2. **Realistic Data**: Use realistic test data
3. **Clean Up**: Ensure tests clean up after themselves
4. **Isolation**: Don't rely on data from other tests

### Performance

1. **Parallel Execution**: Run tests in parallel when possible
2. **Fast Feedback**: Prioritize fast-running tests
3. **Selective Testing**: Use test markers to run specific test suites
4. **Caching**: Cache dependencies and test data when appropriate

## Debugging Tests

### Backend Debugging

```bash
# Run specific test with verbose output
python -m pytest tests/unit/test_models.py::TestUserModel::test_user_creation -v

# Run with debugger
python -m pytest tests/unit/test_models.py --pdb

# Run with coverage and HTML report
python -m pytest tests/ --cov=app --cov-report=html
```

### Frontend Debugging

```bash
# Run specific test
npm test -- ProjectDashboard.test.tsx

# Run with watch mode
npm test -- --watch

# Debug in VS Code
# Set breakpoints and use "Debug Jest Tests" configuration
```

### E2E Debugging

```bash
# Run with headed browser
npx playwright test --headed

# Run with debug mode
npx playwright test --debug

# Generate trace
npx playwright test --trace on
```

## Troubleshooting

### Common Issues

1. **Database Connection**: Ensure test database is properly configured
2. **Authentication**: Check that auth tokens are properly mocked
3. **Async Operations**: Use proper async/await patterns
4. **Environment Variables**: Ensure test environment variables are set
5. **Port Conflicts**: Check for port conflicts in test setup

### Getting Help

1. Check test logs for detailed error messages
2. Review test configuration files
3. Ensure all dependencies are installed
4. Verify environment setup
5. Check for known issues in the project documentation
