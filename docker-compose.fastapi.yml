version: "3.9"
services:
  db:
    image: postgres:15-alpine
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: raid
    ports:
      - "5432:5432"
    volumes:
      - pgdata_fastapi:/var/lib/postgresql/data

  fastapi:
    build:
      context: ./api_py
      dockerfile: Dockerfile
    environment:
      DATABASE_URL: **************************************/raid
    ports:
      - "5000:5000"
    depends_on:
      - db

volumes:
  pgdata_fastapi:


