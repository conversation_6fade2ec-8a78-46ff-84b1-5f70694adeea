# Environment Variables Setup Guide

This guide will help you set up environment variables for different deployment environments (development, staging, production).

## 🚀 Quick Start

```bash
# 1. Set up environment files
npm run env:setup

# 2. Edit the created files with your credentials
# - web/.env.production
# - web/.env.staging

# 3. Validate your configuration
npm run env:validate

# 4. Test build
npm run build:production
```

## 📁 Environment Files Structure

```
web/
├── .env.local                    # Development (already configured)
├── .env.production.example       # Production template
├── .env.staging.example          # Staging template
├── .env.production               # Production (create this, don't commit)
└── .env.staging                  # Staging (create this, don't commit)
```

## 🚀 Quick Setup

### 1. Copy Template Files

```bash
# For production
cp web/.env.production.example web/.env.production

# For staging
cp web/.env.staging.example web/.env.staging
```

### 2. Fill in Your Credentials

Edit the copied files with your actual service credentials (see sections below).

## 🔑 Service Setup Instructions

### Sentry (Error Tracking)

1. **Create Account**: Go to [sentry.io](https://sentry.io)
2. **Create Organization**: Set up your organization
3. **Create Projects**: 
   - Production project: `raid-app-prod`
   - Staging project: `raid-app-staging`
4. **Get Credentials**:
   - DSN: Project Settings → Client Keys (DSN)
   - Auth Token: User Settings → Auth Tokens
   - Org/Project names: From your project URLs

**Environment Variables:**
```bash
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/123456
SENTRY_ORG=your-org-slug
SENTRY_PROJECT=raid-app-prod
SENTRY_AUTH_TOKEN=your-auth-token
```

### LogRocket (Session Replay)

1. **Create Account**: Go to [logrocket.com](https://logrocket.com)
2. **Create Applications**:
   - Production app: `RAID App Production`
   - Staging app: `RAID App Staging`
3. **Get App ID**: Settings → Application Settings

**Environment Variables:**
```bash
NEXT_PUBLIC_LOGROCKET_APP_ID=abc123/raid-app-prod
```

### PostHog (Analytics)

1. **Create Account**: Go to [posthog.com](https://posthog.com)
2. **Create Projects**:
   - Production project
   - Staging project
3. **Get API Key**: Project Settings → Project API Key

**Environment Variables:**
```bash
NEXT_PUBLIC_POSTHOG_KEY=phc_abc123def456ghi789
NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com
```

### Vercel Analytics (if using Vercel)

1. **Enable Analytics**: Vercel Dashboard → Project → Analytics
2. **Get Analytics ID**: From the analytics configuration

**Environment Variables:**
```bash
NEXT_PUBLIC_VERCEL_ANALYTICS_ID=your-analytics-id
```

### Clerk Authentication

1. **Production Instance**: Create a new Clerk application for production
2. **Get Keys**: Dashboard → API Keys
3. **Configure Domains**: Add your production domain

**Environment Variables:**
```bash
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_abc123
CLERK_SECRET_KEY=sk_live_def456
```

## 🌍 Deployment Platform Setup

### Vercel

1. **Environment Variables**: Project Settings → Environment Variables
2. **Add Variables**: Copy from your `.env.production` file
3. **Environment Scope**: Set to "Production" or "Preview" as needed

### Netlify

1. **Environment Variables**: Site Settings → Environment Variables
2. **Add Variables**: Copy from your `.env.production` file

### Railway/Render/Other Platforms

1. **Environment Variables**: Platform-specific environment variable settings
2. **Add Variables**: Copy from your `.env.production` file

## 🔒 Security Best Practices

### ✅ Do's
- Use separate credentials for each environment
- Use least-privilege access tokens
- Regularly rotate auth tokens
- Monitor usage in service dashboards
- Use environment-specific project names

### ❌ Don'ts
- Never commit `.env.production` or `.env.staging` files
- Don't use production credentials in staging
- Don't share credentials in chat/email
- Don't use overly permissive tokens

## 🧪 Testing Your Setup

### 1. Local Testing
```bash
# Test with staging environment
NODE_ENV=production NEXT_PUBLIC_APP_ENV=staging npm run build
npm run start
```

### 2. Verify Services
- Check Sentry dashboard for events
- Verify LogRocket sessions
- Confirm PostHog events
- Test error reporting

### 3. Monitor Deployment
- Watch service dashboards during deployment
- Check for any configuration errors
- Verify all monitoring data is flowing

## 📊 Environment Differences

| Feature | Development | Staging | Production |
|---------|-------------|---------|------------|
| Error Sampling | 100% | 50% | 10% |
| Session Replay | All sessions | 50% | 25% |
| Console Logs | Enabled | Enabled | Disabled |
| Performance Thresholds | Relaxed | Medium | Strict |
| Log Level | Debug | Warn | Error |

## 🆘 Troubleshooting

### Common Issues

1. **Sentry not receiving errors**
   - Check DSN format
   - Verify project exists
   - Check sampling rates

2. **LogRocket not recording**
   - Verify App ID format
   - Check domain allowlist
   - Confirm user identification

3. **PostHog events missing**
   - Check API key
   - Verify host URL
   - Check feature flags

4. **Build failures**
   - Check all required variables are set
   - Verify no syntax errors in env files
   - Check for missing quotes around values

### Getting Help

- Check service documentation
- Review service status pages
- Contact support with specific error messages
- Check browser console for client-side errors
