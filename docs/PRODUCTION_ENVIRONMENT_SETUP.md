# Production Environment Setup - Complete Guide

## 🎯 Overview

This guide provides step-by-step instructions for setting up production environment variables for the RAID application with comprehensive monitoring services.

## ✅ What's Been Configured

### Monitoring Services Integrated
- **Sentry** - Error tracking and performance monitoring
- **LogRocket** - Session replay and user behavior tracking  
- **PostHog** - Product analytics and feature flags
- **Vercel Analytics** - Web analytics (if using Vercel)
- **Custom Monitoring** - API monitoring, rate limiting, health checks

### Environment Files Created
- `web/.env.production.example` - Production template
- `web/.env.staging.example` - Staging template
- `web/.env.production` - Production config (needs your credentials)
- `web/.env.staging` - Staging config (needs your credentials)

### Scripts Available
- `npm run env:setup` - Set up environment files
- `npm run env:validate` - Validate environment configuration
- `npm run build:production` - Build for production
- `npm run build:staging` - Build for staging

## 🚀 Quick Setup Process

### 1. Environment Files Setup
```bash
# Files are already created, just need your credentials
# Edit these files with your actual service credentials:
# - web/.env.production
# - web/.env.staging
```

### 2. Get Service Credentials

#### Sentry Setup
1. Go to [sentry.io](https://sentry.io) and create account
2. Create organization and projects:
   - Production: `raid-app-prod`
   - Staging: `raid-app-staging`
3. Get credentials:
   - **DSN**: Project Settings → Client Keys (DSN)
   - **Auth Token**: User Settings → Auth Tokens
   - **Org/Project**: From project URLs

#### LogRocket Setup
1. Go to [logrocket.com](https://logrocket.com) and create account
2. Create applications:
   - Production: `RAID App Production`
   - Staging: `RAID App Staging`
3. Get **App ID**: Settings → Application Settings

#### PostHog Setup
1. Go to [posthog.com](https://posthog.com) and create account
2. Create projects for production and staging
3. Get **API Key**: Project Settings → Project API Key

#### Clerk Authentication
1. Create production Clerk application
2. Get **API Keys**: Dashboard → API Keys
3. Configure production domain

### 3. Update Environment Files

Edit `web/.env.production`:
```bash
# Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_YOUR_ACTUAL_KEY
CLERK_SECRET_KEY=sk_live_YOUR_ACTUAL_SECRET

# API
NEXT_PUBLIC_API_URL=https://api.yourdomain.com

# Sentry
NEXT_PUBLIC_SENTRY_DSN=https://YOUR_ACTUAL_DSN@YOUR_ORG.ingest.sentry.io/PROJECT_ID
SENTRY_ORG=your-actual-org
SENTRY_PROJECT=raid-app-prod
SENTRY_AUTH_TOKEN=your-actual-token

# LogRocket
NEXT_PUBLIC_LOGROCKET_APP_ID=your-actual-app-id/raid-app-prod

# PostHog
NEXT_PUBLIC_POSTHOG_KEY=phc_YOUR_ACTUAL_API_KEY
NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com

# Vercel (if using Vercel)
NEXT_PUBLIC_VERCEL_ANALYTICS_ID=your-actual-analytics-id
```

### 4. Validate Configuration
```bash
npm run env:validate
```

### 5. Test Build
```bash
npm run build:production
```

## 🌐 Deployment Options

### Vercel (Recommended)
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
cd web
vercel --prod

# Set environment variables in Vercel dashboard
# Or use CLI: vercel env add VARIABLE_NAME production
```

### Netlify
```bash
# Build settings in netlify.toml
[build]
  command = "npm run build"
  publish = "web/.next"

# Set environment variables in Netlify dashboard
```

### Docker
```bash
# Use provided Dockerfile
docker build -t raid-app .
docker run -p 3000:3000 --env-file .env.production raid-app
```

## 🔍 Verification Checklist

### After Deployment
- [ ] Application loads successfully
- [ ] Health check endpoint responds: `/api/monitoring/health`
- [ ] Sentry receives test errors
- [ ] LogRocket records sessions
- [ ] PostHog tracks events
- [ ] Authentication works
- [ ] API requests are monitored

### Test Commands
```bash
# Health check
curl https://yourdomain.com/api/monitoring/health

# Should return:
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "version": "1.0.0",
  "environment": "production"
}
```

## 📊 Monitoring Dashboard Access

After deployment, access your monitoring dashboards:

- **Sentry**: https://sentry.io/organizations/YOUR_ORG/projects/raid-app-prod/
- **LogRocket**: https://app.logrocket.com/YOUR_APP_ID/
- **PostHog**: https://app.posthog.com/project/YOUR_PROJECT_ID
- **Vercel Analytics**: https://vercel.com/dashboard/analytics

## 🚨 Important Security Notes

### ✅ Do's
- Use separate credentials for each environment
- Regularly rotate auth tokens
- Monitor service usage and costs
- Set up alerts for error rates
- Use least-privilege access tokens

### ❌ Don'ts
- Never commit `.env.production` or `.env.staging` files
- Don't use production credentials in staging
- Don't share credentials in chat/email
- Don't use overly permissive tokens

## 🆘 Troubleshooting

### Common Issues

1. **Build Failures**
   ```bash
   # Check environment variables
   npm run env:validate
   
   # Check for missing variables
   npm run build:production 2>&1 | grep -i env
   ```

2. **Monitoring Not Working**
   - Verify service credentials are correct
   - Check browser console for errors
   - Review service dashboards for configuration issues
   - Ensure domains are allowlisted in services

3. **Performance Issues**
   - Check bundle size: `npm run analyze`
   - Review Core Web Vitals in monitoring
   - Monitor API response times
   - Check memory usage patterns

### Getting Help
- Review service documentation
- Check service status pages
- Contact service support with specific error messages
- Review application logs for detailed error information

## 📈 Next Steps

1. **Set Up Alerts**: Configure error rate and performance alerts
2. **Monitor Metrics**: Watch key performance indicators
3. **Regular Maintenance**: Update dependencies and rotate tokens
4. **Scale Monitoring**: Add custom metrics as needed
5. **Team Access**: Add team members to monitoring services

Your RAID application is now ready for production with comprehensive monitoring! 🚀
