# Deployment Guide

This guide covers deploying the RAID application with monitoring services to various platforms.

## 🚀 Pre-Deployment Checklist

### ✅ Environment Setup
- [ ] Created `.env.production` from template
- [ ] Filled in all service credentials
- [ ] Tested build locally: `npm run build`
- [ ] Verified monitoring services are configured
- [ ] Updated API URLs for production

### ✅ Service Accounts Created
- [ ] Sentry project configured
- [ ] LogRocket application set up
- [ ] PostHog project created
- [ ] Clerk production instance ready
- [ ] Database configured (if applicable)

## 🌐 Platform-Specific Deployment

### Vercel (Recommended)

#### 1. Install Vercel CLI
```bash
npm i -g vercel
```

#### 2. Deploy
```bash
# From project root
cd web
vercel --prod
```

#### 3. Set Environment Variables
```bash
# Set production environment variables
vercel env add NEXT_PUBLIC_SENTRY_DSN production
vercel env add SENTRY_ORG production
vercel env add SENTRY_PROJECT production
# ... add all other variables
```

#### 4. Configure Domains
- Add custom domain in Vercel dashboard
- Update Clerk allowed origins
- Update CORS settings if needed

### Netlify

#### 1. Build Settings
```toml
# netlify.toml
[build]
  command = "npm run build"
  publish = "web/.next"

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

#### 2. Environment Variables
- Go to Site Settings → Environment Variables
- Add all variables from `.env.production`

### Railway

#### 1. Deploy
```bash
# Install Railway CLI
npm install -g @railway/cli

# Deploy
railway login
railway link
railway up
```

#### 2. Environment Variables
- Set in Railway dashboard
- Or use: `railway variables set KEY=value`

### Docker Deployment

#### 1. Create Dockerfile
```dockerfile
# web/Dockerfile
FROM node:18-alpine AS deps
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:18-alpine AS builder
WORKDIR /app
COPY . .
COPY --from=deps /app/node_modules ./node_modules
RUN npm run build

FROM node:18-alpine AS runner
WORKDIR /app
ENV NODE_ENV production
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001
COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next ./.next
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json
USER nextjs
EXPOSE 3000
CMD ["npm", "start"]
```

#### 2. Build and Run
```bash
docker build -t raid-app .
docker run -p 3000:3000 --env-file .env.production raid-app
```

## 🔧 Environment Variable Configuration

### Required Variables by Platform

#### All Platforms
```bash
# Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_...
CLERK_SECRET_KEY=sk_live_...

# API
NEXT_PUBLIC_API_URL=https://api.yourdomain.com

# Monitoring
NEXT_PUBLIC_SENTRY_DSN=https://...
SENTRY_ORG=your-org
SENTRY_PROJECT=your-project
SENTRY_AUTH_TOKEN=your-token

NEXT_PUBLIC_LOGROCKET_APP_ID=your-app-id
NEXT_PUBLIC_POSTHOG_KEY=phc_...
NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com

# Environment
NODE_ENV=production
NEXT_PUBLIC_APP_ENV=production
NEXT_PUBLIC_APP_VERSION=1.0.0
```

#### Vercel-Specific
```bash
NEXT_PUBLIC_VERCEL_ANALYTICS_ID=your-analytics-id
```

### Setting Variables by Platform

#### Vercel
```bash
vercel env add VARIABLE_NAME production
```

#### Netlify
- Dashboard → Site Settings → Environment Variables

#### Railway
```bash
railway variables set VARIABLE_NAME=value
```

#### Render
- Dashboard → Environment → Environment Variables

## 🔍 Post-Deployment Verification

### 1. Application Health
```bash
# Check if app is running
curl https://yourdomain.com/api/monitoring/health

# Expected response:
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "version": "1.0.0",
  "environment": "production"
}
```

### 2. Monitoring Services

#### Sentry
- Visit Sentry dashboard
- Trigger a test error
- Verify error appears in dashboard

#### LogRocket
- Visit your application
- Check LogRocket dashboard for session

#### PostHog
- Perform actions in your app
- Check PostHog dashboard for events

### 3. Performance Checks
```bash
# Test API response times
curl -w "@curl-format.txt" -o /dev/null -s https://yourdomain.com/api/projects

# Create curl-format.txt:
echo "     time_namelookup:  %{time_namelookup}\n
      time_connect:  %{time_connect}\n
   time_appconnect:  %{time_appconnect}\n
  time_pretransfer:  %{time_pretransfer}\n
     time_redirect:  %{time_redirect}\n
time_starttransfer:  %{time_starttransfer}\n
                   ----------\n
        time_total:  %{time_total}\n" > curl-format.txt
```

## 🚨 Troubleshooting

### Common Issues

#### Build Failures
```bash
# Check for missing environment variables
npm run build 2>&1 | grep -i "env\|environment"

# Verify all required variables are set
node -e "console.log(process.env.NEXT_PUBLIC_SENTRY_DSN ? 'Sentry DSN set' : 'Missing Sentry DSN')"
```

#### Monitoring Not Working
1. Check environment variables are set correctly
2. Verify service credentials are valid
3. Check browser console for errors
4. Review service dashboards for configuration issues

#### Performance Issues
1. Check bundle size: `npm run analyze`
2. Review Core Web Vitals in monitoring
3. Check API response times
4. Monitor memory usage

### Getting Help
- Check platform-specific documentation
- Review service status pages
- Check application logs
- Contact support with specific error messages

## 📊 Monitoring Your Deployment

### Key Metrics to Watch
- Error rates (Sentry)
- Page load times (Core Web Vitals)
- API response times
- User engagement (PostHog)
- Session quality (LogRocket)

### Setting Up Alerts
1. **Sentry**: Configure error rate alerts
2. **PostHog**: Set up conversion funnel monitoring
3. **Platform**: Set up uptime monitoring
4. **Custom**: Create health check monitoring

### Regular Maintenance
- Review error trends weekly
- Update dependencies monthly
- Rotate auth tokens quarterly
- Review performance metrics monthly
