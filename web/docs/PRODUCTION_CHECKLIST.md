# Production Deployment Checklist

## Pre-Deployment Setup

### 1. Environment Variables
- [ ] Set all required monitoring service credentials
- [ ] Configure production-specific thresholds
- [ ] Set `NODE_ENV=production`
- [ ] Set `NEXT_PUBLIC_APP_ENV=production`
- [ ] Configure proper log levels
- [ ] Set up feature flags for production

### 2. Monitoring Services

#### Sentry
- [ ] Create production project in Sentry
- [ ] Configure proper sampling rates (0.1 for production)
- [ ] Set up release tracking
- [ ] Configure error filtering rules
- [ ] Set up alerts for critical errors
- [ ] Test error reporting

#### LogRocket
- [ ] Create production application
- [ ] Configure session sampling (0.1 for production)
- [ ] Set up data privacy rules
- [ ] Configure PII scrubbing
- [ ] Test session recording

#### PostHog
- [ ] Create production project
- [ ] Configure event sampling
- [ ] Set up feature flags
- [ ] Configure data retention policies
- [ ] Test event tracking

### 3. Performance Configuration
- [ ] Set appropriate API timeouts
- [ ] Configure memory thresholds
- [ ] Set up performance budgets
- [ ] Configure cache headers
- [ ] Enable compression

### 4. Security
- [ ] Review and configure CSP headers
- [ ] Set up proper CORS policies
- [ ] Configure rate limiting
- [ ] Review data privacy settings
- [ ] Set up proper authentication

## Deployment Steps

### 1. Build Verification
```bash
# Test production build
npm run build
npm run start

# Run tests
npm run test
npm run test:e2e

# Check bundle size
npm run build -- --analyze
```

### 2. Environment Setup
```bash
# Production environment variables
NODE_ENV=production
NEXT_PUBLIC_APP_ENV=production
NEXT_PUBLIC_APP_VERSION=1.0.0

# Monitoring
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/project-id
SENTRY_ORG=your-org
SENTRY_PROJECT=your-project
SENTRY_AUTH_TOKEN=your-token

NEXT_PUBLIC_LOGROCKET_APP_ID=your-app-id
NEXT_PUBLIC_POSTHOG_KEY=phc_your-key
NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com

# Feature flags
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_ERROR_REPORTING=true
NEXT_PUBLIC_ENABLE_SESSION_REPLAY=true
NEXT_PUBLIC_ENABLE_PERFORMANCE_MONITORING=true

# Performance thresholds
NEXT_PUBLIC_PERFORMANCE_API_TIMEOUT=10000
NEXT_PUBLIC_PERFORMANCE_MEMORY_THRESHOLD=80
NEXT_PUBLIC_PERFORMANCE_RENDER_THRESHOLD=2000

# Logging
NEXT_PUBLIC_LOG_LEVEL=warn
NEXT_PUBLIC_ENABLE_CONSOLE_LOGS=false
NEXT_PUBLIC_ENABLE_REMOTE_LOGGING=true
```

### 3. Deployment
- [ ] Deploy to staging environment first
- [ ] Run smoke tests
- [ ] Verify monitoring services are working
- [ ] Check performance metrics
- [ ] Deploy to production
- [ ] Monitor deployment metrics

## Post-Deployment Verification

### 1. Monitoring Health Check
- [ ] Verify Sentry is receiving events
- [ ] Check LogRocket session recordings
- [ ] Confirm PostHog events are tracked
- [ ] Test error reporting
- [ ] Verify performance monitoring

### 2. Performance Verification
```bash
# Check health endpoint
curl https://your-domain.com/api/monitoring/health

# Expected response:
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "version": "1.0.0",
  "environment": "production",
  "uptime": 123.456,
  "memory": {...}
}
```

### 3. Error Testing
- [ ] Trigger a test error to verify Sentry
- [ ] Check error appears in Sentry dashboard
- [ ] Verify error context and user information
- [ ] Test error notifications

### 4. Analytics Testing
- [ ] Trigger test events
- [ ] Verify events appear in PostHog
- [ ] Test user identification
- [ ] Check event properties

## Monitoring Dashboard Setup

### 1. Sentry Dashboard
- [ ] Set up error rate alerts
- [ ] Configure performance alerts
- [ ] Set up release tracking
- [ ] Configure team notifications

### 2. LogRocket Dashboard
- [ ] Set up error alerts
- [ ] Configure performance alerts
- [ ] Set up user behavior funnels
- [ ] Configure team access

### 3. PostHog Dashboard
- [ ] Create key metric dashboards
- [ ] Set up conversion funnels
- [ ] Configure retention analysis
- [ ] Set up feature flag monitoring

## Performance Monitoring

### 1. Core Web Vitals
- [ ] Monitor Largest Contentful Paint (LCP < 2.5s)
- [ ] Monitor First Input Delay (FID < 100ms)
- [ ] Monitor Cumulative Layout Shift (CLS < 0.1)

### 2. Custom Metrics
- [ ] API response times
- [ ] Database query performance
- [ ] Memory usage
- [ ] Error rates

### 3. Alerts
- [ ] Set up performance degradation alerts
- [ ] Configure error rate thresholds
- [ ] Set up uptime monitoring
- [ ] Configure team notifications

## Maintenance

### 1. Regular Reviews
- [ ] Weekly error review
- [ ] Monthly performance review
- [ ] Quarterly monitoring service review
- [ ] Annual security audit

### 2. Updates
- [ ] Keep monitoring SDKs updated
- [ ] Review and update sampling rates
- [ ] Update performance thresholds
- [ ] Review and update alerts

### 3. Optimization
- [ ] Analyze performance bottlenecks
- [ ] Optimize bundle sizes
- [ ] Review and optimize API calls
- [ ] Update caching strategies

## Troubleshooting

### Common Issues

1. **High Error Rates**
   - Check recent deployments
   - Review error patterns in Sentry
   - Check system resources
   - Review recent code changes

2. **Performance Degradation**
   - Check Core Web Vitals
   - Review API response times
   - Check memory usage
   - Review database performance

3. **Monitoring Service Issues**
   - Verify service credentials
   - Check network connectivity
   - Review service status pages
   - Check rate limits

### Emergency Procedures

1. **Critical Error Spike**
   - Check Sentry for error patterns
   - Review recent deployments
   - Consider rollback if necessary
   - Notify team immediately

2. **Performance Issues**
   - Check monitoring dashboards
   - Review system resources
   - Scale resources if needed
   - Investigate root cause

3. **Monitoring Service Outage**
   - Check service status pages
   - Switch to backup monitoring
   - Document issues
   - Follow up with service providers

## Success Metrics

### Error Monitoring
- Error rate < 1%
- Mean time to detection < 5 minutes
- Mean time to resolution < 1 hour

### Performance
- Page load time < 3 seconds
- API response time < 500ms
- Core Web Vitals in green

### User Experience
- Session replay coverage > 10%
- User satisfaction score > 4.0
- Feature adoption tracking active
