# Monitoring Services Configuration Guide

This guide will help you configure monitoring services for the RAID application.

## Overview

The application supports multiple monitoring services:
- **Sentry**: Error tracking and performance monitoring
- **LogRocket**: Session replay and user behavior tracking
- **PostHog**: Product analytics and feature flags
- **Vercel Analytics**: Web analytics (if deployed on Vercel)

## Quick Setup

### 1. Sentry Configuration

1. **Create a Sentry account** at [sentry.io](https://sentry.io)
2. **Create a new project** for your RAID application
3. **Get your DSN** from the project settings
4. **Add to environment variables**:
   ```bash
   NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/project-id
   SENTRY_ORG=your-org-name
   SENTRY_PROJECT=your-project-name
   SENTRY_AUTH_TOKEN=your-auth-token
   ```

### 2. LogRocket Configuration

1. **Create a LogRocket account** at [logrocket.com](https://logrocket.com)
2. **Create a new application**
3. **Get your App ID** from the setup page
4. **Add to environment variables**:
   ```bash
   NEXT_PUBLIC_LOGROCKET_APP_ID=your-app-id
   ```

### 3. PostHog Configuration

1. **Create a PostHog account** at [posthog.com](https://posthog.com)
2. **Create a new project**
3. **Get your Project API Key**
4. **Add to environment variables**:
   ```bash
   NEXT_PUBLIC_POSTHOG_KEY=phc_your-project-key
   NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com
   ```

### 4. Vercel Analytics (Optional)

If deploying on Vercel:
```bash
NEXT_PUBLIC_VERCEL_ANALYTICS_ID=your-analytics-id
```

## Environment Variables

Copy these to your `.env.local` file and fill in your actual values:

```bash
# =============================================================================
# MONITORING & ANALYTICS CONFIGURATION
# =============================================================================

# Sentry Error Tracking
NEXT_PUBLIC_SENTRY_DSN=
SENTRY_ORG=
SENTRY_PROJECT=
SENTRY_AUTH_TOKEN=

# LogRocket Session Replay
NEXT_PUBLIC_LOGROCKET_APP_ID=

# PostHog Analytics
NEXT_PUBLIC_POSTHOG_KEY=
NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com

# Vercel Analytics (if using Vercel)
NEXT_PUBLIC_VERCEL_ANALYTICS_ID=

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_ERROR_REPORTING=true
NEXT_PUBLIC_ENABLE_SESSION_REPLAY=true
NEXT_PUBLIC_ENABLE_PERFORMANCE_MONITORING=true

# Performance Monitoring Thresholds
NEXT_PUBLIC_PERFORMANCE_API_TIMEOUT=10000
NEXT_PUBLIC_PERFORMANCE_MEMORY_THRESHOLD=100
NEXT_PUBLIC_PERFORMANCE_RENDER_THRESHOLD=2000

# Logging Configuration
NEXT_PUBLIC_LOG_LEVEL=debug
NEXT_PUBLIC_ENABLE_CONSOLE_LOGS=true
NEXT_PUBLIC_ENABLE_REMOTE_LOGGING=false
```

## Features

### Error Tracking
- Automatic error capture and reporting
- Error context and user information
- Performance monitoring
- Release tracking

### Session Replay
- User session recordings
- Error reproduction
- User behavior analysis
- Performance insights

### Analytics
- Custom event tracking
- User journey analysis
- Feature usage metrics
- A/B testing support

### Performance Monitoring
- Core Web Vitals tracking
- API response time monitoring
- Memory usage tracking
- Network status monitoring

## Usage Examples

### Track Custom Events
```typescript
import { useMonitoring } from '@/lib/monitoring/MonitoringProvider';

function MyComponent() {
  const { trackEvent } = useMonitoring();
  
  const handleButtonClick = () => {
    trackEvent('button_clicked', {
      buttonName: 'create_project',
      location: 'dashboard',
    });
  };
}
```

### Track Errors
```typescript
import { useErrorTracking } from '@/lib/monitoring/hooks';

function MyComponent() {
  const { trackErrorWithContext } = useErrorTracking();
  
  const handleApiCall = async () => {
    try {
      await apiCall();
    } catch (error) {
      trackErrorWithContext(error, {
        component: 'MyComponent',
        action: 'api_call',
      });
    }
  };
}
```

### Track Performance
```typescript
import { usePerformanceTracking } from '@/lib/monitoring/hooks';

function MyComponent() {
  const { trackApiCall } = usePerformanceTracking();
  
  const fetchData = () => {
    return trackApiCall('fetch_projects', async () => {
      return await api.getProjects();
    });
  };
}
```

## Development Tools

### Monitoring Dashboard
In development mode, you'll see a monitoring toggle button (📊) in the bottom-left corner. Click it to view:
- Service status
- Performance metrics
- Memory usage
- Network status

### Console Logging
All monitoring events are logged to the console in development mode for debugging.

## Production Considerations

### Performance Impact
- Sentry: Minimal impact with proper sampling rates
- LogRocket: ~50KB bundle size, minimal runtime impact
- PostHog: ~30KB bundle size, batched events

### Privacy
- Configure data scrubbing in Sentry
- Set up PII filtering in LogRocket
- Review PostHog data collection settings

### Sampling Rates
Adjust sampling rates for production:
```bash
# In production environment
NEXT_PUBLIC_SENTRY_TRACES_SAMPLE_RATE=0.1
NEXT_PUBLIC_LOGROCKET_SAMPLE_RATE=0.1
```

## Troubleshooting

### Common Issues

1. **Sentry not capturing errors**
   - Check DSN configuration
   - Verify environment variables
   - Check browser console for Sentry errors

2. **LogRocket not recording sessions**
   - Verify App ID
   - Check network requests in browser dev tools
   - Ensure session replay is enabled

3. **PostHog events not appearing**
   - Check API key configuration
   - Verify PostHog host URL
   - Check browser network tab for API calls

### Debug Mode
Enable debug mode in development:
```bash
NEXT_PUBLIC_LOG_LEVEL=debug
NEXT_PUBLIC_ENABLE_CONSOLE_LOGS=true
```

## Support

For issues with specific monitoring services:
- Sentry: [docs.sentry.io](https://docs.sentry.io)
- LogRocket: [docs.logrocket.com](https://docs.logrocket.com)
- PostHog: [posthog.com/docs](https://posthog.com/docs)
