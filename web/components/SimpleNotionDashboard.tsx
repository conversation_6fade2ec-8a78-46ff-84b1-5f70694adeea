'use client';
import * as React from 'react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/toast';
import { Filter, X } from 'lucide-react';
import ProjectCreateModal from './ProjectCreateModal';
import RAIDItemCreateModal from './RAIDItemCreateModal';
import RAIDDragDrop from './RAIDDragDrop';
import RAIDFilterSidebar, { DesktopRAIDFilterSidebar } from './RAIDFilterSidebar';
import { useRAIDFilters } from '@/hooks/useRAIDFilters';

interface Project {
  id: string;
  name: string;
  cadence: string;
  categories: string[];
  createdAt: string;
  updatedAt: string;
}

interface RAIDItem {
  id: string;
  title: string;
  description?: string;
  owner?: string;
  status: string;
  priority: string;
  dueAt?: string;
  score?: number;
  severity?: string;
  impact?: string;
  probability?: number;
  rationale?: string;
  decidedBy?: string;
  createdAt: string;
  updatedAt: string;
}

export default function SimpleNotionDashboard() {
  const [projects, setProjects] = React.useState<Project[]>([]);
  const [selectedProject, setSelectedProject] = React.useState<Project | null>(null);
  const [raidItems, setRaidItems] = React.useState<{
    risks: RAIDItem[];
    issues: RAIDItem[];
    actions: RAIDItem[];
    decisions: RAIDItem[];
  }>({
    risks: [],
    issues: [],
    actions: [],
    decisions: []
  });
  const [loading, setLoading] = React.useState(true);
  const [mounted, setMounted] = React.useState(false);
  const [showCreateProject, setShowCreateProject] = React.useState(false);
  const [showCreateRAID, setShowCreateRAID] = React.useState(false);
  const [selectedCategory, setSelectedCategory] = React.useState<string>('risks');
  const [showSidebar, setShowSidebar] = React.useState(true);
  const [showMobileSidebar, setShowMobileSidebar] = React.useState(false);
  const { addToast } = useToast();

  const API_BASE = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5050';

  // Calculate score for risk items based on likelihood and impact
  const calculateRiskScore = (item: any) => {
    if (item.category === 'risks' || item.type === 'risks') {
      const likelihood = item.likelihood || 1; // Default to 1 if not set
      const impact = item.impact || 1; // Default to 1 if not set
      return likelihood * impact;
    }
    return item.score || 0;
  };

  // Combine all RAID items for filtering with calculated scores
  const allRAIDItems = React.useMemo(() => {
    const items = [
      ...raidItems.risks.map(item => ({ 
        ...item, 
        category: 'risks', 
        type: 'risks',
        score: calculateRiskScore({ ...item, category: 'risks' })
      })),
      ...raidItems.issues.map(item => ({ ...item, category: 'issues', type: 'issues' })),
      ...raidItems.actions.map(item => ({ ...item, category: 'actions', type: 'actions' })),
      ...raidItems.decisions.map(item => ({ ...item, category: 'decisions', type: 'decisions' }))
    ];
    return items;
  }, [raidItems]);

  // Use the filtering hook
  const { filters, setFilters, filteredData, resetFilters, activeFilterCount } = useRAIDFilters(allRAIDItems);

  React.useEffect(() => {
    setMounted(true);
    loadProjects();
  }, []);

  React.useEffect(() => {
    if (selectedProject) {
      loadRAIDItems(selectedProject.id);
    }
  }, [selectedProject]);

  const loadProjects = async () => {
    try {
      console.log('Loading projects...');
      const response = await fetch(`${API_BASE}/projects/`);
      console.log('Projects response:', response.status, response.ok);
      if (response.ok) {
        const data = await response.json();
        console.log('Projects data:', data);
        setProjects(data);
        if (data.length > 0) {
          setSelectedProject(data[0]);
        }
      } else {
        console.error('Failed to load projects:', response.status, response.statusText);
      }
    } catch (error) {
      console.error('Error loading projects:', error);
      addToast({
        variant: 'error',
        title: 'Error',
        description: 'Failed to load projects',
      });
    } finally {
      setLoading(false);
    }
  };

  const loadRAIDItems = async (projectId: string) => {
    try {
      const [risks, issues, actions, decisions] = await Promise.all([
        fetch(`${API_BASE}/projects/${projectId}/raid/?category=risks`).then(r => r.json()),
        fetch(`${API_BASE}/projects/${projectId}/raid/?category=issues`).then(r => r.json()),
        fetch(`${API_BASE}/projects/${projectId}/raid/?category=actions`).then(r => r.json()),
        fetch(`${API_BASE}/projects/${projectId}/raid/?category=decisions`).then(r => r.json()),
      ]);

      setRaidItems({
        risks: risks.map((item: any) => ({ ...item, priority: getPriorityFromScore(item.score) })),
        issues: issues.map((item: any) => ({ ...item, priority: getPriorityFromSeverity(item.severity) })),
        actions: actions.map((item: any) => ({ ...item, priority: 'Medium' })),
        decisions: decisions.map((item: any) => ({ ...item, priority: 'Medium' })),
      });
    } catch (error) {
      console.error('Error loading RAID items:', error);
      addToast({
        variant: 'error',
        title: 'Error',
        description: 'Failed to load RAID items',
      });
    }
  };

  const getPriorityFromScore = (score: number) => {
    if (score >= 4) return 'Critical';
    if (score >= 3) return 'High';
    if (score >= 2) return 'Medium';
    return 'Low';
  };

  const getPriorityFromSeverity = (severity: string) => {
    const severityMap = { 'Critical': 'Critical', 'High': 'High', 'Med': 'Medium', 'Low': 'Low' };
    return severityMap[severity as keyof typeof severityMap] || 'Medium';
  };

  const handleProjectCreated = (project: Project) => {
    setProjects(prev => [...prev, project]);
    setSelectedProject(project);
    addToast({
      variant: 'success',
      title: 'Project Created!',
      description: `Project "${project.name}" has been created successfully.`,
    });
  };

  const handleRAIDItemCreated = (item: any) => {
    const newItem = { ...item, priority: 'Medium' };
    setRaidItems(prev => ({
      ...prev,
      [selectedCategory]: [...prev[selectedCategory as keyof typeof prev], newItem]
    }));
    addToast({
      variant: 'success',
      title: 'Item Created!',
      description: `${selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1)} created successfully.`,
    });
  };

  const getProjectIcon = (categories: string[]) => {
    if (categories.includes('risks')) return '⚠️';
    if (categories.includes('issues')) return '🚨';
    if (categories.includes('actions')) return '✅';
    if (categories.includes('decisions')) return '📋';
    return '📁';
  };

  const getStatusColor = (status: string) => {
    const colors = {
      'Open': 'bg-blue-100 text-blue-800',
      'In Progress': 'bg-yellow-100 text-yellow-800',
      'Resolved': 'bg-green-100 text-green-800',
      'Closed': 'bg-gray-100 text-gray-800',
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const getPriorityColor = (priority: string) => {
    const colors = {
      'Critical': 'bg-red-100 text-red-800',
      'High': 'bg-orange-100 text-orange-800',
      'Medium': 'bg-yellow-100 text-yellow-800',
      'Low': 'bg-green-100 text-green-800',
    };
    return colors[priority as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  if (!mounted) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Initializing...</div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading projects...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Page Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowMobileSidebar(!showMobileSidebar)}
              className="lg:hidden"
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters {activeFilterCount > 0 && `(${activeFilterCount})`}
            </Button>
          </div>
        </div>
      </div>

      {/* Project Selector */}
      {!selectedProject ? (
        <div className="p-6">
          {projects.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">📋</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No projects yet</h3>
              <p className="text-gray-500 mb-6">Create your first RAID project to get started</p>
              <Button onClick={() => setShowCreateProject(true)} variant="success" size="lg">
                Create Project
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {projects.map(project => (
                <div
                  key={project.id}
                  onClick={() => setSelectedProject(project)}
                  className="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow cursor-pointer group"
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="text-2xl">{getProjectIcon(project.categories)}</div>
                    <div className="text-xs text-gray-400 group-hover:text-gray-600">
                      {new Date(project.updatedAt).toLocaleDateString()}
                    </div>
                  </div>
                  
                  <h3 className="font-semibold text-gray-900 mb-2 group-hover:text-blue-600">
                    {project.name}
                  </h3>
                  
                  <div className="space-y-2">
                    <div className="flex items-center text-sm text-gray-500">
                      <span className="capitalize">{project.cadence} review</span>
                    </div>
                    
                    <div className="flex flex-wrap gap-1">
                      {project.categories.slice(0, 3).map(category => (
                        <span
                          key={category}
                          className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full"
                        >
                          {category}
                        </span>
                      ))}
                      {project.categories.length > 3 && (
                        <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                          +{project.categories.length - 3}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      ) : (
        <div className="flex">
          {/* Desktop Sidebar */}
          {showSidebar && (
            <div className="hidden lg:block">
              <DesktopRAIDFilterSidebar
                filters={filters}
                onFiltersChange={setFilters}
                data={allRAIDItems}
              />
            </div>
          )}

          {/* Main Content */}
          <div className="flex-1">
            {/* Project Header */}
            <div className="bg-white border-b border-gray-200 px-6 py-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <button
                    onClick={() => setSelectedProject(null)}
                    className="flex items-center gap-2 text-sm text-gray-600 hover:text-gray-900"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                    Back to Projects
                  </button>
                  <div>
                    <h1 className="text-2xl font-bold text-gray-900">{selectedProject.name}</h1>
                    <p className="text-sm text-gray-500 capitalize">
                      {selectedProject.cadence} review • {selectedProject.categories.length} categories • 
                      {filteredData.length} of {allRAIDItems.length} items
                      {activeFilterCount > 0 && ` (${activeFilterCount} filters applied)`}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowSidebar(!showSidebar)}
                    className="hidden lg:flex"
                  >
                    <Filter className="h-4 w-4 mr-2" />
                    {showSidebar ? 'Hide' : 'Show'} Filters
                  </Button>
                </div>
              </div>
            </div>

            {/* RAID Drag & Drop Tables */}
            <div className="p-6">
              <RAIDDragDrop 
                projectId={selectedProject.id}
                onItemUpdate={(itemId, field, value) => {
                  // Handle item updates
                  setRaidItems(prev => {
                    const newState = { ...prev };
                    for (const category in newState) {
                      newState[category as keyof typeof newState] = newState[category as keyof typeof newState].map(item =>
                        item.id === itemId ? { ...item, [field]: value } : item
                      );
                    }
                    return newState;
                  });
                }}
                filteredData={filteredData}
              />
            </div>
          </div>
        </div>
      )}

      {/* Mobile Sidebar */}
      <RAIDFilterSidebar
        isOpen={showMobileSidebar}
        onClose={() => setShowMobileSidebar(false)}
        filters={filters}
        onFiltersChange={setFilters}
        data={allRAIDItems}
      />

      {/* Modals */}
      <ProjectCreateModal
        isOpen={showCreateProject}
        onClose={() => setShowCreateProject(false)}
        onProjectCreated={handleProjectCreated}
      />

      <RAIDItemCreateModal
        isOpen={showCreateRAID}
        onClose={() => setShowCreateRAID(false)}
        onItemCreated={handleRAIDItemCreated}
        projectId={selectedProject?.id || ''}
        category={selectedCategory as any}
      />

    </div>
  );
}
