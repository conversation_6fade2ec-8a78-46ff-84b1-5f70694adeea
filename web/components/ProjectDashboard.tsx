'use client';
import * as React from 'react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/toast';
import ProjectCreateModal from './ProjectCreateModal';
import RAIDItemCreateModal from './RAIDItemCreateModal';
import RAIDMatrix from './RAIDMatrix';

interface Project {
  id: string;
  name: string;
  cadence: string;
  categories: string[];
  createdAt: string;
  updatedAt: string;
}

interface RAIDItem {
  id: string;
  title: string;
  category: 'risks' | 'assumptions' | 'issues' | 'dependencies' | 'actions' | 'decisions';
  priority: string;
  status: string;
  owner?: string;
  dueAt?: string;
  score?: number;
  severity?: string;
  impact?: string;
  probability?: number;
}

export default function ProjectDashboard() {
  const [projects, setProjects] = React.useState<Project[]>([]);
  const [selectedProject, setSelectedProject] = React.useState<Project | null>(null);
  const [raidItems, setRaidItems] = React.useState<RAIDItem[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [showCreateProject, setShowCreateProject] = React.useState(false);
  const [showCreateRAID, setShowCreateRAID] = React.useState(false);
  const [selectedCategory, setSelectedCategory] = React.useState<string>('risks');
  const { addToast } = useToast();

  React.useEffect(() => {
    loadProjects();
  }, []);

  React.useEffect(() => {
    if (selectedProject) {
      loadRAIDItems(selectedProject.id);
    }
  }, [selectedProject]);

  const loadProjects = async () => {
    try {
      const response = await fetch('/api/projects');
      if (!response.ok) {
        throw new Error('Failed to load projects');
      }
      const data = await response.json();
      setProjects(data);
      if (data.length > 0 && !selectedProject) {
        setSelectedProject(data[0]);
      }
    } catch (error) {
      console.error('Error loading projects:', error);
      addToast({
        variant: 'error',
        title: 'Error',
        description: 'Failed to load projects',
      });
    } finally {
      setLoading(false);
    }
  };

  const loadRAIDItems = async (projectId: string) => {
    try {
      const [risks, issues, actions, decisions] = await Promise.all([
        fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5050'}/projects/${projectId}/raid?category=risks`).then(r => r.json()),
        fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5050'}/projects/${projectId}/raid?category=issues`).then(r => r.json()),
        fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5050'}/projects/${projectId}/raid?category=actions`).then(r => r.json()),
        fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5050'}/projects/${projectId}/raid?category=decisions`).then(r => r.json()),
      ]);

      const allItems = [
        ...risks.map((item: any) => ({ ...item, category: 'risks' as const, priority: getPriorityFromScore(item.score) })),
        ...issues.map((item: any) => ({ ...item, category: 'issues' as const, priority: getPriorityFromSeverity(item.severity) })),
        ...actions.map((item: any) => ({ ...item, category: 'actions' as const, priority: 'Medium' })),
        ...decisions.map((item: any) => ({ ...item, category: 'decisions' as const, priority: 'Medium' })),
      ];

      setRaidItems(allItems);
    } catch (error) {
      addToast({
        variant: 'error',
        title: 'Error',
        description: 'Failed to load RAID items',
      });
    }
  };

  const getPriorityFromScore = (score: number) => {
    if (score >= 4) return 'Critical';
    if (score >= 3) return 'High';
    if (score >= 2) return 'Medium';
    return 'Low';
  };

  const getPriorityFromSeverity = (severity: string) => {
    const severityMap = { 'Critical': 'Critical', 'High': 'High', 'Med': 'Medium', 'Low': 'Low' };
    return severityMap[severity as keyof typeof severityMap] || 'Medium';
  };

  const handleProjectCreated = (project: Project) => {
    setProjects(prev => [...prev, project]);
    setSelectedProject(project);
  };

  const handleRAIDItemCreated = (item: any) => {
    setRaidItems(prev => [...prev, { ...item, category: selectedCategory as any, priority: 'Medium' }]);
  };

  const getProjectStats = () => {
    const stats = {
      total: raidItems.length,
      risks: raidItems.filter(item => item.category === 'risks').length,
      issues: raidItems.filter(item => item.category === 'issues').length,
      actions: raidItems.filter(item => item.category === 'actions').length,
      decisions: raidItems.filter(item => item.category === 'decisions').length,
      critical: raidItems.filter(item => item.priority === 'Critical').length,
      high: raidItems.filter(item => item.priority === 'High').length,
      open: raidItems.filter(item => item.status === 'Open').length,
      inProgress: raidItems.filter(item => item.status === 'In Progress').length,
    };
    return stats;
  };

  const stats = getProjectStats();

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading projects...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">RAID Project Dashboard</h1>
        <Button onClick={() => setShowCreateProject(true)}>
          + New Project
        </Button>
      </div>

      {/* Project Selector */}
      {projects.length > 0 && (
        <div className="bg-white p-4 rounded-lg border">
          <h3 className="text-lg font-semibold mb-3">Select Project</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {projects.map(project => (
              <div
                key={project.id}
                className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                  selectedProject?.id === project.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setSelectedProject(project)}
              >
                <h4 className="font-medium">{project.name}</h4>
                <p className="text-sm text-gray-600 capitalize">{project.cadence} review</p>
                <p className="text-xs text-gray-500">
                  {project.categories.length} categories
                </p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Project Stats */}
      {selectedProject && (
        <div className="bg-white p-4 rounded-lg border">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">{selectedProject.name} - RAID Overview</h3>
            <div className="flex gap-2">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-3 py-1 border border-gray-300 rounded-md text-sm"
              >
                <option value="risks">Risks</option>
                <option value="assumptions">Assumptions</option>
                <option value="issues">Issues</option>
                <option value="dependencies">Dependencies</option>
                <option value="actions">Actions</option>
                <option value="decisions">Decisions</option>
              </select>
              <Button 
                size="sm" 
                onClick={() => setShowCreateRAID(true)}
              >
                + Add {selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1)}
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 mb-4">
            <div className="text-center p-3 bg-gray-50 rounded">
              <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
              <div className="text-sm text-gray-600">Total Items</div>
            </div>
            <div className="text-center p-3 bg-red-50 rounded">
              <div className="text-2xl font-bold text-red-600">{stats.critical}</div>
              <div className="text-sm text-gray-600">Critical</div>
            </div>
            <div className="text-center p-3 bg-orange-50 rounded">
              <div className="text-2xl font-bold text-orange-600">{stats.high}</div>
              <div className="text-sm text-gray-600">High Priority</div>
            </div>
            <div className="text-center p-3 bg-blue-50 rounded">
              <div className="text-2xl font-bold text-blue-600">{stats.open}</div>
              <div className="text-sm text-gray-600">Open</div>
            </div>
            <div className="text-center p-3 bg-yellow-50 rounded">
              <div className="text-2xl font-bold text-yellow-600">{stats.inProgress}</div>
              <div className="text-sm text-gray-600">In Progress</div>
            </div>
            <div className="text-center p-3 bg-green-50 rounded">
              <div className="text-2xl font-bold text-green-600">
                {stats.actions - raidItems.filter(item => item.category === 'actions' && item.status === 'Completed').length}
              </div>
              <div className="text-sm text-gray-600">Pending Actions</div>
            </div>
          </div>

          {/* RAID Matrix */}
          <RAIDMatrix 
            items={raidItems} 
            onItemClick={(item) => {
              // Handle item click - could open edit modal
              console.log('Item clicked:', item);
            }}
          />
        </div>
      )}

      {/* Modals */}
      <ProjectCreateModal
        isOpen={showCreateProject}
        onClose={() => setShowCreateProject(false)}
        onProjectCreated={handleProjectCreated}
      />

      <RAIDItemCreateModal
        isOpen={showCreateRAID}
        onClose={() => setShowCreateRAID(false)}
        onItemCreated={handleRAIDItemCreated}
        projectId={selectedProject?.id || ''}
        category={selectedCategory as any}
      />
    </div>
  );
}
