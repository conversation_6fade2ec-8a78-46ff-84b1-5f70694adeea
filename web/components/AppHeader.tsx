'use client';
import * as React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Settings, FileText, BarChart3 } from 'lucide-react';
import { UserButton, SignInButton, SignedIn, SignedOut } from '@clerk/nextjs';
import SettingsModal from './SettingsModal';

export default function AppHeader() {
  const [showSettings, setShowSettings] = React.useState(false);

  return (
    <>
      <header className="flex items-center justify-between">
        <div className="flex items-center">
          <svg 
            width="40" 
            height="40" 
            viewBox="0 0 40 40" 
            className="h-8 w-8"
          >
            <defs>
              <linearGradient id="orangeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#ff8c00" />
                <stop offset="100%" stopColor="#ff6600" />
              </linearGradient>
            </defs>
            
            {/* Hexagon Background */}
            <path 
              d="M20 2 L35 8 L35 22 L20 38 L5 22 L5 8 Z" 
              fill="#1a1a1a" 
              stroke="url(#orangeGradient)" 
              strokeWidth="2"
            />
            
            {/* Crossed Swords */}
            <g fill="url(#orangeGradient)">
              {/* Sword 1 - Top-left to bottom-right */}
              <path d="M12 8 L28 22" stroke="url(#orangeGradient)" strokeWidth="2" strokeLinecap="round" />
              <path d="M12 8 L10 6 L8 8 L10 10 Z" fill="url(#orangeGradient)" />
              <path d="M28 22 L30 20 L32 22 L30 24 Z" fill="url(#orangeGradient)" />
              
              {/* Sword 2 - Top-right to bottom-left */}
              <path d="M28 8 L12 22" stroke="url(#orangeGradient)" strokeWidth="2" strokeLinecap="round" />
              <path d="M28 8 L30 6 L32 8 L30 10 Z" fill="url(#orangeGradient)" />
              <path d="M12 22 L10 20 L8 22 L10 24 Z" fill="url(#orangeGradient)" />
            </g>
            
            {/* Chain Circle */}
            <circle 
              cx="20" 
              cy="20" 
              r="8" 
              fill="none" 
              stroke="url(#orangeGradient)" 
              strokeWidth="2"
            />
            
            {/* Skull */}
            <g fill="url(#orangeGradient)">
              {/* Skull outline */}
              <ellipse cx="20" cy="18" rx="5" ry="6" />
              {/* Eye sockets */}
              <ellipse cx="18" cy="16" rx="1.5" ry="2" />
              <ellipse cx="22" cy="16" rx="1.5" ry="2" />
              {/* Nose cavity */}
              <path d="M20 18 L19 20 L21 20 Z" />
              {/* Jaw */}
              <path d="M15 20 Q20 24 25 20" stroke="url(#orangeGradient)" strokeWidth="1.5" fill="none" />
              {/* Teeth */}
              <rect x="18" y="20" width="1" height="2" />
              <rect x="21" y="20" width="1" height="2" />
            </g>
          </svg>
        </div>
        <div className="flex items-center gap-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => window.location.href = '/analytics'}
            className="text-gray-500 hover:text-gray-700 p-2"
            title="Analytics Dashboard"
          >
            <BarChart3 className="w-5 h-5" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowSettings(true)}
            className="text-gray-500 hover:text-gray-700 p-2"
          >
            <Settings className="w-5 h-5" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => window.open('/docs', '_blank')}
            className="text-gray-500 hover:text-gray-700 p-2"
            title="Documentation"
          >
            <FileText className="w-5 h-5" />
          </Button>
          
          <SignedOut>
            <SignInButton 
              appearance={{
                elements: {
                  formButtonPrimary: "bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white",
                  card: "shadow-lg border border-gray-200"
                }
              }}
            />
          </SignedOut>
          
          <SignedIn>
            <UserButton 
              afterSignOutUrl="/"
              appearance={{
                elements: {
                  avatarBox: "w-8 h-8",
                  userButtonPopoverCard: "shadow-lg border border-gray-200",
                  userButtonPopoverActionButton: "hover:bg-gray-50",
                  userButtonPopoverFooter: "hidden"
                }
              }}
            />
          </SignedIn>
          
          <a href="/" className="inline-flex items-center justify-center rounded-xl text-sm font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none shadow-sm hover:shadow-md active:scale-95 bg-gradient-to-r from-green-600 to-green-700 text-white hover:from-green-700 hover:to-green-800 focus:ring-green-300 shadow-green-200 h-10 px-4">
            + New Project
          </a>
        </div>
      </header>

      <SettingsModal
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
      />
    </>
  );
}
