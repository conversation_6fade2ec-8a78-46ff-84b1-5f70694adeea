'use client';
import * as React from 'react';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { useLocalStorageState } from '@/lib/persist';
import { useColumnResize, ResizableTH } from '@/components/ui/colresize';
import { useColumnOrder } from '@/lib/colorder';

const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5050';

type SortKey = { key: 'owner'|'due'; dir: 'asc'|'desc' };

export default function ActionsPanel({ projectId }: { projectId: string }) {
  const [q, setQ] = useLocalStorageState<string>(`actions:${projectId}:q`, '');
  const [status, setStatus] = useLocalStorageState<string>(`actions:${projectId}:status`, '');
  const [owner, setOwner] = useLocalStorageState<string>(`actions:${projectId}:owner`, '');
  const [items, setItems] = React.useState<any[]>([]);

  const [sortKeys, setSortKeys] = useLocalStorageState<SortKey[]>(`actions:${projectId}:sortKeys`, [{ key: 'due', dir: 'asc' }]);

  async function load() {
    const params = new URLSearchParams({ category: 'actions' });
    if (q) params.set('q', q);
    if (status) params.set('status', status);
    if (owner) params.set('owner', owner);
    const res = await fetch(`${apiUrl}/projects/${projectId}/raid?${params.toString()}`, { cache: 'no-store' });
    setItems(await res.json());
  }
  React.useEffect(() => { load(); }, []);

  const tableKey = `ActionsPanel.tsx:${projectId}`;
  const { onMouseDown, tableStyle, setWidths } = useColumnResize(tableKey, { title: 320, owner: 160, due: 160, status: 120 });
  const { order, setOrder, onDragStart, onDragOver, onDrop } = useColumnOrder(tableKey, ['title','owner','due','status']);

  React.useEffect(() => {
    function onApply(e: any) {
      const detail = e.detail || {};
      if (detail.projectId !== projectId) return;
      const d = detail.preset?.actions; if (!d) return;
      if (d.q !== undefined) setQ(d.q);
      if (d.status !== undefined) setStatus(d.status);
      if (d.owner !== undefined) setOwner(d.owner);
      if (d.sortKeys) setSortKeys(d.sortKeys);
      if (d.colOrder) setOrder(d.colOrder);
      if (d.colWidths) setWidths(d.colWidths);
      load();
    }
    window.addEventListener('apply-preset', onApply as any);
    return () => window.removeEventListener('apply-preset', onApply as any);
  }, [projectId]);

  function toggleSort(key: SortKey['key'], additive: boolean) {
    setSortKeys(prev => {
      const idx = prev.findIndex(k => k.key === key);
      const defDir = key === 'due' ? 'asc' : 'asc';
      if (!additive) {
        if (idx === -1) return [{ key, dir: defDir as any }];
        const next = prev[idx].dir === 'asc' ? 'desc' : 'none';
        if (next === 'none') return [];
        return [{ key, dir: next as any }];
      } else {
        if (idx === -1) return [...prev, { key, dir: defDir as any }];
        const next = prev[idx].dir === 'asc' ? 'desc' : 'none';
        if (next === 'none') return prev.filter(k => k.key !== key);
        const clone = [...prev]; clone[idx] = { key, dir: next as any }; return clone;
      }
    });
  }
  function arrowFor(key: SortKey['key']) {
    const found = sortKeys.find(k => k.key === key);
    if (!found) return null;
    return <span className="ml-1">{found.dir === 'asc' ? '↑' : '↓'}</span>;
  }

  const orderedItems = React.useMemo(() => {
    const arr = [...items];
    arr.sort((a, b) => {
      for (const sk of sortKeys) {
        let va: any = null; let vb: any = null;
        if (sk.key === 'owner') { va = (a.owner || '').toLowerCase(); vb = (b.owner || '').toLowerCase(); }
        if (sk.key === 'due') { va = a.dueAt ? new Date(a.dueAt).getTime() : Number.POSITIVE_INFINITY; vb = b.dueAt ? new Date(b.dueAt).getTime() : Number.POSITIVE_INFINITY; }
        if (va < vb) return sk.dir === 'asc' ? -1 : 1;
        if (va > vb) return sk.dir === 'asc' ? 1 : -1;
      }
      return 0;
    });
    return arr;
  }, [items, sortKeys]);

  const headerFor: Record<string, React.ReactNode> = {
    title: 'Title',
    owner: <button onClick={(e) => toggleSort('owner', (e as any).shiftKey)} className="hover:underline">Owner{arrowFor('owner')}</button>,
    due: <button onClick={(e) => toggleSort('due', (e as any).shiftKey)} className="hover:underline">Due{arrowFor('due')}</button>,
    status: 'Status',
  };

  return (
    <div className="space-y-3">
      <div className="sticky top-0 z-10 -mx-2 mb-2 rounded-t-xl border-b bg-white/80 p-2 backdrop-blur supports-[backdrop-filter]:bg-white/60">
        <div className="flex flex-wrap items-end gap-2">
          <div className="w-40">
            <label className="text-xs text-gray-500">Search</label>
            <Input placeholder="query…" value={q} onChange={(e) => setQ(e.target.value)} />
          </div>
          <div>
            <label className="text-xs text-gray-500">Status</label>
            <select className="rounded-xl border px-2 py-2 text-sm" value={status} onChange={(e) => setStatus(e.target.value)}>
              <option value="">All</option>
              <option>Open</option>
              <option>In progress</option>
              <option>Done</option>
            </select>
          </div>
          <div className="w-40">
            <label className="text-xs text-gray-500">Owner</label>
            <Input placeholder="name…" value={owner} onChange={(e) => setOwner(e.target.value)} />
          </div>
          <button onClick={load} className="rounded-xl border px-3 py-2 text-sm hover:bg-gray-50">Apply</button>
          <div className="grow" />
          <div className="flex items-center gap-2">
            {sortKeys.length > 0 && <span className="text-xs text-gray-500">Sort:</span>}
            {sortKeys.map((k, i) => (<Badge key={k.key}>{k.key} {k.dir === 'asc' ? '↑' : '↓'}{sortKeys.length > 1 ? ` #${i+1}` : ''}</Badge>))}
            {sortKeys.length > 0 && <button className="text-xs underline text-gray-500" onClick={() => setSortKeys([])}>Clear</button>}
          </div>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full text-sm resizable-table" style={tableStyle()}>
          <thead>
            <tr className="text-left text-gray-500">
              {order.map(key => (
                <ResizableTH key={key} colId={key} onMouseDown={onMouseDown}>
                  <div draggable onDragStart={() => onDragStart(key)} onDragOver={onDragOver} onDrop={() => onDrop(key)} className="flex items-center gap-2">
                    <span className="cursor-move text-gray-400">⋮⋮</span>
                    {headerFor[key]}
                  </div>
                </ResizableTH>
              ))}
            </tr>
          </thead>
          <tbody>
            {orderedItems.map((x) => (
              <tr key={x.id} className="border-t">
                {order.map((key) => (
                  <td key={key} className="p-2" data-col={key} style={{ width: `var(--w-${key})` }}>
                    {key === 'title' && x.title}
                    {key === 'owner' && (x.owner || '-')}
                    {key === 'due' && (x.dueAt ? new Date(x.dueAt).toLocaleDateString() : '-')}
                    {key === 'status' && x.status}
                  </td>
                ))}
              </tr>
            ))}
            {orderedItems.length === 0 && (
              <tr><td className="p-2 text-sm text-gray-500" colSpan={order.length}>No actions match your filters.</td></tr>
            )}
          </tbody>
        </table>
      </div>
      <p className="text-xs text-gray-400">Tip: Drag column headers to reorder. Shift+Click headers to add secondary sort.</p>
    </div>
  );
}
