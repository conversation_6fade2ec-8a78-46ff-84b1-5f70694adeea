'use client';
import { useAuthFetch } from '@/lib/client-auth';
import * as React from 'react';
import { Badge } from './ui/badge';
import { useToast } from '@/components/ui/toast';
import { Button } from './ui/button';

type Item = {
  id: string;
  title: string;
  status: 'Open' | 'In progress' | 'Resolved';
  score?: number;
};

type Props = {
  items: Item[];
  onMove?: (id: string, status: Item['status']) => void;
};

const columns: Item['status'][] = ['Open', 'In progress', 'Resolved'];

export function KanbanBoard({ items, onMove }: Props) {
  const { addToast } = useToast();
  const authFetch = useAuthFetch();
  const [local, setLocal] = React.useState(items);

  React.useEffect(() => setLocal(items), [items]);

  async function onDrop(e: React.DragEvent, status: Item['status']) {
    const id = e.dataTransfer.getData('text/plain');
    setLocal((prev) => prev.map((x) => (x.id === id ? { ...x, status } : x)));
    try { await authFetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5050'}/risks/${id}`, { method: 'PATCH', body: JSON.stringify({ status }) }); } catch (e) { addToast({ variant: 'error', title: 'Failed to update status' }); }
    onMove?.(id, status);
  }

  return (
    <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
      {columns.map((status) => (
        <div
          key={status}
          className="card min-h-[300px] p-3"
          onDragOver={(e) => e.preventDefault()}
          onDrop={(e) => onDrop(e, status)}
        >
          <div className="mb-2 flex items-center justify-between">
            <h3 className="font-semibold">{status}</h3>
            <Badge>{local.filter((x) => x.status === status).length}</Badge>
          </div>
          <div className="space-y-2">
            {local
              .filter((x) => x.status === status)
              .map((x) => (
                <div
                  key={x.id}
                  draggable
                  onDragStart={(e) => e.dataTransfer.setData('text/plain', x.id)}
                  className="rounded-xl border border-gray-200 p-3 hover:bg-gray-50"
                >
                  <div className="flex items-center justify-between">
                    <div className="font-medium">{x.title}</div>
                    {typeof x.score === 'number' && <Badge>Score {x.score.toFixed(1)}</Badge>}
                  </div>
                </div>
              ))}
            {local.filter((x) => x.status === status).length === 0 && (
              <div className="text-sm text-gray-500">No items</div>
            )}
          </div>
        </div>
      ))}
    </div>
  );
}
