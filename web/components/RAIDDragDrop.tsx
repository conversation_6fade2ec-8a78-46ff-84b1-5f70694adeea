'use client';
import * as React from 'react';
import {
  DndContext,
  DragEndEvent,
  DragOverEvent,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
  DragOverlay,
  closestCenter,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Button } from '@/components/ui/button';
import DraggableTable from './DraggableTable';
import RAIDItemCreateModal from './RAIDItemCreateModal';

interface RAIDItem {
  id: string;
  title: string;
  description?: string;
  owner?: string;
  status: string;
  priority: string;
  dueAt?: string;
  score?: number;
  likelihood?: number;
  impact?: number;
  severity?: string;
  probability?: number;
  rationale?: string;
  decidedBy?: string;
  createdAt: string;
  updatedAt: string;
  category?: 'risks' | 'assumptions' | 'issues' | 'dependencies' | 'actions' | 'decisions';
  type?: string;
}

interface RAIDDragDropProps {
  projectId: string;
  onItemUpdate?: (itemId: string, field: string, value: any) => void;
  filteredData?: RAIDItem[];
}

export default function RAIDDragDrop({ projectId, onItemUpdate, filteredData }: RAIDDragDropProps) {
  // Data is now provided by parent component via filteredData prop
  const [loading, setLoading] = React.useState(false);
  const [showCreateModal, setShowCreateModal] = React.useState(false);
  const [selectedCategory, setSelectedCategory] = React.useState<string>('risks');
  const [activeItem, setActiveItem] = React.useState<RAIDItem | null>(null);

  // Calculate score for risk items based on likelihood and impact
  const calculateRiskScore = (item: RAIDItem) => {
    if (item.category === 'risks' || item.type === 'risks') {
      const likelihood = item.likelihood || 0;
      const impact = item.impact || 0;
      return likelihood * impact;
    }
    return item.score || 0;
  };

  // Use filtered data from parent component
  const displayData = React.useMemo(() => {
    if (!filteredData || filteredData.length === 0) {
      return {
        risks: [],
        issues: [],
        actions: [],
        decisions: []
      };
    }

    // Group filtered data by category
    const dataToProcess = {
      risks: filteredData.filter(item => item.category === 'risks' || item.type === 'risks'),
      issues: filteredData.filter(item => item.category === 'issues' || item.type === 'issues'),
      actions: filteredData.filter(item => item.category === 'actions' || item.type === 'actions'),
      decisions: filteredData.filter(item => item.category === 'decisions' || item.type === 'decisions')
    };

    // Calculate scores for risk items - always recalculate based on current likelihood and impact
    const processedData = {
      ...dataToProcess,
      risks: dataToProcess.risks.map(item => ({
        ...item,
        score: calculateRiskScore(item)
      }))
    };

    return processedData;
  }, [filteredData, calculateRiskScore]);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  // Data is now provided by parent component via filteredData prop
  // No need to load data locally

  // Data loading is now handled by parent component

  const getPriorityFromScore = (score: number) => {
    if (score >= 4) return 'Critical';
    if (score >= 3) return 'High';
    if (score >= 2) return 'Medium';
    return 'Low';
  };

  const getPriorityFromSeverity = (severity: string) => {
    const severityMap = { 'Critical': 'Critical', 'High': 'High', 'Med': 'Medium', 'Low': 'Low' };
    return severityMap[severity as keyof typeof severityMap] || 'Medium';
  };

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    const item = findItemById(active.id as string);
    setActiveItem(item);
  };

  const handleDragOver = (event: DragOverEvent) => {
    const { active, over } = event;
    
    if (!over) return;

    const activeId = active.id as string;
    const overId = over.id as string;

    // Find the containers
    const activeContainer = findContainer(activeId);
    const overContainer = findContainer(overId);

    if (!activeContainer || !overContainer) return;

    if (activeContainer === overContainer) {
      // Reordering within the same container
      return;
    }

    // Moving between containers
    const activeItems = displayData[activeContainer as keyof typeof displayData];
    const overItems = displayData[overContainer as keyof typeof displayData];
    
    const activeIndex = activeItems.findIndex(item => item.id === activeId);
    const overIndex = overItems.findIndex(item => item.id === overId);

    if (activeIndex === -1) return;

    const newActiveItems = [...activeItems];
    const newOverItems = [...overItems];
    
    const [removed] = newActiveItems.splice(activeIndex, 1);
    
    if (overIndex === -1) {
      // Dropping on empty container
      newOverItems.push(removed);
    } else {
      // Dropping on specific item
      newOverItems.splice(overIndex, 0, removed);
    }

    // Note: This component now uses filteredData from props
    // Drag and drop reordering should be handled by parent component
    console.log('Drag and drop reorder:', { activeContainer, overContainer, activeItem });
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    
    if (!over) {
      setActiveItem(null);
      return;
    }

    const activeId = active.id as string;
    const overId = over.id as string;

    const activeContainer = findContainer(activeId);
    const overContainer = findContainer(overId);

    if (!activeContainer || !overContainer) {
      setActiveItem(null);
      return;
    }

    if (activeContainer === overContainer) {
      // Reordering within the same container
      const items = displayData[activeContainer as keyof typeof displayData];
      const oldIndex = items.findIndex(item => item.id === activeId);
      const newIndex = items.findIndex(item => item.id === overId);

      if (oldIndex !== newIndex) {
        // Reordering should be handled by parent component
        console.log('Reorder within container:', { activeContainer, oldIndex, newIndex });
      }
    } else {
      // Moving between containers - this was handled in handleDragOver
      // Here we could add API calls to persist the changes
      console.log(`Moved item from ${activeContainer} to ${overContainer}`);
    }

    setActiveItem(null);
  };

  const findItemById = (id: string): RAIDItem | null => {
    for (const category in displayData) {
      const item = displayData[category as keyof typeof displayData].find(item => item.id === id);
      if (item) return item;
    }
    return null;
  };

  const findContainer = (id: string): string | null => {
    for (const category in displayData) {
      if (displayData[category as keyof typeof displayData].some(item => item.id === id)) {
        return category;
      }
    }
    return null;
  };

  const handleItemCreated = (item: any) => {
    // Item creation should be handled by parent component
    console.log('Item created:', item, 'in category:', selectedCategory);
  };

  const handleItemUpdate = async (id: string, field: string, value: any) => {
    // Call parent callback to update the data
    if (onItemUpdate) {
      onItemUpdate(id, field, value);
    }
  };

  const handleItemDelete = async (id: string) => {
    // Item deletion should be handled by parent component
    console.log('Item delete requested:', id);
  };

  const handleReorderRows = (category: string, newOrder: any[]) => {
    // Row reordering should be handled by parent component
    console.log('Row reorder requested:', category, newOrder);
  };

  const getTableColumns = (category: string): any[] => {
    const baseColumns = [
      { key: 'title', label: 'Title', type: 'text' as const, width: '30%', sortable: true, filterable: true },
      { key: 'owner', label: 'Owner', type: 'text' as const, width: '15%', sortable: true, filterable: true },
      { key: 'status', label: 'Status', type: 'status' as const, width: '12%', sortable: true, filterable: true, options: ['Open', 'In Progress', 'Resolved', 'Closed'] },
      { key: 'priority', label: 'Priority', type: 'priority' as const, width: '12%', sortable: true, filterable: true, options: ['Critical', 'High', 'Medium', 'Low'] },
      { key: 'dueAt', label: 'Due Date', type: 'date' as const, width: '15%', sortable: true, filterable: true },
    ];

    switch (category) {
      case 'risks':
        return [
          ...baseColumns,
          { key: 'likelihood', label: 'Likelihood', type: 'number' as const, width: '10%', sortable: true, filterable: true, min: 1, max: 5 },
          { key: 'impact', label: 'Impact', type: 'number' as const, width: '10%', sortable: true, filterable: true, min: 1, max: 5 },
          { key: 'score', label: 'Score', type: 'number' as const, width: '8%', sortable: true, filterable: true, calculated: true },
        ];
      case 'issues':
        return [
          ...baseColumns,
          { key: 'severity', label: 'Severity', type: 'select' as const, width: '10%', options: ['Low', 'Med', 'High', 'Critical'], sortable: true, filterable: true },
        ];
      case 'actions':
        return baseColumns;
      case 'decisions':
        return [
          { key: 'title', label: 'Title', type: 'text' as const, width: '25%', sortable: true, filterable: true },
          { key: 'decidedBy', label: 'Decided By', type: 'text' as const, width: '15%', sortable: true, filterable: true },
          { key: 'rationale', label: 'Rationale', type: 'text' as const, width: '40%', sortable: true, filterable: true },
          { key: 'createdAt', label: 'Date', type: 'date' as const, width: '20%', sortable: true, filterable: true },
        ];
      default:
        return baseColumns;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading RAID items...</div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragOver={handleDragOver}
        onDragEnd={handleDragEnd}
      >
        {/* Risks Table */}
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <div className="px-4 py-3 border-b border-gray-200 bg-red-50">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                ⚠️ Risks
                <span className="text-sm font-normal text-gray-500">({displayData.risks.length})</span>
              </h3>
              <Button
                onClick={() => {
                  setSelectedCategory('risks');
                  setShowCreateModal(true);
                }}
                size="sm"
                variant="outline"
              >
                + Add Risk
              </Button>
            </div>
          </div>
          <SortableContext items={displayData.risks.map(item => item.id)} strategy={verticalListSortingStrategy}>
            <DraggableTable
              title=""
              columns={getTableColumns('risks')}
              data={displayData.risks}
              onEditRow={handleItemUpdate}
              onDeleteRow={handleItemDelete}
              onReorderRows={(newOrder) => handleReorderRows('risks', newOrder)}
              emptyMessage="No risks identified yet"
            />
          </SortableContext>
        </div>

        {/* Issues Table */}
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <div className="px-4 py-3 border-b border-gray-200 bg-orange-50">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                🚨 Issues
                <span className="text-sm font-normal text-gray-500">({displayData.issues.length})</span>
              </h3>
              <Button
                onClick={() => {
                  setSelectedCategory('issues');
                  setShowCreateModal(true);
                }}
                size="sm"
                variant="outline"
              >
                + Add Issue
              </Button>
            </div>
          </div>
          <SortableContext items={displayData.issues.map(item => item.id)} strategy={verticalListSortingStrategy}>
            <DraggableTable
              title=""
              columns={getTableColumns('issues')}
              data={displayData.issues}
              onEditRow={handleItemUpdate}
              onDeleteRow={handleItemDelete}
              onReorderRows={(newOrder) => handleReorderRows('issues', newOrder)}
              emptyMessage="No issues reported yet"
            />
          </SortableContext>
        </div>

        {/* Actions Table */}
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <div className="px-4 py-3 border-b border-gray-200 bg-green-50">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                ✅ Actions
                <span className="text-sm font-normal text-gray-500">({displayData.actions.length})</span>
              </h3>
              <Button
                onClick={() => {
                  setSelectedCategory('actions');
                  setShowCreateModal(true);
                }}
                size="sm"
                variant="outline"
              >
                + Add Action
              </Button>
            </div>
          </div>
          <SortableContext items={displayData.actions.map(item => item.id)} strategy={verticalListSortingStrategy}>
            <DraggableTable
              title=""
              columns={getTableColumns('actions')}
              data={displayData.actions}
              onEditRow={handleItemUpdate}
              onDeleteRow={handleItemDelete}
              onReorderRows={(newOrder) => handleReorderRows('actions', newOrder)}
              emptyMessage="No actions planned yet"
            />
          </SortableContext>
        </div>

        {/* Decisions Table */}
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <div className="px-4 py-3 border-b border-gray-200 bg-blue-50">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                📋 Decisions
                <span className="text-sm font-normal text-gray-500">({displayData.decisions.length})</span>
              </h3>
              <Button
                onClick={() => {
                  setSelectedCategory('decisions');
                  setShowCreateModal(true);
                }}
                size="sm"
                variant="outline"
              >
                + Add Decision
              </Button>
            </div>
          </div>
          <SortableContext items={displayData.decisions.map(item => item.id)} strategy={verticalListSortingStrategy}>
            <DraggableTable
              title=""
              columns={getTableColumns('decisions')}
              data={displayData.decisions}
              onEditRow={handleItemUpdate}
              onDeleteRow={handleItemDelete}
              onReorderRows={(newOrder) => handleReorderRows('decisions', newOrder)}
              emptyMessage="No decisions recorded yet"
            />
          </SortableContext>
        </div>

        <DragOverlay>
          {activeItem ? (
            <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-lg opacity-90">
              <div className="font-medium text-gray-900">{activeItem.title}</div>
              <div className="text-sm text-gray-500">{activeItem.owner || 'No owner'}</div>
            </div>
          ) : null}
        </DragOverlay>
      </DndContext>

      {/* Create Modal */}
      <RAIDItemCreateModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onItemCreated={handleItemCreated}
        projectId={projectId}
        category={selectedCategory as any}
      />
    </div>
  );
}
