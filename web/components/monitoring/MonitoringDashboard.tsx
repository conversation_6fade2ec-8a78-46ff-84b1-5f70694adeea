'use client';

import React, { useState, useEffect } from 'react';
import { useMonitoring } from '../../lib/monitoring/MonitoringProvider';
import { PerformanceMonitor, MemoryMonitor, NetworkMonitor } from '../../lib/monitoring/utils';

interface MonitoringStats {
  performance: {
    pageLoadTime?: number;
    firstContentfulPaint?: number;
    largestContentfulPaint?: number;
    firstInputDelay?: number;
    cumulativeLayoutShift?: number;
  };
  memory: {
    used: number;
    total: number;
    limit: number;
    usagePercentage: number;
  } | null;
  network: {
    isOnline: boolean;
    connectionType?: string;
    effectiveType?: string;
    downlink?: number;
  };
  errors: number;
  events: number;
}

export function MonitoringDashboard() {
  const { config } = useMonitoring();
  const [stats, setStats] = useState<MonitoringStats>({
    performance: {},
    memory: null,
    network: { isOnline: true },
    errors: 0,
    events: 0,
  });
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Only show in development or when explicitly enabled
    if (process.env.NODE_ENV === 'development' || 
        process.env.NEXT_PUBLIC_SHOW_MONITORING_DASHBOARD === 'true') {
      setIsVisible(true);
    }
  }, []);

  useEffect(() => {
    if (!isVisible) return;

    const updateStats = () => {
      const performanceMonitor = PerformanceMonitor.getInstance();
      const memoryMonitor = MemoryMonitor.getInstance();
      const networkMonitor = NetworkMonitor.getInstance();

      // Performance metrics
      const performanceStats = {
        pageLoadTime: performanceMonitor.getAverageMetric('page_load_time') || undefined,
        firstContentfulPaint: performanceMonitor.getAverageMetric('first_contentful_paint') || undefined,
        largestContentfulPaint: performanceMonitor.getAverageMetric('largest_contentful_paint') || undefined,
        firstInputDelay: performanceMonitor.getAverageMetric('first_input_delay') || undefined,
        cumulativeLayoutShift: performanceMonitor.getAverageMetric('cumulative_layout_shift') || undefined,
      };

      // Memory stats
      const memoryUsage = memoryMonitor.getCurrentMemoryUsage();
      const memoryStats = memoryUsage ? {
        ...memoryUsage,
        usagePercentage: (memoryUsage.used / memoryUsage.limit) * 100,
      } : null;

      // Network stats
      const connectionInfo = networkMonitor.getConnectionInfo();
      const networkStats = {
        isOnline: networkMonitor.getNetworkStatus(),
        connectionType: connectionInfo?.type,
        effectiveType: connectionInfo?.effectiveType,
        downlink: connectionInfo?.downlink,
      };

      setStats({
        performance: performanceStats,
        memory: memoryStats,
        network: networkStats,
        errors: 0, // This would be tracked by your error monitoring
        events: 0, // This would be tracked by your event monitoring
      });
    };

    // Update stats every 5 seconds
    const interval = setInterval(updateStats, 5000);
    updateStats(); // Initial update

    return () => clearInterval(interval);
  }, [isVisible]);

  if (!isVisible) {
    return null;
  }

  const formatMs = (ms: number | null | undefined) => {
    if (ms === null || ms === undefined) return 'N/A';
    return `${ms.toFixed(0)}ms`;
  };

  const formatMB = (mb: number) => `${mb.toFixed(1)}MB`;

  const getPerformanceColor = (value: number | null | undefined, thresholds: [number, number]) => {
    if (value === null || value === undefined) return 'text-gray-500';
    if (value < thresholds[0]) return 'text-green-600';
    if (value < thresholds[1]) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getMemoryColor = (percentage: number) => {
    if (percentage < 50) return 'text-green-600';
    if (percentage < 80) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg shadow-lg p-4 max-w-sm z-50">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-semibold text-gray-800">Monitoring</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-500 hover:text-gray-700 text-xs"
        >
          ✕
        </button>
      </div>

      {/* Configuration Status */}
      <div className="mb-3">
        <h4 className="text-xs font-medium text-gray-600 mb-1">Services</h4>
        <div className="grid grid-cols-2 gap-1 text-xs">
          <div className={`flex items-center ${config.sentryEnabled ? 'text-green-600' : 'text-gray-400'}`}>
            <span className={`w-2 h-2 rounded-full mr-1 ${config.sentryEnabled ? 'bg-green-500' : 'bg-gray-300'}`}></span>
            Sentry
          </div>
          <div className={`flex items-center ${config.logRocketEnabled ? 'text-green-600' : 'text-gray-400'}`}>
            <span className={`w-2 h-2 rounded-full mr-1 ${config.logRocketEnabled ? 'bg-green-500' : 'bg-gray-300'}`}></span>
            LogRocket
          </div>
          <div className={`flex items-center ${config.postHogEnabled ? 'text-green-600' : 'text-gray-400'}`}>
            <span className={`w-2 h-2 rounded-full mr-1 ${config.postHogEnabled ? 'bg-green-500' : 'bg-gray-300'}`}></span>
            PostHog
          </div>
          <div className={`flex items-center ${config.performanceMonitoringEnabled ? 'text-green-600' : 'text-gray-400'}`}>
            <span className={`w-2 h-2 rounded-full mr-1 ${config.performanceMonitoringEnabled ? 'bg-green-500' : 'bg-gray-300'}`}></span>
            Performance
          </div>
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="mb-3">
        <h4 className="text-xs font-medium text-gray-600 mb-1">Performance</h4>
        <div className="space-y-1 text-xs">
          <div className="flex justify-between">
            <span>Page Load:</span>
            <span className={getPerformanceColor(stats.performance.pageLoadTime, [2000, 4000])}>
              {formatMs(stats.performance.pageLoadTime)}
            </span>
          </div>
          <div className="flex justify-between">
            <span>FCP:</span>
            <span className={getPerformanceColor(stats.performance.firstContentfulPaint, [1500, 3000])}>
              {formatMs(stats.performance.firstContentfulPaint)}
            </span>
          </div>
          <div className="flex justify-between">
            <span>LCP:</span>
            <span className={getPerformanceColor(stats.performance.largestContentfulPaint, [2500, 4000])}>
              {formatMs(stats.performance.largestContentfulPaint)}
            </span>
          </div>
          <div className="flex justify-between">
            <span>FID:</span>
            <span className={getPerformanceColor(stats.performance.firstInputDelay, [100, 300])}>
              {formatMs(stats.performance.firstInputDelay)}
            </span>
          </div>
        </div>
      </div>

      {/* Memory Usage */}
      {stats.memory && (
        <div className="mb-3">
          <h4 className="text-xs font-medium text-gray-600 mb-1">Memory</h4>
          <div className="space-y-1 text-xs">
            <div className="flex justify-between">
              <span>Used:</span>
              <span className={getMemoryColor(stats.memory.usagePercentage)}>
                {formatMB(stats.memory.used)} ({stats.memory.usagePercentage.toFixed(1)}%)
              </span>
            </div>
            <div className="flex justify-between">
              <span>Limit:</span>
              <span className="text-gray-600">{formatMB(stats.memory.limit)}</span>
            </div>
          </div>
        </div>
      )}

      {/* Network Status */}
      <div className="mb-3">
        <h4 className="text-xs font-medium text-gray-600 mb-1">Network</h4>
        <div className="space-y-1 text-xs">
          <div className="flex justify-between">
            <span>Status:</span>
            <span className={stats.network.isOnline ? 'text-green-600' : 'text-red-600'}>
              {stats.network.isOnline ? 'Online' : 'Offline'}
            </span>
          </div>
          {stats.network.effectiveType && (
            <div className="flex justify-between">
              <span>Type:</span>
              <span className="text-gray-600">{stats.network.effectiveType}</span>
            </div>
          )}
          {stats.network.downlink && (
            <div className="flex justify-between">
              <span>Speed:</span>
              <span className="text-gray-600">{stats.network.downlink} Mbps</span>
            </div>
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="border-t pt-2">
        <div className="flex space-x-2">
          <button
            onClick={() => {
              const performanceMonitor = PerformanceMonitor.getInstance();
              performanceMonitor.clearMetrics();
              console.log('Performance metrics cleared');
            }}
            className="text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded"
          >
            Clear Metrics
          </button>
          <button
            onClick={() => {
              console.log('Current monitoring stats:', stats);
            }}
            className="text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded"
          >
            Log Stats
          </button>
        </div>
      </div>
    </div>
  );
}

// Toggle button to show/hide monitoring dashboard
export function MonitoringToggle() {
  const [showDashboard, setShowDashboard] = useState(false);

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <>
      <button
        onClick={() => setShowDashboard(!showDashboard)}
        className="fixed bottom-4 left-4 bg-blue-600 text-white p-2 rounded-full shadow-lg hover:bg-blue-700 z-50"
        title="Toggle Monitoring Dashboard"
      >
        📊
      </button>
      {showDashboard && <MonitoringDashboard />}
    </>
  );
}
