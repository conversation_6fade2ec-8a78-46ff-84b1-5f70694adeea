/**
 * Dashboard Widget Component
 */

import React, { useState, useEffect } from 'react';
import { DashboardWidget as WidgetType, ChartData, WidgetType as WidgetTypeEnum } from '../../lib/types/analytics';
import { widgetApi, withErrorHandling } from '../../lib/api/analytics';
import { logger } from '../../lib/utils/logger';
import { performanceMonitor, usePerformanceMonitoring } from '../../lib/utils/performance';
import { memoryCache, cacheStrategies } from '../../lib/utils/cache';
import ChartWrapper from './charts/ChartWrapper';
import { withErrorBoundary } from './ErrorBoundary';

interface DashboardWidgetProps {
  widget: WidgetType;
  onEdit?: (widget: WidgetType) => void;
  onDelete?: (widgetId: string) => void;
  onRefresh?: (widgetId: string) => void;
  isEditing?: boolean;
}

const DashboardWidget: React.FC<DashboardWidgetProps> = ({
  widget,
  onEdit,
  onDelete,
  onRefresh,
  isEditing = false,
}) => {
  const [chartData, setChartData] = useState<ChartData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  // Performance monitoring
  const { startTiming, recordMetric } = usePerformanceMonitoring(`widget_${widget.id}`);

  // Load widget data with caching and performance monitoring
  const loadWidgetData = async (forceRefresh = false) => {
    const endTiming = startTiming('data_load');

    setLoading(true);
    setError(null);

    try {
      // Generate cache key
      const cacheKey = `widget_data:${widget.id}:${widget.updated_at}`;

      let data;
      if (forceRefresh) {
        // Force refresh bypasses cache
        data = await withErrorHandling(
          () => widgetApi.getWidgetData(widget.id, forceRefresh),
          'Failed to load widget data'
        );
        memoryCache.set(cacheKey, data, widget.cache_duration || 300000);
      } else {
        // Use cache strategy
        data = await cacheStrategies.staleWhileRevalidate(
          cacheKey,
          () => withErrorHandling(
            () => widgetApi.getWidgetData(widget.id, forceRefresh),
            'Failed to load widget data'
          ),
          widget.cache_duration || 300000 // 5 minutes default
        );
      }

      if (data) {
        setChartData(data);
        setLastRefresh(new Date());

        // Log successful load
        logger.userAction('widget_data_loaded', {
          widgetId: widget.id,
          widgetType: widget.widget_type,
          dataPoints: data.data?.length || 0,
          fromCache: !forceRefresh,
        });
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);

      // Log error
      logger.error('Widget data load failed', {
        widgetId: widget.id,
        error: errorMessage,
      });
    } finally {
      setLoading(false);
      endTiming();
    }
  };

  // Load data on mount and when widget changes
  useEffect(() => {
    loadWidgetData();
  }, [widget.id]);

  // Handle refresh
  const handleRefresh = async () => {
    await loadWidgetData(true);
    onRefresh?.(widget.id);
  };

  // Handle edit
  const handleEdit = () => {
    onEdit?.(widget);
  };

  // Handle delete
  const handleDelete = () => {
    if (window.confirm('Are you sure you want to delete this widget?')) {
      onDelete?.(widget.id);
    }
  };

  // Render metric widget
  const renderMetricWidget = () => {
    if (!chartData || !chartData.data) return null;

    // Handle both array and object data structures
    const data = Array.isArray(chartData.data) ? chartData.data[0] : chartData.data;
    const value = data?.value || data?.total_value || 0;
    const label = widget.name;
    const change = data?.change || null;
    const trend = data?.trend || null;

    return (
      <div className="flex flex-col items-center justify-center h-full p-4">
        <div className="text-3xl font-bold text-gray-900 mb-2">
          {typeof value === 'number' ? value.toLocaleString() : value}
        </div>
        <div className="text-sm text-gray-600 text-center mb-2">
          {label}
        </div>
        {change !== null && (
          <div className={`text-sm font-medium ${
            change > 0 ? 'text-green-600' : change < 0 ? 'text-red-600' : 'text-gray-600'
          }`}>
            {change > 0 ? '+' : ''}{change}%
            {trend && (
              <span className="ml-1">
                {trend === 'up' ? '↗' : trend === 'down' ? '↘' : '→'}
              </span>
            )}
          </div>
        )}
      </div>
    );
  };

  // Render table widget
  const renderTableWidget = () => {
    if (!chartData || !Array.isArray(chartData.data)) return null;

    const data = chartData.data.slice(0, 10); // Limit to 10 rows

    return (
      <div className="h-full overflow-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {Object.keys(data[0] || {}).map((key) => (
                <th
                  key={key}
                  className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  {key}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {data.map((row, index) => (
              <tr key={index}>
                {Object.values(row).map((value, cellIndex) => (
                  <td key={cellIndex} className="px-3 py-2 whitespace-nowrap text-sm text-gray-900">
                    {String(value)}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  // Render text widget
  const renderTextWidget = () => {
    const content = widget.chart_config?.content || widget.description || 'No content';
    
    return (
      <div className="h-full p-4 overflow-auto">
        <div className="prose prose-sm max-w-none">
          {content.split('\n').map((line: string, index: number) => (
            <p key={index} className="mb-2">
              {line}
            </p>
          ))}
        </div>
      </div>
    );
  };

  // Render widget content based on type
  const renderWidgetContent = () => {
    switch (widget.widget_type) {
      case WidgetTypeEnum.CHART:
        return (
          <ChartWrapper
            chartType={widget.chart_type!}
            data={chartData}
            config={widget.chart_config}
            loading={loading}
            error={error || undefined}
          />
        );

      case WidgetTypeEnum.METRIC:
        return renderMetricWidget();

      case WidgetTypeEnum.TABLE:
        return renderTableWidget();

      case WidgetTypeEnum.TEXT:
        return renderTextWidget();

      default:
        return (
          <div className="flex items-center justify-center h-full">
            <p className="text-gray-500">Unsupported widget type</p>
          </div>
        );
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 h-full flex flex-col">
      {/* Widget Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200">
        <div className="flex-1 min-w-0">
          <h3 className="text-sm font-medium text-gray-900 truncate">
            {widget.name}
          </h3>
          {widget.description && (
            <p className="text-xs text-gray-500 truncate mt-1">
              {widget.description}
            </p>
          )}
        </div>
        
        {/* Widget Actions */}
        <div className="flex items-center space-x-1 ml-2">
          {/* Refresh Button */}
          <button
            onClick={handleRefresh}
            disabled={loading}
            className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"
            title="Refresh data"
          >
            <svg className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>

          {/* Edit Button */}
          {isEditing && onEdit && (
            <button
              onClick={handleEdit}
              className="p-1 text-gray-400 hover:text-gray-600"
              title="Edit widget"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </button>
          )}

          {/* Delete Button */}
          {isEditing && onDelete && (
            <button
              onClick={handleDelete}
              className="p-1 text-gray-400 hover:text-red-600"
              title="Delete widget"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </button>
          )}
        </div>
      </div>

      {/* Widget Content */}
      <div className="flex-1 p-3 min-h-0">
        {renderWidgetContent()}
      </div>

      {/* Widget Footer */}
      {lastRefresh && (
        <div className="px-3 py-2 border-t border-gray-100 bg-gray-50">
          <p className="text-xs text-gray-500">
            Last updated: {lastRefresh.toLocaleTimeString()}
          </p>
        </div>
      )}
    </div>
  );
};

// Export with error boundary
export default withErrorBoundary(DashboardWidget, 'DashboardWidget');
