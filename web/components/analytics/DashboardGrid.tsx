/**
 * Dashboard Grid Component using react-grid-layout
 */

import React, { useState, useCallback } from 'react';
import { Responsive, WidthProvider, Layout } from 'react-grid-layout';
import { DashboardWidget as WidgetType, Dashboard } from '../../lib/types/analytics';
import { widgetApi, withErrorHandling } from '../../lib/api/analytics';
import DashboardWidget from './DashboardWidget';

// Import CSS for react-grid-layout
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';

const ResponsiveGridLayout = WidthProvider(Responsive);

interface DashboardGridProps {
  dashboard: Dashboard;
  widgets: WidgetType[];
  isEditing?: boolean;
  onWidgetEdit?: (widget: WidgetType) => void;
  onWidgetDelete?: (widgetId: string) => void;
  onLayoutChange?: (layout: Layout[], layouts: any) => void;
}

const DashboardGrid: React.FC<DashboardGridProps> = ({
  dashboard,
  widgets,
  isEditing = false,
  onWidgetEdit,
  onWidgetDelete,
  onLayoutChange,
}) => {
  const [layouts, setLayouts] = useState<any>({});

  // Convert widgets to grid layout format
  const generateLayout = useCallback((widgets: WidgetType[]): Layout[] => {
    return widgets.map((widget) => ({
      i: widget.id,
      x: widget.position.x,
      y: widget.position.y,
      w: widget.position.width,
      h: widget.position.height,
      minW: 2,
      minH: 2,
      maxW: 12,
      maxH: 20,
      static: !isEditing,
      isDraggable: isEditing,
      isResizable: isEditing,
    }));
  }, [isEditing]);

  // Handle layout change
  const handleLayoutChange = useCallback(
    (layout: Layout[], layouts: any) => {
      setLayouts(layouts);
      
      // Update widget positions
      if (isEditing && onLayoutChange) {
        onLayoutChange(layout, layouts);
      }
    },
    [isEditing, onLayoutChange]
  );

  // Handle widget refresh
  const handleWidgetRefresh = useCallback((widgetId: string) => {
    console.log(`Refreshing widget: ${widgetId}`);
  }, []);

  // Handle widget delete
  const handleWidgetDelete = useCallback(
    async (widgetId: string) => {
      try {
        await withErrorHandling(
          () => widgetApi.deleteWidget(widgetId),
          'Failed to delete widget'
        );
        onWidgetDelete?.(widgetId);
      } catch (error) {
        console.error('Error deleting widget:', error);
      }
    },
    [onWidgetDelete]
  );

  // Grid breakpoints
  const breakpoints = {
    lg: 1200,
    md: 996,
    sm: 768,
    xs: 480,
    xxs: 0,
  };

  // Columns for each breakpoint
  const cols = {
    lg: 12,
    md: 10,
    sm: 6,
    xs: 4,
    xxs: 2,
  };

  // Generate layouts for all breakpoints
  const generateLayouts = useCallback(() => {
    const layout = generateLayout(widgets);
    return {
      lg: layout,
      md: layout,
      sm: layout,
      xs: layout,
      xxs: layout,
    };
  }, [widgets, generateLayout]);

  if (!widgets || widgets.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
        <div className="text-center">
          <svg className="w-12 h-12 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No widgets yet</h3>
          <p className="text-gray-500">
            {isEditing 
              ? 'Add your first widget to get started with analytics'
              : 'This dashboard doesn\'t have any widgets yet'
            }
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <ResponsiveGridLayout
        className="layout"
        layouts={Object.keys(layouts).length > 0 ? layouts : generateLayouts()}
        breakpoints={breakpoints}
        cols={cols}
        rowHeight={60}
        margin={[16, 16]}
        containerPadding={[0, 0]}
        isDraggable={isEditing}
        isResizable={isEditing}
        onLayoutChange={handleLayoutChange}
        draggableHandle=".drag-handle"
        resizeHandles={['se']}
        compactType="vertical"
        preventCollision={false}
      >
        {widgets.map((widget) => (
          <div key={widget.id} className="relative">
            {/* Drag handle for editing mode */}
            {isEditing && (
              <div className="drag-handle absolute top-2 left-2 z-10 cursor-move opacity-0 hover:opacity-100 transition-opacity">
                <svg className="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z" />
                </svg>
              </div>
            )}
            
            {/* Widget component */}
            <DashboardWidget
              widget={widget}
              onEdit={onWidgetEdit}
              onDelete={handleWidgetDelete}
              onRefresh={handleWidgetRefresh}
              isEditing={isEditing}
            />
            
            {/* Resize handle for editing mode */}
            {isEditing && (
              <div className="react-resizable-handle react-resizable-handle-se">
                <svg className="w-3 h-3 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M22 22H2L22 2v20z" />
                </svg>
              </div>
            )}
          </div>
        ))}
      </ResponsiveGridLayout>

      {/* Grid overlay for editing mode */}
      {isEditing && (
        <style jsx>{`
          .layout {
            position: relative;
          }
          
          .layout::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
              linear-gradient(to right, rgba(0,0,0,0.1) 1px, transparent 1px),
              linear-gradient(to bottom, rgba(0,0,0,0.1) 1px, transparent 1px);
            background-size: 100px 76px;
            pointer-events: none;
            opacity: 0.3;
            z-index: -1;
          }
          
          .react-grid-item {
            transition: all 200ms ease;
            transition-property: left, top, width, height;
          }
          
          .react-grid-item.cssTransforms {
            transition-property: transform, width, height;
          }
          
          .react-grid-item > .react-resizable-handle {
            position: absolute;
            width: 20px;
            height: 20px;
            bottom: 0;
            right: 0;
            background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNiIgaGVpZ2h0PSI2IiB2aWV3Qm94PSIwIDAgNiA2IiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxnIGZpbGw9IiM0QTVBNjgiIGZpbGwtcnVsZT0iZXZlbm9kZCI+PGNpcmNsZSBjeD0iNSIgY3k9IjUiIHI9IjEiLz48Y2lyY2xlIGN4PSI1IiBjeT0iMSIgcj0iMSIvPjxjaXJjbGUgY3g9IjEiIGN5PSI1IiByPSIxIi8+PC9nPjwvc3ZnPg==') no-repeat;
            background-position: bottom right;
            padding: 0 3px 3px 0;
            background-repeat: no-repeat;
            background-origin: content-box;
            box-sizing: border-box;
            cursor: se-resize;
          }
          
          .react-grid-item.react-grid-placeholder {
            background: rgba(59, 130, 246, 0.15);
            border: 2px dashed rgba(59, 130, 246, 0.4);
            opacity: 0.2;
            transition-duration: 100ms;
            z-index: 2;
            user-select: none;
          }
          
          .react-grid-item.react-draggable-dragging {
            transition: none;
            z-index: 3;
            opacity: 0.8;
          }
          
          .react-grid-item.react-grid-resizing {
            opacity: 0.8;
            z-index: 3;
          }
        `}</style>
      )}
    </div>
  );
};

export default DashboardGrid;
