# Analytics Dashboard System

A comprehensive analytics and data visualization system for the RAID application, providing advanced charts, customizable dashboards, and real-time insights.

## 🚀 Features

### ✅ Completed Features

- **Complete Backend API** - 20+ endpoints for analytics operations
- **Database Models** - Dashboard, Widget, TrendAnalysis, and ChartData models
- **Chart Components** - Interactive charts using Recharts library
- **Dashboard Grid** - Drag-and-drop layout using react-grid-layout
- **TypeScript Integration** - Full type safety with comprehensive interfaces
- **React Query** - Efficient data fetching and caching
- **Responsive Design** - Mobile-friendly dashboard layouts

### 📊 Chart Types

1. **Pie Chart** - Distribution visualization with interactive tooltips
2. **Bar Chart** - Comparative data with horizontal/vertical orientations
3. **Line Chart** - Time series data with trend analysis
4. **Scatter Plot** - Correlation analysis with regression lines
5. **Gauge Chart** - KPI visualization with threshold indicators
6. **Donut Chart** - Pie chart variant with center content
7. **Area Chart** - Filled line charts for cumulative data

### 🎛️ Dashboard Features

- **Drag & Drop Layout** - Customizable widget positioning
- **Responsive Grid** - Adapts to different screen sizes
- **Real-time Updates** - Automatic data refresh capabilities
- **Widget Management** - Create, edit, and delete dashboard widgets
- **Access Control** - Public/private dashboards with user permissions
- **Theme Support** - Light/dark theme compatibility

## 🏗️ Architecture

### Frontend Structure

```
web/components/analytics/
├── charts/
│   ├── PieChart.tsx          # Pie chart component
│   ├── BarChart.tsx          # Bar chart component
│   ├── LineChart.tsx         # Line chart component
│   ├── ScatterChart.tsx      # Scatter plot component
│   ├── GaugeChart.tsx        # Gauge chart component
│   └── ChartWrapper.tsx      # Chart type router
├── DashboardGrid.tsx         # Grid layout manager
├── DashboardView.tsx         # Main dashboard component
├── DashboardWidget.tsx       # Individual widget component
└── README.md                 # This file

web/lib/
├── types/analytics.ts        # TypeScript interfaces
├── api/analytics.ts          # API client functions
└── hooks/useAnalytics.ts     # React Query hooks

web/app/analytics/
├── page.tsx                  # Main analytics page
└── demo/page.tsx            # Demo with sample data
```

### Backend Structure

```
api_py/app/
├── models/analytics.py       # Database models
├── schemas/analytics.py      # Pydantic schemas
├── services/
│   ├── analytics_service.py  # Core analytics logic
│   ├── chart_processor.py    # Chart data processing
│   └── trend_analyzer.py     # Trend analysis algorithms
└── routers/analytics.py      # API endpoints
```

## 🔧 Usage

### Basic Dashboard Creation

```typescript
import { useDashboards, useCreateDashboard } from '@/lib/hooks/useAnalytics';

const MyComponent = () => {
  const { data: dashboards } = useDashboards();
  const createDashboard = useCreateDashboard();

  const handleCreate = async () => {
    await createDashboard.mutateAsync({
      name: 'My Dashboard',
      description: 'Custom analytics dashboard',
      layout: { columns: 12, rows: 20 },
      is_public: false,
    });
  };
};
```

### Widget Management

```typescript
import { useCreateWidget } from '@/lib/hooks/useAnalytics';
import { ChartType, DataSource, WidgetType } from '@/lib/types/analytics';

const createWidget = useCreateWidget();

const addPieChart = async (dashboardId: string) => {
  await createWidget.mutateAsync({
    dashboard_id: dashboardId,
    name: 'Risk Distribution',
    widget_type: WidgetType.CHART,
    chart_type: ChartType.PIE,
    data_source: DataSource.RISKS,
    position: { x: 0, y: 0, width: 6, height: 4 },
    entity_filters: { status: 'active' },
    date_range: { period: 'last_30_days' },
  });
};
```

### Custom Chart Configuration

```typescript
const chartConfig = {
  show_legend: true,
  show_labels: true,
  max_slices: 8,
  color_scheme: 'default',
};

<ChartWrapper
  chartType={ChartType.PIE}
  data={chartData}
  config={chartConfig}
  width={400}
  height={300}
/>
```

## 📡 API Integration

### Dashboard Endpoints

- `GET /api/v1/dashboards` - List all dashboards
- `POST /api/v1/dashboards` - Create new dashboard
- `GET /api/v1/dashboards/{id}` - Get dashboard details
- `PUT /api/v1/dashboards/{id}` - Update dashboard
- `DELETE /api/v1/dashboards/{id}` - Delete dashboard

### Widget Endpoints

- `GET /api/v1/widgets/{id}` - Get widget details
- `POST /api/v1/widgets` - Create new widget
- `PUT /api/v1/widgets/{id}` - Update widget
- `DELETE /api/v1/widgets/{id}` - Delete widget
- `GET /api/v1/widgets/{id}/data` - Get widget data

### Analytics Endpoints

- `POST /api/v1/analytics/query` - Execute analytics query
- `GET /api/v1/analytics/summary` - Get dashboard summary
- `POST /api/v1/charts/process/{type}` - Process chart data

## 🎨 Styling

The system uses Tailwind CSS for styling with custom CSS for react-grid-layout:

```css
/* Grid layout styles */
.react-grid-item {
  transition: all 200ms ease;
  transition-property: left, top, width, height;
}

.react-grid-item.react-grid-placeholder {
  background: rgba(59, 130, 246, 0.15);
  border: 2px dashed rgba(59, 130, 246, 0.4);
}
```

## 🔮 Future Enhancements

### Planned Features

- **Advanced Chart Types** - Heatmaps, funnel charts, radar charts
- **Widget Templates** - Pre-built widget configurations
- **Export Functionality** - PDF/PNG export of dashboards
- **Scheduled Reports** - Automated report generation
- **Advanced Filters** - Complex query builder interface
- **Collaboration** - Dashboard sharing and commenting
- **Mobile App** - Native mobile dashboard viewer

### Technical Improvements

- **Performance Optimization** - Virtual scrolling for large datasets
- **Offline Support** - PWA capabilities with offline caching
- **Real-time Streaming** - WebSocket integration for live updates
- **Advanced Analytics** - Machine learning insights
- **Custom Visualizations** - Plugin system for custom charts

## 🧪 Testing

### Demo Page

Visit `/analytics/demo` to see the analytics system in action with sample data:

- Interactive chart type switching
- Sample data visualization
- Feature demonstrations
- Implementation status overview

### Development Testing

```bash
# Start development server
cd web && npm run dev

# Run tests
npm run test

# Run E2E tests
npm run test:e2e
```

## 📚 Dependencies

### Frontend

- **React** - UI framework
- **Next.js** - React framework with App Router
- **Recharts** - Chart library
- **React Grid Layout** - Drag-and-drop grid system
- **React Query** - Data fetching and caching
- **Tailwind CSS** - Utility-first CSS framework
- **TypeScript** - Type safety

### Backend

- **FastAPI** - Python web framework
- **SQLAlchemy** - Database ORM
- **Pydantic** - Data validation
- **NumPy/Pandas** - Data processing
- **Scikit-learn** - Machine learning algorithms

## 🤝 Contributing

1. Follow the existing code structure and patterns
2. Add TypeScript types for new features
3. Include comprehensive error handling
4. Write tests for new functionality
5. Update documentation for API changes

## 📄 License

This analytics system is part of the RAID application and follows the same licensing terms.
