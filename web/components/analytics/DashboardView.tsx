/**
 * Dashboard View Component
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Layout } from 'react-grid-layout';
import { Dashboard, DashboardWidget, DashboardWidgetUpdate } from '../../lib/types/analytics';
import { dashboardApi, widgetApi, withErrorHandling } from '../../lib/api/analytics';
import DashboardGrid from './DashboardGrid';
// import WidgetModal from './WidgetModal';

interface DashboardViewProps {
  dashboardId: string;
  onDashboardChange?: (dashboard: Dashboard) => void;
}

const DashboardView: React.FC<DashboardViewProps> = ({
  dashboardId,
  onDashboardChange,
}) => {
  const [dashboard, setDashboard] = useState<Dashboard | null>(null);
  const [widgets, setWidgets] = useState<DashboardWidget[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editingWidget, setEditingWidget] = useState<DashboardWidget | null>(null);
  const [showWidgetModal, setShowWidgetModal] = useState(false);
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  // Load dashboard and widgets
  const loadDashboard = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const [dashboardData, widgetsData] = await Promise.all([
        withErrorHandling(
          () => dashboardApi.getDashboard(dashboardId),
          'Failed to load dashboard'
        ),
        withErrorHandling(
          () => dashboardApi.getDashboardWidgets(dashboardId),
          'Failed to load widgets'
        ),
      ]);

      if (dashboardData) {
        setDashboard(dashboardData);
        onDashboardChange?.(dashboardData);
      }

      if (widgetsData) {
        setWidgets(widgetsData.filter(w => w.is_active));
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, [dashboardId, onDashboardChange]);

  // Load dashboard on mount and when ID changes
  useEffect(() => {
    loadDashboard();
  }, [loadDashboard]);

  // Set up auto-refresh
  useEffect(() => {
    if (dashboard?.refresh_interval && dashboard.refresh_interval > 0) {
      const interval = setInterval(() => {
        loadDashboard();
      }, dashboard.refresh_interval * 1000);

      setRefreshInterval(interval);

      return () => {
        clearInterval(interval);
      };
    }

    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [dashboard?.refresh_interval, loadDashboard]);

  // Handle layout change
  const handleLayoutChange = useCallback(
    async (layout: Layout[], layouts: any) => {
      if (!isEditing) return;

      try {
        // Update widget positions
        const updatePromises = layout.map(async (item) => {
          const widget = widgets.find(w => w.id === item.i);
          if (!widget) return;

          const updateData: DashboardWidgetUpdate = {
            position: {
              x: item.x,
              y: item.y,
              width: item.w,
              height: item.h,
            },
          };

          return withErrorHandling(
            () => widgetApi.updateWidget(widget.id, updateData),
            `Failed to update widget ${widget.name}`
          );
        });

        await Promise.all(updatePromises);
      } catch (error) {
        console.error('Error updating widget positions:', error);
      }
    },
    [isEditing, widgets]
  );

  // Handle widget edit
  const handleWidgetEdit = useCallback((widget: DashboardWidget) => {
    setEditingWidget(widget);
    setShowWidgetModal(true);
  }, []);

  // Handle widget delete
  const handleWidgetDelete = useCallback(
    (widgetId: string) => {
      setWidgets(prev => prev.filter(w => w.id !== widgetId));
    },
    []
  );

  // Handle add widget
  const handleAddWidget = useCallback(() => {
    setEditingWidget(null);
    setShowWidgetModal(true);
  }, []);

  // Handle widget modal close
  const handleWidgetModalClose = useCallback(() => {
    setShowWidgetModal(false);
    setEditingWidget(null);
  }, []);

  // Handle widget save
  const handleWidgetSave = useCallback(
    (widget: DashboardWidget) => {
      if (editingWidget) {
        // Update existing widget
        setWidgets(prev => prev.map(w => w.id === widget.id ? widget : w));
      } else {
        // Add new widget
        setWidgets(prev => [...prev, widget]);
      }
      handleWidgetModalClose();
    },
    [editingWidget, handleWidgetModalClose]
  );

  // Handle refresh dashboard
  const handleRefresh = useCallback(() => {
    loadDashboard();
  }, [loadDashboard]);

  // Handle toggle edit mode
  const handleToggleEdit = useCallback(() => {
    setIsEditing(prev => !prev);
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex flex-col items-center space-y-2">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <p className="text-sm text-gray-500">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-red-500 mb-2">
            <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <p className="text-sm text-gray-600 mb-4">Failed to load dashboard</p>
          <p className="text-xs text-gray-400 mb-4">{error}</p>
          <button
            onClick={handleRefresh}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!dashboard) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500">Dashboard not found</p>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Dashboard Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{dashboard.name}</h1>
          {dashboard.description && (
            <p className="text-gray-600 mt-1">{dashboard.description}</p>
          )}
        </div>

        <div className="flex items-center space-x-2">
          {/* Refresh Button */}
          <button
            onClick={handleRefresh}
            className="px-3 py-2 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50"
            title="Refresh dashboard"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>

          {/* Add Widget Button */}
          {isEditing && (
            <button
              onClick={handleAddWidget}
              className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              <svg className="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              Add Widget
            </button>
          )}

          {/* Edit Toggle Button */}
          <button
            onClick={handleToggleEdit}
            className={`px-3 py-2 rounded-md ${
              isEditing
                ? 'bg-green-600 text-white hover:bg-green-700'
                : 'bg-gray-600 text-white hover:bg-gray-700'
            }`}
          >
            {isEditing ? (
              <>
                <svg className="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Done
              </>
            ) : (
              <>
                <svg className="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                Edit
              </>
            )}
          </button>
        </div>
      </div>

      {/* Dashboard Grid */}
      <div className="flex-1">
        <DashboardGrid
          dashboard={dashboard}
          widgets={widgets}
          isEditing={isEditing}
          onWidgetEdit={handleWidgetEdit}
          onWidgetDelete={handleWidgetDelete}
          onLayoutChange={handleLayoutChange}
        />
      </div>

      {/* Widget Modal */}
      {showWidgetModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-medium mb-4">
              {editingWidget ? 'Edit Widget' : 'Add Widget'}
            </h3>
            <p className="text-gray-600 mb-4">Widget modal coming soon...</p>
            <div className="flex justify-end space-x-2">
              <button
                onClick={handleWidgetModalClose}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DashboardView;
