/**
 * Production-ready Error Boundary for Analytics Components
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { logger, logComponentError } from '../../lib/utils/logger';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  componentName?: string;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
  errorId?: string;
}

class AnalyticsErrorBoundary extends Component<Props, State> {
  private retryCount = 0;
  private maxRetries = 3;

  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const componentName = this.props.componentName || 'AnalyticsComponent';
    
    // Log the error
    logComponentError(error, errorInfo, componentName);
    
    // Call custom error handler
    this.props.onError?.(error, errorInfo);

    // Update state with error info
    this.setState({
      error,
      errorInfo,
    });

    // Send error to monitoring service
    this.sendErrorToMonitoring(error, errorInfo, componentName);
  }

  private async sendErrorToMonitoring(error: Error, errorInfo: ErrorInfo, componentName: string) {
    try {
      // Send to error monitoring service (e.g., Sentry)
      await fetch('/api/errors', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          error: {
            message: error.message,
            stack: error.stack,
            name: error.name,
          },
          errorInfo: {
            componentStack: errorInfo.componentStack,
          },
          context: {
            componentName,
            url: window.location.href,
            userAgent: navigator.userAgent,
            timestamp: new Date().toISOString(),
            errorId: this.state.errorId,
          },
        }),
      });
    } catch (monitoringError) {
      logger.error('Failed to send error to monitoring service', {
        originalError: error.message,
        monitoringError: monitoringError instanceof Error ? monitoringError.message : 'Unknown error',
      });
    }
  }

  private handleRetry = () => {
    if (this.retryCount < this.maxRetries) {
      this.retryCount++;
      logger.info(`Retrying component render (attempt ${this.retryCount}/${this.maxRetries})`, {
        componentName: this.props.componentName,
        errorId: this.state.errorId,
      });
      
      this.setState({
        hasError: false,
        error: undefined,
        errorInfo: undefined,
        errorId: undefined,
      });
    }
  };

  private handleReportIssue = () => {
    const { error, errorInfo, errorId } = this.state;
    const componentName = this.props.componentName || 'AnalyticsComponent';

    // Create issue report
    const issueData = {
      title: `Analytics Error: ${error?.message}`,
      description: `
**Error ID:** ${errorId}
**Component:** ${componentName}
**Error Message:** ${error?.message}
**Stack Trace:**
\`\`\`
${error?.stack}
\`\`\`
**Component Stack:**
\`\`\`
${errorInfo?.componentStack}
\`\`\`
**URL:** ${window.location.href}
**User Agent:** ${navigator.userAgent}
**Timestamp:** ${new Date().toISOString()}
      `,
      labels: ['bug', 'analytics', 'error-boundary'],
    };

    // Copy to clipboard or open issue tracker
    navigator.clipboard?.writeText(JSON.stringify(issueData, null, 2))
      .then(() => {
        alert('Error details copied to clipboard. Please paste in your issue tracker.');
      })
      .catch(() => {
        // Fallback: open mailto
        const subject = encodeURIComponent(issueData.title);
        const body = encodeURIComponent(issueData.description);
        window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`);
      });
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 m-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="h-6 w-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-3 flex-1">
              <h3 className="text-sm font-medium text-red-800">
                Analytics Component Error
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <p>
                  Something went wrong while rendering the analytics component.
                  {this.state.error?.message && (
                    <span className="block mt-1 font-mono text-xs bg-red-100 p-2 rounded">
                      {this.state.error.message}
                    </span>
                  )}
                </p>
              </div>
              
              {/* Error ID for support */}
              {this.state.errorId && (
                <div className="mt-3 text-xs text-red-600">
                  Error ID: <code className="bg-red-100 px-1 rounded">{this.state.errorId}</code>
                </div>
              )}

              {/* Action buttons */}
              <div className="mt-4 flex space-x-2">
                {this.retryCount < this.maxRetries && (
                  <button
                    onClick={this.handleRetry}
                    className="bg-red-100 text-red-800 px-3 py-1 rounded text-sm hover:bg-red-200 transition-colors"
                  >
                    Try Again ({this.maxRetries - this.retryCount} attempts left)
                  </button>
                )}
                
                <button
                  onClick={this.handleReportIssue}
                  className="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700 transition-colors"
                >
                  Report Issue
                </button>
                
                <button
                  onClick={() => window.location.reload()}
                  className="bg-gray-600 text-white px-3 py-1 rounded text-sm hover:bg-gray-700 transition-colors"
                >
                  Reload Page
                </button>
              </div>

              {/* Development mode details */}
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <details className="mt-4">
                  <summary className="text-sm text-red-700 cursor-pointer hover:text-red-800">
                    Show Error Details (Development)
                  </summary>
                  <div className="mt-2 p-3 bg-red-100 rounded text-xs font-mono overflow-auto max-h-40">
                    <div className="mb-2">
                      <strong>Error:</strong> {this.state.error.message}
                    </div>
                    <div className="mb-2">
                      <strong>Stack:</strong>
                      <pre className="whitespace-pre-wrap">{this.state.error.stack}</pre>
                    </div>
                    {this.state.errorInfo && (
                      <div>
                        <strong>Component Stack:</strong>
                        <pre className="whitespace-pre-wrap">{this.state.errorInfo.componentStack}</pre>
                      </div>
                    )}
                  </div>
                </details>
              )}
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// HOC for wrapping components with error boundary
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  componentName?: string,
  fallback?: ReactNode
) => {
  const WrappedComponent = React.forwardRef<any, P>((props, ref) => (
    <AnalyticsErrorBoundary
      componentName={componentName || Component.displayName || Component.name}
      fallback={fallback}
    >
      <Component {...(props as P)} />
    </AnalyticsErrorBoundary>
  ));

  WrappedComponent.displayName = `withErrorBoundary(${componentName || Component.displayName || Component.name})`;

  return WrappedComponent;
};

// Hook for error handling in functional components
export const useErrorHandler = (componentName?: string) => {
  const handleError = React.useCallback((error: Error, errorInfo?: any) => {
    logComponentError(error, errorInfo, componentName || 'UnknownComponent');
  }, [componentName]);

  return handleError;
};

// Async error boundary for handling promise rejections
export const AsyncErrorBoundary: React.FC<{
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error) => void;
}> = ({ children, fallback, onError }) => {
  const [error, setError] = React.useState<Error | null>(null);

  React.useEffect(() => {
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      const error = new Error(event.reason?.message || 'Unhandled promise rejection');
      setError(error);
      onError?.(error);
      logger.error('Unhandled promise rejection', { reason: event.reason });
    };

    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    
    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, [onError]);

  if (error) {
    return (
      <AnalyticsErrorBoundary fallback={fallback}>
        {children}
      </AnalyticsErrorBoundary>
    );
  }

  return <>{children}</>;
};

export default AnalyticsErrorBoundary;
