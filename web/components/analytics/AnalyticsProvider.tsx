/**
 * Analytics Provider with production-ready features
 */

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { logger } from '../../lib/utils/logger';
import { performanceMonitor } from '../../lib/utils/performance';
import { config, features, development } from '../../lib/config/production';
import AnalyticsErrorBoundary from './ErrorBoundary';

// Analytics context interface
interface AnalyticsContextType {
  isOnline: boolean;
  isLoading: boolean;
  error: string | null;
  retryConnection: () => void;
  clearError: () => void;
  config: typeof config;
  features: typeof features;
}

// Create context
const AnalyticsContext = createContext<AnalyticsContextType | undefined>(undefined);

// Query client configuration
const createQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
        retry: (failureCount, error: any) => {
          // Don't retry on 4xx errors
          if (error?.response?.status >= 400 && error?.response?.status < 500) {
            return false;
          }
          return failureCount < 3;
        },
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
        refetchOnWindowFocus: config.NODE_ENV === 'production',
        refetchOnReconnect: true,
        refetchOnMount: true,
      },
      mutations: {
        retry: 1,
        retryDelay: 1000,
      },
    },
  });
};

// Provider component
interface AnalyticsProviderProps {
  children: ReactNode;
  fallback?: ReactNode;
}

export const AnalyticsProvider: React.FC<AnalyticsProviderProps> = ({
  children,
  fallback,
}) => {
  const [queryClient] = useState(() => createQueryClient());
  const [isOnline, setIsOnline] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Initialize analytics
  useEffect(() => {
    const initializeAnalytics = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Check API connectivity
        const response = await fetch(`${config.NEXT_PUBLIC_API_URL}/health`, {
          method: 'HEAD',
          timeout: 5000,
        } as RequestInit);

        if (!response.ok) {
          throw new Error(`API health check failed: ${response.status}`);
        }

        logger.info('Analytics system initialized successfully');
        performanceMonitor.recordMetric('analytics_init', performance.now());

      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown initialization error';
        setError(errorMessage);
        logger.error('Analytics initialization failed', { error: errorMessage });
      } finally {
        setIsLoading(false);
      }
    };

    initializeAnalytics();
  }, []);

  // Online/offline detection
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      logger.info('Connection restored');
    };

    const handleOffline = () => {
      setIsOnline(false);
      logger.warn('Connection lost');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Performance monitoring
  useEffect(() => {
    const interval = setInterval(() => {
      const memory = performanceMonitor.getMemoryUsage();
      if (memory) {
        performanceMonitor.recordMetric('memory_usage', memory.used, 'bytes');
        
        // Warn if memory usage is high
        if (memory.used > memory.limit * 0.8) {
          logger.warn('High memory usage detected', { memory });
        }
      }
    }, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, []);

  // Error recovery
  const retryConnection = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`${config.NEXT_PUBLIC_API_URL}/health`, {
        method: 'HEAD',
        timeout: 5000,
      } as RequestInit);

      if (!response.ok) {
        throw new Error(`Connection test failed: ${response.status}`);
      }

      logger.info('Connection retry successful');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Connection retry failed';
      setError(errorMessage);
      logger.error('Connection retry failed', { error: errorMessage });
    } finally {
      setIsLoading(false);
    }
  };

  const clearError = () => {
    setError(null);
  };

  // Context value
  const contextValue: AnalyticsContextType = {
    isOnline,
    isLoading,
    error,
    retryConnection,
    clearError,
    config,
    features,
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-lg font-semibold text-gray-900 mb-2">
            Initializing Analytics
          </h2>
          <p className="text-gray-600">
            Setting up data visualization and monitoring...
          </p>
        </div>
      </div>
    );
  }

  // Error state with retry
  if (error && !isOnline) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="text-red-500 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Connection Error
          </h2>
          <p className="text-gray-600 mb-6">
            Unable to connect to the analytics service. Please check your internet connection.
          </p>
          <div className="space-y-3">
            <button
              onClick={retryConnection}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Retry Connection
            </button>
            <button
              onClick={clearError}
              className="w-full px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
            >
              Continue Offline
            </button>
          </div>
          {development.showErrorDetails && (
            <details className="mt-4 text-left">
              <summary className="text-sm text-gray-500 cursor-pointer">
                Error Details
              </summary>
              <pre className="mt-2 p-3 bg-gray-100 rounded text-xs overflow-auto">
                {error}
              </pre>
            </details>
          )}
        </div>
      </div>
    );
  }

  return (
    <AnalyticsErrorBoundary fallback={fallback}>
      <AnalyticsContext.Provider value={contextValue}>
        <QueryClientProvider client={queryClient}>
          {/* Offline indicator */}
          {!isOnline && (
            <div className="bg-yellow-50 border-b border-yellow-200 px-4 py-2">
              <div className="flex items-center justify-center">
                <svg className="w-4 h-4 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="text-sm text-yellow-800">
                  You're currently offline. Some features may be limited.
                </span>
              </div>
            </div>
          )}

          {/* Error indicator */}
          {error && isOnline && (
            <div className="bg-red-50 border-b border-red-200 px-4 py-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <svg className="w-4 h-4 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span className="text-sm text-red-800">
                    Analytics service error: {error}
                  </span>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={retryConnection}
                    className="text-xs text-red-700 hover:text-red-900 underline"
                  >
                    Retry
                  </button>
                  <button
                    onClick={clearError}
                    className="text-xs text-red-700 hover:text-red-900 underline"
                  >
                    Dismiss
                  </button>
                </div>
              </div>
            </div>
          )}

          {children}

          {/* Development tools */}
          {development.enableDebugLogs && (
            <ReactQueryDevtools initialIsOpen={false} />
          )}
        </QueryClientProvider>
      </AnalyticsContext.Provider>
    </AnalyticsErrorBoundary>
  );
};

// Hook to use analytics context
export const useAnalyticsContext = (): AnalyticsContextType => {
  const context = useContext(AnalyticsContext);
  if (context === undefined) {
    throw new Error('useAnalyticsContext must be used within an AnalyticsProvider');
  }
  return context;
};

// Hook for feature flags
export const useFeatureFlag = (feature: keyof typeof features): boolean => {
  const { features: enabledFeatures } = useAnalyticsContext();
  return enabledFeatures[feature];
};

// Hook for configuration
export const useAnalyticsConfig = () => {
  const { config } = useAnalyticsContext();
  return config;
};

export default AnalyticsProvider;
