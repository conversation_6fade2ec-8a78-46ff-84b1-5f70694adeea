/**
 * Line Chart Component
 */

import React from 'react';
import {
  LineChart as Re<PERSON>rtsLine<PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Area,
  AreaChart,
} from 'recharts';
import { LineChartConfig } from '../../../lib/types/analytics';
import { format, parseISO } from 'date-fns';

interface LineChartProps {
  data: Array<{
    x: string;
    y: number;
    count: number;
    avg: number;
    filled?: boolean;
  }>;
  config?: LineChartConfig;
  width?: number;
  height?: number;
}

const LineChart: React.FC<LineChartProps> = ({
  data,
  config = {},
  width = 400,
  height = 300,
}) => {
  const {
    show_legend = false,
    show_grid = true,
    show_dots = true,
    smooth_line = false,
    fill_area = false,
    stroke_width = 2,
  } = config;

  const formatXAxisLabel = (tickItem: string) => {
    try {
      const date = parseISO(tickItem);
      return format(date, 'MMM dd');
    } catch {
      return tickItem;
    }
  };

  const renderTooltip = (props: any) => {
    if (!props.active || !props.payload || !props.payload[0]) return null;
    
    const data = props.payload[0].payload;
    const date = data.x;
    
    let formattedDate = date;
    try {
      formattedDate = format(parseISO(date), 'MMM dd, yyyy HH:mm');
    } catch {
      // Keep original if parsing fails
    }
    
    return (
      <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
        <p className="font-medium text-gray-900">{formattedDate}</p>
        <p className="text-sm text-gray-600">
          Value: <span className="font-medium">{data.y.toLocaleString()}</span>
        </p>
        <p className="text-sm text-gray-600">
          Count: <span className="font-medium">{data.count}</span>
        </p>
        <p className="text-sm text-gray-600">
          Average: <span className="font-medium">{data.avg.toFixed(2)}</span>
        </p>
        {data.filled && (
          <p className="text-xs text-gray-400 italic">Interpolated data</p>
        )}
      </div>
    );
  };

  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-full">
        <p className="text-gray-500">No data available</p>
      </div>
    );
  }

  const ChartComponent = fill_area ? AreaChart : RechartsLineChart;

  return (
    <ResponsiveContainer width="100%" height={height}>
      <ChartComponent
        data={data}
        margin={{
          top: 5,
          right: 30,
          left: 20,
          bottom: 5,
        }}
      >
        {show_grid && <CartesianGrid strokeDasharray="3 3" />}
        <XAxis 
          dataKey="x" 
          tickFormatter={formatXAxisLabel}
          interval="preserveStartEnd"
        />
        <YAxis />
        <Tooltip content={renderTooltip} />
        {show_legend && <Legend />}
        
        {fill_area ? (
          <Area
            type={smooth_line ? 'monotone' : 'linear'}
            dataKey="y"
            stroke="#8884d8"
            strokeWidth={stroke_width}
            fill="#8884d8"
            fillOpacity={0.3}
            dot={show_dots}
          />
        ) : (
          <Line
            type={smooth_line ? 'monotone' : 'linear'}
            dataKey="y"
            stroke="#8884d8"
            strokeWidth={stroke_width}
            dot={show_dots}
            activeDot={{ r: 6 }}
          />
        )}
      </ChartComponent>
    </ResponsiveContainer>
  );
};

export default LineChart;
