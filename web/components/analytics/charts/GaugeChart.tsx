/**
 * Gauge Chart Component
 */

import React from 'react';
import { GaugeConfig } from '../../../lib/types/analytics';

interface GaugeChartProps {
  data: {
    value: number;
    percentage: number;
    normalized: number;
    status: string;
  };
  metadata: {
    min_value: number;
    max_value: number;
    thresholds: Record<string, number>;
    chart_type: string;
  };
  config?: GaugeConfig;
  width?: number;
  height?: number;
}

const GaugeChart: React.FC<GaugeChartProps> = ({
  data,
  metadata,
  config = {},
  width = 300,
  height = 200,
}) => {
  const {
    show_value = true,
    show_percentage = true,
    color_scheme = 'default',
  } = config;

  const { value, percentage, normalized, status } = data;
  const { min_value, max_value, thresholds } = metadata;

  // Calculate gauge parameters
  const radius = Math.min(width, height) * 0.35;
  const centerX = width / 2;
  const centerY = height * 0.7;
  const startAngle = -180;
  const endAngle = 0;
  const angleRange = endAngle - startAngle;
  const currentAngle = startAngle + (angleRange * normalized);

  // Color based on status and thresholds
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'critical':
        return '#ef4444'; // red-500
      case 'warning':
        return '#f59e0b'; // amber-500
      case 'good':
        return '#10b981'; // emerald-500
      default:
        return '#6b7280'; // gray-500
    }
  };

  const statusColor = getStatusColor(status);

  // Create SVG path for gauge arc
  const createArcPath = (
    centerX: number,
    centerY: number,
    radius: number,
    startAngle: number,
    endAngle: number
  ) => {
    const start = polarToCartesian(centerX, centerY, radius, endAngle);
    const end = polarToCartesian(centerX, centerY, radius, startAngle);
    const largeArcFlag = endAngle - startAngle <= 180 ? '0' : '1';
    
    return [
      'M', start.x, start.y,
      'A', radius, radius, 0, largeArcFlag, 0, end.x, end.y
    ].join(' ');
  };

  const polarToCartesian = (
    centerX: number,
    centerY: number,
    radius: number,
    angleInDegrees: number
  ) => {
    const angleInRadians = (angleInDegrees - 90) * Math.PI / 180.0;
    return {
      x: centerX + (radius * Math.cos(angleInRadians)),
      y: centerY + (radius * Math.sin(angleInRadians))
    };
  };

  // Create needle path
  const needleEnd = polarToCartesian(centerX, centerY, radius - 10, currentAngle);
  const needlePath = `M ${centerX} ${centerY} L ${needleEnd.x} ${needleEnd.y}`;

  // Create threshold markers
  const thresholdMarkers = Object.entries(thresholds).map(([name, thresholdValue]) => {
    const thresholdNormalized = (thresholdValue - min_value) / (max_value - min_value);
    const thresholdAngle = startAngle + (angleRange * thresholdNormalized);
    const markerStart = polarToCartesian(centerX, centerY, radius - 15, thresholdAngle);
    const markerEnd = polarToCartesian(centerX, centerY, radius - 5, thresholdAngle);
    
    return (
      <g key={name}>
        <line
          x1={markerStart.x}
          y1={markerStart.y}
          x2={markerEnd.x}
          y2={markerEnd.y}
          stroke="#374151"
          strokeWidth="2"
        />
        <text
          x={markerStart.x}
          y={markerStart.y - 5}
          textAnchor="middle"
          className="text-xs fill-gray-600"
        >
          {name}
        </text>
      </g>
    );
  });

  return (
    <div className="flex flex-col items-center justify-center w-full h-full">
      <svg width={width} height={height} className="overflow-visible">
        {/* Background arc */}
        <path
          d={createArcPath(centerX, centerY, radius, startAngle, endAngle)}
          fill="none"
          stroke="#e5e7eb"
          strokeWidth="20"
          strokeLinecap="round"
        />
        
        {/* Progress arc */}
        <path
          d={createArcPath(centerX, centerY, radius, startAngle, currentAngle)}
          fill="none"
          stroke={statusColor}
          strokeWidth="20"
          strokeLinecap="round"
        />
        
        {/* Threshold markers */}
        {thresholdMarkers}
        
        {/* Needle */}
        <line
          x1={centerX}
          y1={centerY}
          x2={needleEnd.x}
          y2={needleEnd.y}
          stroke="#1f2937"
          strokeWidth="3"
          strokeLinecap="round"
        />
        
        {/* Center dot */}
        <circle
          cx={centerX}
          cy={centerY}
          r="6"
          fill="#1f2937"
        />
        
        {/* Min/Max labels */}
        <text
          x={centerX - radius + 10}
          y={centerY + 20}
          textAnchor="middle"
          className="text-sm fill-gray-600"
        >
          {min_value}
        </text>
        <text
          x={centerX + radius - 10}
          y={centerY + 20}
          textAnchor="middle"
          className="text-sm fill-gray-600"
        >
          {max_value}
        </text>
      </svg>
      
      {/* Value display */}
      <div className="mt-4 text-center">
        {show_value && (
          <div className="text-2xl font-bold text-gray-900">
            {value.toLocaleString()}
          </div>
        )}
        {show_percentage && (
          <div className="text-lg text-gray-600">
            {percentage.toFixed(1)}%
          </div>
        )}
        <div className={`text-sm font-medium capitalize ${
          status === 'critical' ? 'text-red-600' :
          status === 'warning' ? 'text-amber-600' :
          status === 'good' ? 'text-emerald-600' :
          'text-gray-600'
        }`}>
          {status}
        </div>
      </div>
    </div>
  );
};

export default GaugeChart;
