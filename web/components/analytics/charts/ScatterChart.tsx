/**
 * Scatter Chart Component
 */

import React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON> as Recha<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ianG<PERSON>,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
  Cell,
} from 'recharts';
import { ScatterChartConfig } from '../../../lib/types/analytics';

interface ScatterChartProps {
  data: Array<{
    x: number;
    y: number;
    size?: number;
    group?: string;
    color?: string;
    metadata?: Record<string, any>;
  }>;
  config?: ScatterChartConfig;
  width?: number;
  height?: number;
  correlation?: number;
}

const ScatterChart: React.FC<ScatterChartProps> = ({
  data,
  config = {},
  width = 400,
  height = 300,
  correlation,
}) => {
  const {
    show_legend = true,
    show_grid = true,
    dot_size = 6,
    show_regression = false,
  } = config;

  const renderTooltip = (props: any) => {
    if (!props.active || !props.payload || !props.payload[0]) return null;
    
    const point = props.payload[0].payload;
    return (
      <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
        <p className="font-medium text-gray-900">
          {point.group && `${point.group} - `}Point
        </p>
        <p className="text-sm text-gray-600">
          X: <span className="font-medium">{point.x.toFixed(2)}</span>
        </p>
        <p className="text-sm text-gray-600">
          Y: <span className="font-medium">{point.y.toFixed(2)}</span>
        </p>
        {point.size && (
          <p className="text-sm text-gray-600">
            Size: <span className="font-medium">{point.size.toFixed(2)}</span>
          </p>
        )}
        {point.metadata && Object.keys(point.metadata).length > 0 && (
          <div className="mt-2 pt-2 border-t border-gray-100">
            {Object.entries(point.metadata).map(([key, value]) => (
              <p key={key} className="text-xs text-gray-500">
                {key}: {String(value)}
              </p>
            ))}
          </div>
        )}
      </div>
    );
  };

  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-full">
        <p className="text-gray-500">No data available</p>
      </div>
    );
  }

  // Group data by group field if it exists
  const groupedData = data.reduce((acc, point) => {
    const group = point.group || 'default';
    if (!acc[group]) {
      acc[group] = [];
    }
    acc[group].push(point);
    return acc;
  }, {} as Record<string, typeof data>);

  const groups = Object.keys(groupedData);
  const colors = [
    '#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#00ff00',
    '#ff00ff', '#00ffff', '#ff0000', '#0000ff', '#ffff00'
  ];

  return (
    <div className="w-full h-full">
      {correlation !== undefined && (
        <div className="mb-2 text-sm text-gray-600">
          Correlation: <span className="font-medium">{correlation.toFixed(3)}</span>
        </div>
      )}
      
      <ResponsiveContainer width="100%" height={height - (correlation !== undefined ? 30 : 0)}>
        <RechartsScatterChart
          margin={{
            top: 20,
            right: 20,
            bottom: 20,
            left: 20,
          }}
        >
          {show_grid && <CartesianGrid />}
          <XAxis type="number" dataKey="x" name="X" />
          <YAxis type="number" dataKey="y" name="Y" />
          <Tooltip content={renderTooltip} />
          {show_legend && groups.length > 1 && <Legend />}
          
          {groups.map((group, index) => (
            <Scatter
              key={group}
              name={group !== 'default' ? group : 'Data'}
              data={groupedData[group]}
              fill={colors[index % colors.length]}
            >
              {groupedData[group].map((point, pointIndex) => (
                <Cell
                  key={`cell-${pointIndex}`}
                  fill={point.color || colors[index % colors.length]}
                />
              ))}
            </Scatter>
          ))}
        </RechartsScatterChart>
      </ResponsiveContainer>
    </div>
  );
};

export default ScatterChart;
