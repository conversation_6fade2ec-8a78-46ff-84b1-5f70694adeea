/**
 * Chart Wrapper Component - Renders different chart types
 */

import React from 'react';
import { ChartType } from '../../../lib/types/analytics';
import <PERSON><PERSON><PERSON> from './PieChart';
import Bar<PERSON>hart from './BarChart';
import <PERSON><PERSON><PERSON> from './LineChart';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from './ScatterChart';
import Gau<PERSON><PERSON><PERSON> from './GaugeChart';

interface ChartWrapperProps {
  chartType: ChartType;
  data: any;
  config?: any;
  width?: number;
  height?: number;
  loading?: boolean;
  error?: string;
}

const ChartWrapper: React.FC<ChartWrapperProps> = ({
  chartType,
  data,
  config = {},
  width = 400,
  height = 300,
  loading = false,
  error,
}) => {
  // Loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center w-full h-full">
        <div className="flex flex-col items-center space-y-2">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <p className="text-sm text-gray-500">Loading chart...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center w-full h-full">
        <div className="text-center">
          <div className="text-red-500 mb-2">
            <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <p className="text-sm text-gray-600">Failed to load chart</p>
          <p className="text-xs text-gray-400 mt-1">{error}</p>
        </div>
      </div>
    );
  }

  // No data state
  if (!data || (Array.isArray(data.data) && data.data.length === 0)) {
    return (
      <div className="flex items-center justify-center w-full h-full">
        <div className="text-center">
          <div className="text-gray-400 mb-2">
            <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <p className="text-sm text-gray-500">No data available</p>
        </div>
      </div>
    );
  }

  // Render appropriate chart based on type
  const renderChart = () => {
    switch (chartType) {
      case ChartType.PIE:
        return (
          <PieChart
            data={data.data}
            config={config}
            width={width}
            height={height}
          />
        );

      case ChartType.BAR:
        return (
          <BarChart
            data={data.data}
            config={config}
            width={width}
            height={height}
          />
        );

      case ChartType.LINE:
        return (
          <LineChart
            data={data.data}
            config={config}
            width={width}
            height={height}
          />
        );

      case ChartType.AREA:
        return (
          <LineChart
            data={data.data}
            config={{ ...config, fill_area: true }}
            width={width}
            height={height}
          />
        );

      case ChartType.SCATTER:
        return (
          <ScatterChart
            data={data.data}
            config={config}
            width={width}
            height={height}
            correlation={data.metadata?.correlation}
          />
        );

      case ChartType.GAUGE:
        return (
          <GaugeChart
            data={data.data}
            metadata={data.metadata}
            config={config}
            width={width}
            height={height}
          />
        );

      case ChartType.DONUT:
        return (
          <PieChart
            data={data.data}
            config={{ ...config, inner_radius: 40 }}
            width={width}
            height={height}
          />
        );

      case ChartType.HEATMAP:
        return (
          <div className="flex items-center justify-center w-full h-full">
            <p className="text-gray-500">Heatmap visualization coming soon</p>
          </div>
        );

      case ChartType.FUNNEL:
        return (
          <div className="flex items-center justify-center w-full h-full">
            <p className="text-gray-500">Funnel chart visualization coming soon</p>
          </div>
        );

      case ChartType.RADAR:
        return (
          <div className="flex items-center justify-center w-full h-full">
            <p className="text-gray-500">Radar chart visualization coming soon</p>
          </div>
        );

      default:
        return (
          <div className="flex items-center justify-center w-full h-full">
            <p className="text-gray-500">Unsupported chart type: {chartType}</p>
          </div>
        );
    }
  };

  return (
    <div className="w-full h-full">
      {renderChart()}
      
      {/* Chart metadata */}
      {data.metadata && (
        <div className="mt-2 text-xs text-gray-500">
          {data.metadata.processing_time_ms && (
            <span>Processed in {data.metadata.processing_time_ms}ms</span>
          )}
          {data.metadata.total_value && (
            <span className="ml-2">Total: {data.metadata.total_value.toLocaleString()}</span>
          )}
          {data.metadata.point_count && (
            <span className="ml-2">Points: {data.metadata.point_count}</span>
          )}
        </div>
      )}
    </div>
  );
};

export default ChartWrapper;
