/**
 * Bar Chart Component
 */

import React from 'react';
import {
  <PERSON><PERSON><PERSON> as Re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { BarChartConfig } from '../../../lib/types/analytics';

interface BarChartProps {
  data: Array<{
    x: string;
    y: number;
    count: number;
    avg: number;
    color: string;
  }>;
  config?: BarChartConfig;
  width?: number;
  height?: number;
}

const BarChart: React.FC<BarChartProps> = ({
  data,
  config = {},
  width = 400,
  height = 300,
}) => {
  const {
    show_legend = false,
    show_grid = true,
    orientation = 'vertical',
    stack_bars = false,
    bar_size = undefined,
  } = config;

  const renderTooltip = (props: any) => {
    if (!props.active || !props.payload || !props.payload[0]) return null;
    
    const data = props.payload[0].payload;
    return (
      <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
        <p className="font-medium text-gray-900">{data.x}</p>
        <p className="text-sm text-gray-600">
          Total: <span className="font-medium">{data.y.toLocaleString()}</span>
        </p>
        <p className="text-sm text-gray-600">
          Count: <span className="font-medium">{data.count}</span>
        </p>
        <p className="text-sm text-gray-600">
          Average: <span className="font-medium">{data.avg.toFixed(2)}</span>
        </p>
      </div>
    );
  };

  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-full">
        <p className="text-gray-500">No data available</p>
      </div>
    );
  }

  return (
    <ResponsiveContainer width="100%" height={height}>
      <RechartsBarChart
        data={data}
        layout={orientation === 'horizontal' ? 'horizontal' : 'vertical'}
        margin={{
          top: 5,
          right: 30,
          left: 20,
          bottom: 5,
        }}
      >
        {show_grid && <CartesianGrid strokeDasharray="3 3" />}
        
        {orientation === 'horizontal' ? (
          <>
            <XAxis type="number" />
            <YAxis dataKey="x" type="category" width={100} />
          </>
        ) : (
          <>
            <XAxis dataKey="x" />
            <YAxis />
          </>
        )}
        
        <Tooltip content={renderTooltip} />
        {show_legend && <Legend />}
        
        <Bar
          dataKey="y"
          fill={(entry: any) => entry.color || '#8884d8'}
          maxBarSize={bar_size}
        >
          {data.map((entry, index) => (
            <Bar key={`bar-${index}`} fill={entry.color} />
          ))}
        </Bar>
      </RechartsBarChart>
    </ResponsiveContainer>
  );
};

export default BarChart;
