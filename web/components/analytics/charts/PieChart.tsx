/**
 * Pie Chart Component
 */

import React from 'react';
import {
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  Responsive<PERSON><PERSON>r,
  Legend,
  Tooltip,
} from 'recharts';
import { PieChartConfig } from '../../../lib/types/analytics';

interface PieChartProps {
  data: Array<{
    label: string;
    value: number;
    percentage: number;
    color: string;
  }>;
  config?: PieChartConfig;
  width?: number;
  height?: number;
}

const PieChart: React.FC<PieChartProps> = ({
  data,
  config = {},
  width = 400,
  height = 300,
}) => {
  const {
    show_legend = true,
    show_labels = true,
    inner_radius = 0,
    outer_radius = 80,
  } = config;

  const renderCustomLabel = (entry: any) => {
    if (!show_labels) return null;
    return `${entry.label}: ${entry.percentage}%`;
  };

  const renderTooltip = (props: any) => {
    if (!props.active || !props.payload || !props.payload[0]) return null;
    
    const data = props.payload[0].payload;
    return (
      <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
        <p className="font-medium text-gray-900">{data.label}</p>
        <p className="text-sm text-gray-600">
          Value: <span className="font-medium">{data.value.toLocaleString()}</span>
        </p>
        <p className="text-sm text-gray-600">
          Percentage: <span className="font-medium">{data.percentage}%</span>
        </p>
      </div>
    );
  };

  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-full">
        <p className="text-gray-500">No data available</p>
      </div>
    );
  }

  return (
    <ResponsiveContainer width="100%" height={height}>
      <RechartsPieChart>
        <Pie
          data={data}
          cx="50%"
          cy="50%"
          labelLine={false}
          label={show_labels ? renderCustomLabel : false}
          outerRadius={outer_radius}
          innerRadius={inner_radius}
          fill="#8884d8"
          dataKey="value"
        >
          {data.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={entry.color} />
          ))}
        </Pie>
        <Tooltip content={renderTooltip} />
        {show_legend && (
          <Legend
            verticalAlign="bottom"
            height={36}
            formatter={(value, entry) => (
              <span style={{ color: entry.color }}>{value}</span>
            )}
          />
        )}
      </RechartsPieChart>
    </ResponsiveContainer>
  );
};

export default PieChart;
