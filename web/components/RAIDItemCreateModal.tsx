'use client';
import * as React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useToast } from '@/components/ui/toast';

interface RAIDItemCreateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onItemCreated: (item: any) => void;
  projectId: string;
  category: 'risks' | 'assumptions' | 'issues' | 'dependencies' | 'actions' | 'decisions';
}

export default function RAIDItemCreateModal({ 
  isOpen, 
  onClose, 
  onItemCreated, 
  projectId, 
  category 
}: RAIDItemCreateModalProps) {
  const [formData, setFormData] = React.useState({
    title: '',
    description: '',
    owner: '',
    priority: 'Medium',
    status: 'Open',
    dueAt: '',
    impact: 1,
    likelihood: 1,
    severity: 'Low',
    rationale: '',
    decidedBy: '',
    relatedRiskId: '',
  });
  const [loading, setLoading] = React.useState(false);
  const { addToast } = useToast();

  const priorityOptions = ['Low', 'Medium', 'High', 'Critical'];
  const impactOptions = ['Low', 'Medium', 'High', 'Critical'];
  const severityOptions = ['Low', 'Med', 'High', 'Critical'];
  const statusOptions = ['Open', 'In Progress', 'Resolved', 'Closed'];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const endpoint = getEndpoint(category);
      const payload = getPayload(category);
      
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error(`Failed to create ${category}`);
      }

      const item = await response.json();
      addToast({
        variant: 'success',
        title: `${category.charAt(0).toUpperCase() + category.slice(1)} Created!`,
        description: `"${item.title}" has been created successfully.`,
      });
      
      onItemCreated(item);
      onClose();
      resetForm();
    } catch (error) {
      addToast({
        variant: 'error',
        title: 'Error',
        description: `Failed to create ${category}. Please try again.`,
      });
    } finally {
      setLoading(false);
    }
  };

  const getEndpoint = (category: string) => {
    switch (category) {
      case 'risks':
        return `/api/projects/${projectId}/risks`;
      case 'issues':
        return `/api/projects/${projectId}/issues`;
      case 'actions':
        return `/api/projects/${projectId}/actions`;
      case 'decisions':
        return `/api/projects/${projectId}/decisions`;
      default:
        return `/api/projects/${projectId}/raid`;
    }
  };

  const getPayload = (category: string) => {
    const basePayload = {
      title: formData.title,
      description: formData.description,
      owner: formData.owner || undefined,
      status: formData.status,
      dueAt: formData.dueAt || undefined,
    };

    switch (category) {
      case 'risks':
        return {
          ...basePayload,
          likelihood: Number(formData.likelihood),
          impact: Number(formData.impact),
          trigger: formData.description,
          response: '',
        };
      case 'issues':
        return {
          ...basePayload,
          severity: formData.severity,
        };
      case 'actions':
        return {
          ...basePayload,
          relatedId: formData.relatedRiskId || undefined,
        };
      case 'decisions':
        return {
          ...basePayload,
          decidedBy: formData.decidedBy || undefined,
          rationale: formData.rationale || undefined,
        };
      default:
        return basePayload;
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      owner: '',
      priority: 'Medium',
      status: 'Open',
      dueAt: '',
      impact: 1,
      likelihood: 1,
      severity: 'Low',
      rationale: '',
      decidedBy: '',
      relatedRiskId: '',
    });
  };

  const getCategoryIcon = (category: string) => {
    const icons = {
      risks: '⚠️',
      assumptions: '💭',
      issues: '🚨',
      dependencies: '🔗',
      actions: '✅',
      decisions: '📋',
    };
    return icons[category as keyof typeof icons] || '📝';
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-lg mx-4 max-h-[90vh] overflow-y-auto">
        <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
          {getCategoryIcon(category)} Create New {category.charAt(0).toUpperCase() + category.slice(1)}
        </h2>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">Title *</label>
            <Input
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              placeholder={`Enter ${category} title`}
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Description</label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder={`Describe this ${category}`}
              className="w-full p-2 border border-gray-300 rounded-md"
              rows={3}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Owner</label>
              <Input
                value={formData.owner}
                onChange={(e) => setFormData(prev => ({ ...prev, owner: e.target.value }))}
                placeholder="Assign to..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Status</label>
              <select
                value={formData.status}
                onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value }))}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                {statusOptions.map(status => (
                  <option key={status} value={status}>{status}</option>
                ))}
              </select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Priority</label>
              <select
                value={formData.priority}
                onChange={(e) => setFormData(prev => ({ ...prev, priority: e.target.value }))}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                {priorityOptions.map(priority => (
                  <option key={priority} value={priority}>{priority}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Due Date</label>
              <Input
                type="date"
                value={formData.dueAt}
                onChange={(e) => setFormData(prev => ({ ...prev, dueAt: e.target.value }))}
              />
            </div>
          </div>

          {/* Risk-specific fields */}
          {category === 'risks' && (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">Likelihood (1-5)</label>
                <Input
                  type="number"
                  min="1"
                  max="5"
                  value={formData.likelihood}
                  onChange={(e) => setFormData(prev => ({ ...prev, likelihood: Number(e.target.value) }))}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Impact (1-5)</label>
                <Input
                  type="number"
                  min="1"
                  max="5"
                  value={formData.impact}
                  onChange={(e) => setFormData(prev => ({ ...prev, impact: Number(e.target.value) }))}
                />
              </div>
            </div>
          )}

          {/* Issue-specific fields */}
          {category === 'issues' && (
            <div>
              <label className="block text-sm font-medium mb-1">Severity</label>
              <select
                value={formData.severity}
                onChange={(e) => setFormData(prev => ({ ...prev, severity: e.target.value }))}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                {severityOptions.map(severity => (
                  <option key={severity} value={severity}>{severity}</option>
                ))}
              </select>
            </div>
          )}

          {/* Decision-specific fields */}
          {category === 'decisions' && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Decided By</label>
                <Input
                  value={formData.decidedBy}
                  onChange={(e) => setFormData(prev => ({ ...prev, decidedBy: e.target.value }))}
                  placeholder="Who made this decision?"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Rationale</label>
                <textarea
                  value={formData.rationale}
                  onChange={(e) => setFormData(prev => ({ ...prev, rationale: e.target.value }))}
                  placeholder="Why was this decision made?"
                  className="w-full p-2 border border-gray-300 rounded-md"
                  rows={2}
                />
              </div>
            </div>
          )}

          {/* Action-specific fields */}
          {category === 'actions' && (
            <div>
              <label className="block text-sm font-medium mb-1">Related Risk ID (optional)</label>
              <Input
                value={formData.relatedRiskId}
                onChange={(e) => setFormData(prev => ({ ...prev, relatedRiskId: e.target.value }))}
                placeholder="Link to a specific risk"
              />
            </div>
          )}

          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading || !formData.title.trim()}>
              {loading ? 'Creating...' : `Create ${category.charAt(0).toUpperCase() + category.slice(1)}`}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
