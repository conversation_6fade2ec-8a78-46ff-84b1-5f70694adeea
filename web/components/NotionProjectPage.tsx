'use client';
import * as React from 'react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/toast';
import NotionTable from './NotionTable';
import RAIDItemCreateModal from './RAIDItemCreateModal';

interface Project {
  id: string;
  name: string;
  cadence: string;
  categories: string[];
  createdAt: string;
  updatedAt: string;
}

interface RAIDItem {
  id: string;
  title: string;
  description?: string;
  owner?: string;
  status: string;
  priority: string;
  dueAt?: string;
  score?: number;
  severity?: string;
  impact?: string;
  probability?: number;
  rationale?: string;
  decidedBy?: string;
  createdAt: string;
  updatedAt: string;
}

interface NotionProjectPageProps {
  project: Project;
  onProjectUpdate?: (project: Project) => void;
}

export default function NotionProjectPage({ project, onProjectUpdate }: NotionProjectPageProps) {
  const [raidItems, setRaidItems] = React.useState<{
    risks: RAIDItem[];
    issues: RAIDItem[];
    actions: RAIDItem[];
    decisions: RAIDItem[];
  }>({
    risks: [],
    issues: [],
    actions: [],
    decisions: []
  });
  const [loading, setLoading] = React.useState(true);
  const [showCreateModal, setShowCreateModal] = React.useState(false);
  const [selectedCategory, setSelectedCategory] = React.useState<string>('risks');
  const { addToast } = useToast();

  React.useEffect(() => {
    loadRAIDItems();
  }, [project.id]);

  const loadRAIDItems = async () => {
    try {
      const [risks, issues, actions, decisions] = await Promise.all([
        fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5050'}/projects/${project.id}/raid?category=risks`).then(r => r.json()),
        fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5050'}/projects/${project.id}/raid?category=issues`).then(r => r.json()),
        fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5050'}/projects/${project.id}/raid?category=actions`).then(r => r.json()),
        fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5050'}/projects/${project.id}/raid?category=decisions`).then(r => r.json()),
      ]);

      setRaidItems({
        risks: risks.map((item: any) => ({ ...item, priority: getPriorityFromScore(item.score) })),
        issues: issues.map((item: any) => ({ ...item, priority: getPriorityFromSeverity(item.severity) })),
        actions: actions.map((item: any) => ({ ...item, priority: 'Medium' })),
        decisions: decisions.map((item: any) => ({ ...item, priority: 'Medium' })),
      });
    } catch (error) {
      console.error('Error loading RAID items:', error);
      addToast({
        variant: 'error',
        title: 'Error',
        description: 'Failed to load RAID items',
      });
    } finally {
      setLoading(false);
    }
  };

  const getPriorityFromScore = (score: number) => {
    if (score >= 4) return 'Critical';
    if (score >= 3) return 'High';
    if (score >= 2) return 'Medium';
    return 'Low';
  };

  const getPriorityFromSeverity = (severity: string) => {
    const severityMap = { 'Critical': 'Critical', 'High': 'High', 'Med': 'Medium', 'Low': 'Low' };
    return severityMap[severity as keyof typeof severityMap] || 'Medium';
  };

  const handleItemCreated = (item: any) => {
    const newItem = { ...item, priority: 'Medium' };
    setRaidItems(prev => ({
      ...prev,
      [selectedCategory]: [...prev[selectedCategory as keyof typeof prev], newItem]
    }));
    addToast({
      variant: 'success',
      title: 'Item Created!',
      description: `${selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1)} created successfully.`,
    });
  };

  const handleItemUpdate = async (id: string, field: string, value: any) => {
    try {
      // Determine the category based on the item ID
      let category = 'risks';
      for (const [cat, items] of Object.entries(raidItems)) {
        if (items.find(item => item.id === id)) {
          category = cat;
          break;
        }
      }

      const endpoint = getUpdateEndpoint(category, id);
      const payload = { [field]: value };

      const response = await fetch(endpoint, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });

      if (response.ok) {
        // Update local state
        setRaidItems(prev => ({
          ...prev,
          [category]: prev[category as keyof typeof prev].map(item =>
            item.id === id ? { ...item, [field]: value } : item
          )
        }));

        addToast({
          variant: 'success',
          title: 'Updated!',
          description: 'Item updated successfully.',
        });
      }
    } catch (error) {
      addToast({
        variant: 'error',
        title: 'Error',
        description: 'Failed to update item.',
      });
    }
  };

  const getUpdateEndpoint = (category: string, id: string) => {
    switch (category) {
      case 'risks':
        return `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5050'}/risks/${id}`;
      case 'issues':
        return `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5050'}/issues/${id}`;
      case 'actions':
        return `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5050'}/actions/${id}`;
      case 'decisions':
        return `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5050'}/decisions/${id}`;
      default:
        return '';
    }
  };

  const handleItemDelete = async (id: string) => {
    // For now, just remove from local state
    // In a real app, you'd make an API call to delete
    setRaidItems(prev => {
      const newState = { ...prev };
      for (const category in newState) {
        newState[category as keyof typeof newState] = newState[category as keyof typeof newState].filter(
          item => item.id !== id
        );
      }
      return newState;
    });

    addToast({
      variant: 'success',
      title: 'Deleted!',
      description: 'Item deleted successfully.',
    });
  };

  const getTableColumns = (category: string): any[] => {
    const baseColumns = [
      { key: 'title', label: 'Title', type: 'text' as const, width: '30%', sortable: true, filterable: true },
      { key: 'owner', label: 'Owner', type: 'text' as const, width: '15%', sortable: true, filterable: true },
      { key: 'status', label: 'Status', type: 'status' as const, width: '12%', sortable: true, filterable: true, options: ['Open', 'In Progress', 'Resolved', 'Closed'] },
      { key: 'priority', label: 'Priority', type: 'priority' as const, width: '12%', sortable: true, filterable: true, options: ['Critical', 'High', 'Medium', 'Low'] },
      { key: 'dueAt', label: 'Due Date', type: 'date' as const, width: '15%', sortable: true, filterable: true },
    ];

    switch (category) {
      case 'risks':
        return [
          ...baseColumns,
          { key: 'score', label: 'Score', type: 'number' as const, width: '8%', sortable: true, filterable: true },
        ];
      case 'issues':
        return [
          ...baseColumns,
          { key: 'severity', label: 'Severity', type: 'select' as const, width: '10%', options: ['Low', 'Med', 'High', 'Critical'], sortable: true, filterable: true },
        ];
      case 'actions':
        return baseColumns;
      case 'decisions':
        return [
          { key: 'title', label: 'Title', type: 'text' as const, width: '25%', sortable: true, filterable: true },
          { key: 'decidedBy', label: 'Decided By', type: 'text' as const, width: '15%', sortable: true, filterable: true },
          { key: 'rationale', label: 'Rationale', type: 'text' as const, width: '40%', sortable: true, filterable: true },
          { key: 'createdAt', label: 'Date', type: 'date' as const, width: '20%', sortable: true, filterable: true },
        ];
      default:
        return baseColumns;
    }
  };

  const getTableData = (category: string) => {
    return raidItems[category as keyof typeof raidItems] || [];
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading project data...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Project Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{project.name}</h1>
            <p className="text-sm text-gray-500 capitalize">{project.cadence} review • {project.categories.length} categories</p>
          </div>
        </div>
      </div>

      {/* Project Content */}
      <div className="p-6 space-y-8">
        {/* Risks Table */}
        <NotionTable
          title="Risks"
          columns={getTableColumns('risks')}
          data={getTableData('risks')}
          onEditRow={handleItemUpdate}
          onDeleteRow={handleItemDelete}
          onAddRow={() => {
            setSelectedCategory('risks');
            setShowCreateModal(true);
          }}
          addButtonText="Add Risk"
          emptyMessage="No risks identified yet"
        />

        {/* Issues Table */}
        <NotionTable
          title="Issues"
          columns={getTableColumns('issues')}
          data={getTableData('issues')}
          onEditRow={handleItemUpdate}
          onDeleteRow={handleItemDelete}
          onAddRow={() => {
            setSelectedCategory('issues');
            setShowCreateModal(true);
          }}
          addButtonText="Add Issue"
          emptyMessage="No issues reported yet"
        />

        {/* Actions Table */}
        <NotionTable
          title="Actions"
          columns={getTableColumns('actions')}
          data={getTableData('actions')}
          onEditRow={handleItemUpdate}
          onDeleteRow={handleItemDelete}
          onAddRow={() => {
            setSelectedCategory('actions');
            setShowCreateModal(true);
          }}
          addButtonText="Add Action"
          emptyMessage="No actions planned yet"
        />

        {/* Decisions Table */}
        <NotionTable
          title="Decisions"
          columns={getTableColumns('decisions')}
          data={getTableData('decisions')}
          onEditRow={handleItemUpdate}
          onDeleteRow={handleItemDelete}
          onAddRow={() => {
            setSelectedCategory('decisions');
            setShowCreateModal(true);
          }}
          addButtonText="Add Decision"
          emptyMessage="No decisions recorded yet"
        />
      </div>

      {/* Create Modal */}
      <RAIDItemCreateModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onItemCreated={handleItemCreated}
        projectId={project.id}
        category={selectedCategory as any}
      />
    </div>
  );
}
