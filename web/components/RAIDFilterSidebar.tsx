'use client';
import * as React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { X, Filter, Calendar, User, AlertTriangle, CheckCircle, Clock, XCircle, Star, Target, FileText } from 'lucide-react';

interface FilterState {
  search: string;
  status: string[];
  priority: string[];
  owner: string[];
  category: string[];
  severity: string[];
  assignee: string[];
  decidedBy: string[];
  scoreRange: [number, number];
  progressRange: [number, number];
  dateRange: {
    created_start?: string;
    created_end?: string;
    updated_start?: string;
    updated_end?: string;
    due_start?: string;
    due_end?: string;
  };
  searchMode: 'contains' | 'exact' | 'starts_with' | 'ends_with';
  searchFields: string[];
  caseSensitive: boolean;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

interface RAIDFilterSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  filters: FilterState;
  onFiltersChange: (filters: FilterState) => void;
  data: any[];
}

export default function RAIDFilterSidebar({ 
  isOpen, 
  onClose, 
  filters, 
  onFiltersChange, 
  data 
}: RAIDFilterSidebarProps) {
  const [localFilters, setLocalFilters] = React.useState<FilterState>(() => ({
    search: filters?.search || '',
    status: filters?.status || [],
    priority: filters?.priority || [],
    owner: filters?.owner || [],
    category: filters?.category || [],
    severity: filters?.severity || [],
    assignee: filters?.assignee || [],
    decidedBy: filters?.decidedBy || [],
    scoreRange: filters?.scoreRange || [0, 100],
    progressRange: filters?.progressRange || [0, 100],
    dateRange: filters?.dateRange || {},
    searchMode: filters?.searchMode || 'contains',
    searchFields: filters?.searchFields || [],
    caseSensitive: filters?.caseSensitive || false,
    sortBy: filters?.sortBy || 'createdAt',
    sortOrder: filters?.sortOrder || 'desc'
  }));

  // Extract unique values from data for filter options
  const filterOptions = React.useMemo(() => {
    const statuses = Array.from(new Set(data.map(item => item.status).filter(Boolean)));
    const priorities = Array.from(new Set(data.map(item => item.priority).filter(Boolean)));
    const owners = Array.from(new Set(data.map(item => item.owner).filter(Boolean)));
    const severities = Array.from(new Set(data.map(item => item.severity).filter(Boolean)));
    const categories = Array.from(new Set(data.map(item => item.category || item.type).filter(Boolean)));
    
    return {
      statuses: statuses.sort(),
      priorities: priorities.sort(),
      owners: owners.sort(),
      severities: severities.sort(),
      categories: categories.sort()
    };
  }, [data]);

  const handleFilterChange = (key: keyof FilterState, value: any) => {
    if (!localFilters) return;
    const newFilters = { ...localFilters, [key]: value };
    setLocalFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const resetFilters = () => {
    const defaultFilters: FilterState = {
      search: '',
      status: [],
      priority: [],
      owner: [],
      category: [],
      severity: [],
      assignee: [],
      decidedBy: [],
      scoreRange: [0, 100],
      progressRange: [0, 100],
      dateRange: {},
      searchMode: 'contains',
      searchFields: [],
      caseSensitive: false,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    };
    setLocalFilters(defaultFilters);
    onFiltersChange(defaultFilters);
  };

  const FilterSection = ({ 
    title, 
    icon: Icon, 
    children 
  }: { 
    title: string; 
    icon: any; 
    children: React.ReactNode;
  }) => (
    <div className="space-y-3">
      <div className="flex items-center gap-2">
        <Icon className="h-4 w-4 text-gray-600" />
        <h3 className="font-semibold text-gray-900">{title}</h3>
      </div>
      {children}
    </div>
  );

  const CheckboxGroup = ({ 
    options, 
    selected, 
    onChange,
    colorClass = "text-blue-600"
  }: { 
    options: string[]; 
    selected: string[]; 
    onChange: (value: string[]) => void;
    colorClass?: string;
  }) => (
    <div className="space-y-2">
      {options.map((option) => (
        <label key={option} className="flex items-center gap-3 cursor-pointer">
          <input
            type="checkbox"
            checked={selected.includes(option)}
            onChange={(e) => {
              if (e.target.checked) {
                onChange([...selected, option]);
              } else {
                onChange(selected.filter(item => item !== option));
              }
            }}
            className={`w-4 h-4 rounded border-2 border-gray-300 ${colorClass} focus:ring-2 focus:ring-blue-500 focus:ring-2`}
          />
          <span className="text-sm text-gray-700">{option}</span>
        </label>
      ))}
    </div>
  );

  const RangeSlider = ({ 
    label, 
    value, 
    onChange, 
    min = 0, 
    max = 100, 
    step = 1 
  }: {
    label: string;
    value: [number, number];
    onChange: (value: [number, number]) => void;
    min?: number;
    max?: number;
    step?: number;
  }) => {
    const [isDragging, setIsDragging] = React.useState<'min' | 'max' | null>(null);
    const sliderRef = React.useRef<HTMLDivElement>(null);
    const sliderBarRef = React.useRef<HTMLDivElement>(null);
    const valueDisplayRef = React.useRef<HTMLSpanElement>(null);

    const getValueFromPosition = (clientX: number) => {
      if (!sliderRef.current) return min;
      const rect = sliderRef.current.getBoundingClientRect();
      const percentage = Math.max(0, Math.min(1, (clientX - rect.left) / rect.width));
      return min + percentage * (max - min);
    };

    const updateSliderVisuals = (newValue: number, handle: 'min' | 'max') => {
      if (!sliderBarRef.current || !valueDisplayRef.current) return;
      
      let newMin = value[0];
      let newMax = value[1];
      
      if (handle === 'min') {
        newMin = Math.max(min, Math.min(newValue, value[1] - step));
      } else {
        newMax = Math.min(max, Math.max(newValue, value[0] + step));
      }
      
      // Update slider bar position directly
      const leftPercent = ((newMin - min) / (max - min)) * 100;
      const widthPercent = ((newMax - newMin) / (max - min)) * 100;
      
      sliderBarRef.current.style.left = `${leftPercent}%`;
      sliderBarRef.current.style.width = `${widthPercent}%`;
      
      // Update value display directly
      valueDisplayRef.current.textContent = `${Math.round(newMin)} - ${Math.round(newMax)}`;
    };

    const handleMouseDown = (e: React.MouseEvent, handle: 'min' | 'max') => {
      e.preventDefault();
      e.stopPropagation();
      setIsDragging(handle);
    };

    const handleMouseMove = React.useCallback((e: MouseEvent) => {
      if (!isDragging) return;
      
      const newValue = getValueFromPosition(e.clientX);
      updateSliderVisuals(newValue, isDragging);
    }, [isDragging, value, min, max, step]);

    const handleMouseUp = React.useCallback((e: MouseEvent) => {
      if (isDragging) {
        // Calculate final value from current mouse position
        const finalValue = getValueFromPosition(e.clientX);
        const roundedValue = Math.round(finalValue / step) * step;
        
        if (isDragging === 'min') {
          const clampedValue = Math.max(min, Math.min(roundedValue, value[1] - step));
          onChange([clampedValue, value[1]]);
        } else {
          const clampedValue = Math.min(max, Math.max(roundedValue, value[0] + step));
          onChange([value[0], clampedValue]);
        }
      }
      setIsDragging(null);
    }, [isDragging, value, min, max, step, onChange]);

    React.useEffect(() => {
      if (isDragging) {
        document.addEventListener('mousemove', handleMouseMove, { passive: true });
        document.addEventListener('mouseup', handleMouseUp, { passive: true });
        document.body.style.userSelect = 'none'; // Prevent text selection during drag
        return () => {
          document.removeEventListener('mousemove', handleMouseMove);
          document.removeEventListener('mouseup', handleMouseUp);
          document.body.style.userSelect = '';
        };
      }
    }, [isDragging, handleMouseMove, handleMouseUp]);

    return (
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <label className="text-sm font-medium text-gray-700">{label}</label>
          <span ref={valueDisplayRef} className="text-sm text-gray-600">
            {value[0]} - {value[1]}
          </span>
        </div>
        <div className="relative" ref={sliderRef}>
          <div 
            className={`h-2 bg-gray-200 rounded-lg cursor-pointer transition-colors ${isDragging ? 'bg-gray-300' : ''}`}
            onClick={(e) => {
              if (!isDragging) {
                const newValue = getValueFromPosition(e.clientX);
                const currentRange = value[1] - value[0];
                const midPoint = value[0] + currentRange / 2;
                
                if (newValue < midPoint) {
                  // Click closer to min handle
                  const clampedValue = Math.max(min, Math.min(newValue, value[1] - step));
                  const roundedValue = Math.round(clampedValue / step) * step;
                  onChange([roundedValue, value[1]]);
                } else {
                  // Click closer to max handle
                  const clampedValue = Math.min(max, Math.max(newValue, value[0] + step));
                  const roundedValue = Math.round(clampedValue / step) * step;
                  onChange([value[0], roundedValue]);
                }
              }
            }}
          >
            <div 
              ref={sliderBarRef}
              className={`h-2 bg-blue-500 rounded-lg relative transition-all duration-150 ${isDragging ? 'bg-blue-600' : ''}`}
              style={{
                left: `${((value[0] - min) / (max - min)) * 100}%`,
                width: `${((value[1] - value[0]) / (max - min)) * 100}%`
              }}
            >
              <div 
                className={`absolute left-0 top-1/2 transform -translate-y-1/2 w-5 h-5 bg-blue-500 rounded-full border-2 border-white shadow-lg cursor-pointer hover:bg-blue-600 transition-all duration-150 z-10 ${isDragging === 'min' ? 'scale-110 shadow-xl' : ''}`}
                onMouseDown={(e) => handleMouseDown(e, 'min')}
                style={{ marginLeft: '-2px' }}
              ></div>
              <div 
                className={`absolute right-0 top-1/2 transform -translate-y-1/2 w-5 h-5 bg-blue-500 rounded-full border-2 border-white shadow-lg cursor-pointer hover:bg-blue-600 transition-all duration-150 z-10 ${isDragging === 'max' ? 'scale-110 shadow-xl' : ''}`}
                onMouseDown={(e) => handleMouseDown(e, 'max')}
                style={{ marginRight: '-2px' }}
              ></div>
            </div>
          </div>
        </div>
        <div className="flex gap-2">
          <div className="flex-1">
            <Input
              type="number"
              value={value[0]}
              onChange={(e) => onChange([parseInt(e.target.value) || min, value[1]])}
              min={min}
              max={max}
              step={step}
              className="text-sm"
            />
          </div>
          <div className="flex-1">
            <Input
              type="number"
              value={value[1]}
              onChange={(e) => onChange([value[0], parseInt(e.target.value) || max])}
              min={min}
              max={max}
              step={step}
              className="text-sm"
            />
          </div>
        </div>
      </div>
    );
  };

  const getActiveFilterCount = () => {
    let count = 0;
    if (localFilters?.search) count++;
    if (localFilters?.status?.length > 0) count++;
    if (localFilters?.priority?.length > 0) count++;
    if (localFilters?.owner?.length > 0) count++;
    if (localFilters?.category?.length > 0) count++;
    if (localFilters?.severity?.length > 0) count++;
    if (localFilters?.scoreRange?.[0] > 1 || localFilters?.scoreRange?.[1] < 25) count++;
    if (localFilters?.dateRange?.created_start || localFilters?.dateRange?.created_end ||
        localFilters?.dateRange?.updated_start || localFilters?.dateRange?.updated_end ||
        localFilters?.dateRange?.due_start || localFilters?.dateRange?.due_end) count++;
    return count;
  };

  const activeFilterCount = getActiveFilterCount();

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-40 lg:relative lg:inset-auto lg:z-auto">
      {/* Mobile overlay */}
      <div className="fixed inset-0 bg-black bg-opacity-50 lg:hidden" onClick={onClose}></div>
      
      {/* Sidebar */}
      <div className="fixed right-0 top-0 h-full w-80 bg-white shadow-xl lg:relative lg:shadow-none lg:w-80 lg:rounded-l-2xl lg:border-r lg:border-gray-200">
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center gap-2">
              <Filter className="h-5 w-5 text-gray-600" />
              <h2 className="text-lg font-semibold text-gray-900">Filters</h2>
              {activeFilterCount > 0 && (
                <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                  {activeFilterCount}
                </Badge>
              )}
            </div>
            <Button variant="ghost" size="sm" onClick={onClose} className="lg:hidden text-gray-500 hover:text-gray-700">
              <X className="h-5 w-5" />
            </Button>
          </div>

          {/* Filter Content */}
          <div className="flex-1 overflow-y-auto p-6 space-y-8">
            {/* Search */}
            <FilterSection title="Search" icon={FileText}>
              <Input
                placeholder="Search items..."
                value={localFilters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="w-full"
              />
            </FilterSection>

            {/* Category */}
            <FilterSection title="Category" icon={Target}>
              <CheckboxGroup
                options={filterOptions.categories}
                selected={localFilters.category}
                onChange={(value) => handleFilterChange('category', value)}
              />
            </FilterSection>

            {/* Status */}
            <FilterSection title="Status" icon={Clock}>
              <CheckboxGroup
                options={filterOptions.statuses}
                selected={localFilters.status}
                onChange={(value) => handleFilterChange('status', value)}
              />
            </FilterSection>

            {/* Priority */}
            <FilterSection title="Priority" icon={Star}>
              <CheckboxGroup
                options={filterOptions.priorities}
                selected={localFilters.priority}
                onChange={(value) => handleFilterChange('priority', value)}
              />
            </FilterSection>

            {/* Owner */}
            <FilterSection title="Owner" icon={User}>
              <CheckboxGroup
                options={filterOptions.owners}
                selected={localFilters.owner}
                onChange={(value) => handleFilterChange('owner', value)}
              />
            </FilterSection>

            {/* Severity (for Issues) */}
            {filterOptions.severities.length > 0 && (
              <FilterSection title="Severity" icon={AlertTriangle}>
                <CheckboxGroup
                  options={filterOptions.severities}
                  selected={localFilters.severity}
                  onChange={(value) => handleFilterChange('severity', value)}
                />
              </FilterSection>
            )}

            {/* Score Range (for Risks) */}
            <FilterSection title="Risk Score" icon={Target}>
              <RangeSlider
                label="Score Range"
                value={localFilters.scoreRange}
                onChange={(value) => handleFilterChange('scoreRange', value)}
                min={1}
                max={25}
                step={1}
              />
            </FilterSection>


            {/* Date Range */}
            <FilterSection title="Date Range" icon={Calendar}>
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Created Start</label>
                  <Input
                    type="date"
                    value={localFilters.dateRange.created_start || ''}
                    onChange={(e) => handleFilterChange('dateRange', {
                      ...localFilters.dateRange,
                      created_start: e.target.value
                    })}
                    className="w-full"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Created End</label>
                  <Input
                    type="date"
                    value={localFilters.dateRange.created_end || ''}
                    onChange={(e) => handleFilterChange('dateRange', {
                      ...localFilters.dateRange,
                      created_end: e.target.value
                    })}
                    className="w-full"
                  />
                </div>
              </div>
            </FilterSection>
          </div>

          {/* Footer */}
          <div className="p-6 border-t border-gray-200">
            <Button 
              onClick={resetFilters} 
              variant="outline" 
              className="w-full"
              disabled={activeFilterCount === 0}
            >
              Clear all filters
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

// Desktop sidebar component
export function DesktopRAIDFilterSidebar({ 
  filters, 
  onFiltersChange, 
  data 
}: { 
  filters: FilterState; 
  onFiltersChange: (filters: FilterState) => void; 
  data: any[];
}) {
  const [localFilters, setLocalFilters] = React.useState<FilterState>(() => ({
    search: filters?.search || '',
    status: filters?.status || [],
    priority: filters?.priority || [],
    owner: filters?.owner || [],
    category: filters?.category || [],
    severity: filters?.severity || [],
    assignee: filters?.assignee || [],
    decidedBy: filters?.decidedBy || [],
    scoreRange: filters?.scoreRange || [0, 100],
    progressRange: filters?.progressRange || [0, 100],
    dateRange: filters?.dateRange || {},
    searchMode: filters?.searchMode || 'contains',
    searchFields: filters?.searchFields || [],
    caseSensitive: filters?.caseSensitive || false,
    sortBy: filters?.sortBy || 'createdAt',
    sortOrder: filters?.sortOrder || 'desc'
  }));

  // Extract unique values from data for filter options
  const filterOptions = React.useMemo(() => {
    const statuses = Array.from(new Set(data.map(item => item.status).filter(Boolean)));
    const priorities = Array.from(new Set(data.map(item => item.priority).filter(Boolean)));
    const owners = Array.from(new Set(data.map(item => item.owner).filter(Boolean)));
    const severities = Array.from(new Set(data.map(item => item.severity).filter(Boolean)));
    const categories = Array.from(new Set(data.map(item => item.category || item.type).filter(Boolean)));
    
    return {
      statuses: statuses.sort(),
      priorities: priorities.sort(),
      owners: owners.sort(),
      severities: severities.sort(),
      categories: categories.sort()
    };
  }, [data]);

  const handleFilterChange = (key: keyof FilterState, value: any) => {
    if (!localFilters) return;
    const newFilters = { ...localFilters, [key]: value };
    setLocalFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const resetFilters = () => {
    const defaultFilters: FilterState = {
      search: '',
      status: [],
      priority: [],
      owner: [],
      category: [],
      severity: [],
      assignee: [],
      decidedBy: [],
      scoreRange: [0, 100],
      progressRange: [0, 100],
      dateRange: {},
      searchMode: 'contains',
      searchFields: [],
      caseSensitive: false,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    };
    setLocalFilters(defaultFilters);
    onFiltersChange(defaultFilters);
  };

  const FilterSection = ({ 
    title, 
    icon: Icon, 
    children 
  }: { 
    title: string; 
    icon: any; 
    children: React.ReactNode;
  }) => (
    <div className="space-y-3">
      <div className="flex items-center gap-2">
        <Icon className="h-4 w-4 text-gray-600" />
        <h3 className="font-semibold text-gray-900">{title}</h3>
      </div>
      {children}
    </div>
  );

  const CheckboxGroup = ({ 
    options, 
    selected, 
    onChange 
  }: { 
    options: string[]; 
    selected: string[]; 
    onChange: (value: string[]) => void;
  }) => (
    <div className="space-y-2">
      {options.map((option) => (
        <label key={option} className="flex items-center gap-3 cursor-pointer">
          <input
            type="checkbox"
            checked={selected.includes(option)}
            onChange={(e) => {
              if (e.target.checked) {
                onChange([...selected, option]);
              } else {
                onChange(selected.filter(item => item !== option));
              }
            }}
            className="w-4 h-4 rounded border-2 border-gray-300 text-blue-600 focus:ring-2 focus:ring-blue-500 focus:ring-2"
          />
          <span className="text-sm text-gray-700">{option}</span>
        </label>
      ))}
    </div>
  );

  const RangeSlider = ({ 
    label, 
    value, 
    onChange, 
    min = 0, 
    max = 100, 
    step = 1 
  }: {
    label: string;
    value: [number, number];
    onChange: (value: [number, number]) => void;
    min?: number;
    max?: number;
    step?: number;
  }) => {
    const [isDragging, setIsDragging] = React.useState<'min' | 'max' | null>(null);
    const sliderRef = React.useRef<HTMLDivElement>(null);
    const sliderBarRef = React.useRef<HTMLDivElement>(null);
    const valueDisplayRef = React.useRef<HTMLSpanElement>(null);

    const getValueFromPosition = (clientX: number) => {
      if (!sliderRef.current) return min;
      const rect = sliderRef.current.getBoundingClientRect();
      const percentage = Math.max(0, Math.min(1, (clientX - rect.left) / rect.width));
      return min + percentage * (max - min);
    };

    const updateSliderVisuals = (newValue: number, handle: 'min' | 'max') => {
      if (!sliderBarRef.current || !valueDisplayRef.current) return;
      
      let newMin = value[0];
      let newMax = value[1];
      
      if (handle === 'min') {
        newMin = Math.max(min, Math.min(newValue, value[1] - step));
      } else {
        newMax = Math.min(max, Math.max(newValue, value[0] + step));
      }
      
      // Update slider bar position directly
      const leftPercent = ((newMin - min) / (max - min)) * 100;
      const widthPercent = ((newMax - newMin) / (max - min)) * 100;
      
      sliderBarRef.current.style.left = `${leftPercent}%`;
      sliderBarRef.current.style.width = `${widthPercent}%`;
      
      // Update value display directly
      valueDisplayRef.current.textContent = `${Math.round(newMin)} - ${Math.round(newMax)}`;
    };

    const handleMouseDown = (e: React.MouseEvent, handle: 'min' | 'max') => {
      e.preventDefault();
      e.stopPropagation();
      setIsDragging(handle);
    };

    const handleMouseMove = React.useCallback((e: MouseEvent) => {
      if (!isDragging) return;
      
      const newValue = getValueFromPosition(e.clientX);
      updateSliderVisuals(newValue, isDragging);
    }, [isDragging, value, min, max, step]);

    const handleMouseUp = React.useCallback((e: MouseEvent) => {
      if (isDragging) {
        // Calculate final value from current mouse position
        const finalValue = getValueFromPosition(e.clientX);
        const roundedValue = Math.round(finalValue / step) * step;
        
        if (isDragging === 'min') {
          const clampedValue = Math.max(min, Math.min(roundedValue, value[1] - step));
          onChange([clampedValue, value[1]]);
        } else {
          const clampedValue = Math.min(max, Math.max(roundedValue, value[0] + step));
          onChange([value[0], clampedValue]);
        }
      }
      setIsDragging(null);
    }, [isDragging, value, min, max, step, onChange]);

    React.useEffect(() => {
      if (isDragging) {
        document.addEventListener('mousemove', handleMouseMove, { passive: true });
        document.addEventListener('mouseup', handleMouseUp, { passive: true });
        document.body.style.userSelect = 'none'; // Prevent text selection during drag
        return () => {
          document.removeEventListener('mousemove', handleMouseMove);
          document.removeEventListener('mouseup', handleMouseUp);
          document.body.style.userSelect = '';
        };
      }
    }, [isDragging, handleMouseMove, handleMouseUp]);

    return (
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <label className="text-sm font-medium text-gray-700">{label}</label>
          <span ref={valueDisplayRef} className="text-sm text-gray-600">
            {value[0]} - {value[1]}
          </span>
        </div>
        <div className="relative" ref={sliderRef}>
          <div 
            className={`h-2 bg-gray-200 rounded-lg cursor-pointer transition-colors ${isDragging ? 'bg-gray-300' : ''}`}
            onClick={(e) => {
              if (!isDragging) {
                const newValue = getValueFromPosition(e.clientX);
                const currentRange = value[1] - value[0];
                const midPoint = value[0] + currentRange / 2;
                
                if (newValue < midPoint) {
                  // Click closer to min handle
                  const clampedValue = Math.max(min, Math.min(newValue, value[1] - step));
                  const roundedValue = Math.round(clampedValue / step) * step;
                  onChange([roundedValue, value[1]]);
                } else {
                  // Click closer to max handle
                  const clampedValue = Math.min(max, Math.max(newValue, value[0] + step));
                  const roundedValue = Math.round(clampedValue / step) * step;
                  onChange([value[0], roundedValue]);
                }
              }
            }}
          >
            <div 
              ref={sliderBarRef}
              className={`h-2 bg-blue-500 rounded-lg relative transition-all duration-150 ${isDragging ? 'bg-blue-600' : ''}`}
              style={{
                left: `${((value[0] - min) / (max - min)) * 100}%`,
                width: `${((value[1] - value[0]) / (max - min)) * 100}%`
              }}
            >
              <div 
                className={`absolute left-0 top-1/2 transform -translate-y-1/2 w-5 h-5 bg-blue-500 rounded-full border-2 border-white shadow-lg cursor-pointer hover:bg-blue-600 transition-all duration-150 z-10 ${isDragging === 'min' ? 'scale-110 shadow-xl' : ''}`}
                onMouseDown={(e) => handleMouseDown(e, 'min')}
                style={{ marginLeft: '-2px' }}
              ></div>
              <div 
                className={`absolute right-0 top-1/2 transform -translate-y-1/2 w-5 h-5 bg-blue-500 rounded-full border-2 border-white shadow-lg cursor-pointer hover:bg-blue-600 transition-all duration-150 z-10 ${isDragging === 'max' ? 'scale-110 shadow-xl' : ''}`}
                onMouseDown={(e) => handleMouseDown(e, 'max')}
                style={{ marginRight: '-2px' }}
              ></div>
            </div>
          </div>
        </div>
        <div className="flex gap-2">
          <div className="flex-1">
            <Input
              type="number"
              value={value[0]}
              onChange={(e) => onChange([parseInt(e.target.value) || min, value[1]])}
              min={min}
              max={max}
              step={step}
              className="text-sm"
            />
          </div>
          <div className="flex-1">
            <Input
              type="number"
              value={value[1]}
              onChange={(e) => onChange([value[0], parseInt(e.target.value) || max])}
              min={min}
              max={max}
              step={step}
              className="text-sm"
            />
          </div>
        </div>
      </div>
    );
  };

  const getActiveFilterCount = () => {
    let count = 0;
    if (localFilters?.search) count++;
    if (localFilters?.status?.length > 0) count++;
    if (localFilters?.priority?.length > 0) count++;
    if (localFilters?.owner?.length > 0) count++;
    if (localFilters?.category?.length > 0) count++;
    if (localFilters?.severity?.length > 0) count++;
    if (localFilters?.scoreRange?.[0] > 1 || localFilters?.scoreRange?.[1] < 25) count++;
    if (localFilters?.dateRange?.created_start || localFilters?.dateRange?.created_end ||
        localFilters?.dateRange?.updated_start || localFilters?.dateRange?.updated_end ||
        localFilters?.dateRange?.due_start || localFilters?.dateRange?.due_end) count++;
    return count;
  };

  const activeFilterCount = getActiveFilterCount();

  return (
    <div className="w-80 bg-white rounded-l-2xl border-r border-gray-200 shadow-lg">
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-2">
            <Filter className="h-5 w-5 text-gray-600" />
            <h2 className="text-lg font-semibold text-gray-900">Filters</h2>
            {activeFilterCount > 0 && (
              <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                {activeFilterCount}
              </Badge>
            )}
          </div>
          <button
            onClick={resetFilters}
            className="text-sm text-blue-600 hover:text-blue-800 font-medium"
            disabled={activeFilterCount === 0}
          >
            Reset all
          </button>
        </div>

        {/* Filter Content */}
        <div className="flex-1 overflow-y-auto p-6 space-y-8">
          {/* Search */}
          <FilterSection title="Search" icon={FileText}>
            <Input
              placeholder="Search items..."
              value={localFilters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="w-full"
            />
          </FilterSection>

          {/* Category */}
          <FilterSection title="Category" icon={Target}>
            <CheckboxGroup
              options={filterOptions.categories}
              selected={localFilters.category}
              onChange={(value) => handleFilterChange('category', value)}
            />
          </FilterSection>

          {/* Status */}
          <FilterSection title="Status" icon={Clock}>
            <CheckboxGroup
              options={filterOptions.statuses}
              selected={localFilters.status}
              onChange={(value) => handleFilterChange('status', value)}
            />
          </FilterSection>

          {/* Priority */}
          <FilterSection title="Priority" icon={Star}>
            <CheckboxGroup
              options={filterOptions.priorities}
              selected={localFilters.priority}
              onChange={(value) => handleFilterChange('priority', value)}
            />
          </FilterSection>

          {/* Owner */}
          <FilterSection title="Owner" icon={User}>
            <CheckboxGroup
              options={filterOptions.owners}
              selected={localFilters.owner}
              onChange={(value) => handleFilterChange('owner', value)}
            />
          </FilterSection>

          {/* Severity (for Issues) */}
          {filterOptions.severities.length > 0 && (
            <FilterSection title="Severity" icon={AlertTriangle}>
              <CheckboxGroup
                options={filterOptions.severities}
                selected={localFilters.severity}
                onChange={(value) => handleFilterChange('severity', value)}
              />
            </FilterSection>
          )}

          {/* Score Range (for Risks) */}
          <FilterSection title="Risk Score" icon={Target}>
            <RangeSlider
              label="Score Range"
              value={localFilters.scoreRange}
              onChange={(value) => handleFilterChange('scoreRange', value)}
              min={1}
              max={25}
              step={1}
            />
          </FilterSection>


          {/* Date Range */}
          <FilterSection title="Date Range" icon={Calendar}>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Created Start</label>
                <Input
                  type="date"
                  value={localFilters.dateRange.created_start || ''}
                  onChange={(e) => handleFilterChange('dateRange', {
                    ...localFilters.dateRange,
                    created_start: e.target.value
                  })}
                  className="w-full"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Created End</label>
                <Input
                  type="date"
                  value={localFilters.dateRange.created_end || ''}
                  onChange={(e) => handleFilterChange('dateRange', {
                    ...localFilters.dateRange,
                    created_end: e.target.value
                  })}
                  className="w-full"
                />
              </div>
            </div>
          </FilterSection>
        </div>
      </div>
    </div>
  );
}
