import * as Sentry from '@sentry/nextjs';

const SENTRY_DSN = process.env.NEXT_PUBLIC_SENTRY_DSN;

if (SENTRY_DSN) {
  Sentry.init({
    dsn: SENTRY_DSN,

    // Performance Monitoring
    tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,

    // Session Replay
    replaysSessionSampleRate: 0.1,
    replaysOnErrorSampleRate: 1.0,

    // Environment
    environment: process.env.NEXT_PUBLIC_APP_ENV || 'development',
    release: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',

    // Error Filtering
    beforeSend(event, hint) {
      // Filter out development errors
      if (process.env.NODE_ENV === 'development') {
        console.log('Sentry Event:', event);
      }

      // Filter out specific errors
      if (event.exception) {
        const error = hint.originalException;
        if (error instanceof Error) {
          // Skip network errors in development
          if (process.env.NODE_ENV === 'development' &&
              error.message.includes('fetch')) {
            return null;
          }

          // Skip specific error types
          if (error.message.includes('ResizeObserver loop limit exceeded') ||
              error.message.includes('Non-Error promise rejection captured')) {
            return null;
          }
        }
      }

      return event;
    },

    // Custom tags
    initialScope: {
      tags: {
        component: 'raid-web',
        feature: 'analytics',
      },
    },
  });
}

// Export Sentry for manual error reporting
export { Sentry };
