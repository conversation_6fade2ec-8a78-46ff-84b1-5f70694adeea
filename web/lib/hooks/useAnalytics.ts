/**
 * React Query hooks for Analytics API
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Dashboard,
  DashboardCreate,
  DashboardUpdate,
  DashboardWidget,
  DashboardWidgetCreate,
  DashboardWidgetUpdate,
  TrendAnalysis,
  TrendAnalysisCreate,
  AnalyticsQuery,
  DashboardFilters,
  WidgetFilters,
  TrendAnalysisFilters,
} from '../types/analytics';
import {
  dashboardApi,
  widgetApi,
  analyticsApi,
  trendApi,
  withErrorHandling,
} from '../api/analytics';

// Dashboard hooks
export const useDashboards = (filters?: DashboardFilters) => {
  return useQuery({
    queryKey: ['dashboards', filters],
    queryFn: () => dashboardApi.getDashboards(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useDashboard = (id: string) => {
  return useQuery({
    queryKey: ['dashboard', id],
    queryFn: () => dashboardApi.getDashboard(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
};

export const useDashboardWidgets = (dashboardId: string) => {
  return useQuery({
    queryKey: ['dashboard-widgets', dashboardId],
    queryFn: () => dashboardApi.getDashboardWidgets(dashboardId),
    enabled: !!dashboardId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

export const useCreateDashboard = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: DashboardCreate) => dashboardApi.createDashboard(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['dashboards'] });
    },
  });
};

export const useUpdateDashboard = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: DashboardUpdate }) =>
      dashboardApi.updateDashboard(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['dashboard', id] });
      queryClient.invalidateQueries({ queryKey: ['dashboards'] });
    },
  });
};

export const useDeleteDashboard = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => dashboardApi.deleteDashboard(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['dashboards'] });
    },
  });
};

// Widget hooks
export const useWidget = (id: string) => {
  return useQuery({
    queryKey: ['widget', id],
    queryFn: () => widgetApi.getWidget(id),
    enabled: !!id,
    staleTime: 2 * 60 * 1000,
  });
};

export const useWidgetData = (id: string, forceRefresh = false) => {
  return useQuery({
    queryKey: ['widget-data', id, forceRefresh],
    queryFn: () => widgetApi.getWidgetData(id, forceRefresh),
    enabled: !!id,
    staleTime: 1 * 60 * 1000, // 1 minute
    refetchInterval: 5 * 60 * 1000, // Auto-refresh every 5 minutes
  });
};

export const useCreateWidget = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: DashboardWidgetCreate) => widgetApi.createWidget(data),
    onSuccess: (widget) => {
      queryClient.invalidateQueries({ 
        queryKey: ['dashboard-widgets', widget.dashboard_id] 
      });
    },
  });
};

export const useUpdateWidget = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: DashboardWidgetUpdate }) =>
      widgetApi.updateWidget(id, data),
    onSuccess: (widget) => {
      queryClient.invalidateQueries({ queryKey: ['widget', widget.id] });
      queryClient.invalidateQueries({ 
        queryKey: ['dashboard-widgets', widget.dashboard_id] 
      });
      queryClient.invalidateQueries({ 
        queryKey: ['widget-data', widget.id] 
      });
    },
  });
};

export const useDeleteWidget = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => widgetApi.deleteWidget(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: ['widget', id] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-widgets'] });
      queryClient.invalidateQueries({ queryKey: ['widget-data', id] });
    },
  });
};

// Analytics hooks
export const useAnalyticsQuery = () => {
  return useMutation({
    mutationFn: (query: AnalyticsQuery) => analyticsApi.executeQuery(query),
  });
};

export const useDashboardSummary = (projectId?: string) => {
  return useQuery({
    queryKey: ['dashboard-summary', projectId],
    queryFn: () => analyticsApi.getDashboardSummary(projectId),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Trend Analysis hooks
export const useTrendAnalyses = (filters?: TrendAnalysisFilters) => {
  return useQuery({
    queryKey: ['trend-analyses', filters],
    queryFn: () => trendApi.getTrendAnalyses(filters),
    staleTime: 5 * 60 * 1000,
  });
};

export const useTrendAnalysis = (id: string) => {
  return useQuery({
    queryKey: ['trend-analysis', id],
    queryFn: () => trendApi.getTrendAnalysis(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
};

export const useCreateTrendAnalysis = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: TrendAnalysisCreate) => trendApi.createTrendAnalysis(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['trend-analyses'] });
    },
  });
};

export const useDeleteTrendAnalysis = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => trendApi.deleteTrendAnalysis(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['trend-analyses'] });
    },
  });
};

// Utility hooks
export const useRefreshWidget = () => {
  const queryClient = useQueryClient();
  
  return (widgetId: string) => {
    queryClient.invalidateQueries({ 
      queryKey: ['widget-data', widgetId] 
    });
  };
};

export const useRefreshDashboard = () => {
  const queryClient = useQueryClient();
  
  return (dashboardId: string) => {
    queryClient.invalidateQueries({ 
      queryKey: ['dashboard-widgets', dashboardId] 
    });
    // Also refresh all widget data for this dashboard
    queryClient.invalidateQueries({ 
      queryKey: ['widget-data'] 
    });
  };
};

// Real-time data hook with polling
export const useRealTimeWidgetData = (
  widgetId: string, 
  intervalMs: number = 30000 // 30 seconds default
) => {
  return useQuery({
    queryKey: ['widget-data-realtime', widgetId],
    queryFn: () => widgetApi.getWidgetData(widgetId, true),
    enabled: !!widgetId,
    refetchInterval: intervalMs,
    refetchIntervalInBackground: true,
    staleTime: 0, // Always consider stale for real-time data
  });
};

// Batch operations
export const useBatchUpdateWidgets = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (updates: Array<{ id: string; data: DashboardWidgetUpdate }>) => {
      const results = await Promise.all(
        updates.map(({ id, data }) => widgetApi.updateWidget(id, data))
      );
      return results;
    },
    onSuccess: (widgets) => {
      // Invalidate all affected queries
      widgets.forEach(widget => {
        queryClient.invalidateQueries({ queryKey: ['widget', widget.id] });
        queryClient.invalidateQueries({ 
          queryKey: ['dashboard-widgets', widget.dashboard_id] 
        });
      });
    },
  });
};

// Error handling wrapper for hooks
export const useAnalyticsWithErrorHandling = <T>(
  queryFn: () => Promise<T>,
  errorMessage: string
) => {
  return useQuery({
    queryFn: () => withErrorHandling(queryFn, errorMessage),
    retry: (failureCount, error) => {
      // Don't retry on 4xx errors
      if (error && typeof error === 'object' && 'status' in error) {
        const status = (error as any).status;
        if (status >= 400 && status < 500) {
          return false;
        }
      }
      return failureCount < 3;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};
