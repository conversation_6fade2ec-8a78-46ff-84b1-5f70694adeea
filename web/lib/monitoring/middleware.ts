import { NextRequest, NextResponse } from 'next/server';
import { Sentry } from '../../sentry.server.config';

export interface MonitoringMiddlewareOptions {
  enablePerformanceTracking?: boolean;
  enableErrorTracking?: boolean;
  enableRequestLogging?: boolean;
  excludePaths?: string[];
  sensitiveHeaders?: string[];
}

const defaultOptions: MonitoringMiddlewareOptions = {
  enablePerformanceTracking: true,
  enableErrorTracking: true,
  enableRequestLogging: true,
  excludePaths: ['/api/health', '/api/monitoring'],
  sensitiveHeaders: ['authorization', 'cookie', 'x-api-key'],
};

/**
 * Monitoring middleware for API routes
 */
export function withMonitoring(
  handler: (req: NextRequest) => Promise<NextResponse>,
  options: MonitoringMiddlewareOptions = {}
) {
  const config = { ...defaultOptions, ...options };

  return async (req: NextRequest): Promise<NextResponse> => {
    const startTime = Date.now();
    const requestId = generateRequestId();
    const path = new URL(req.url).pathname;

    // Skip monitoring for excluded paths
    if (config.excludePaths?.some(excludePath => path.startsWith(excludePath))) {
      return handler(req);
    }

    // Set request context for Sentry
    if (config.enableErrorTracking) {
      Sentry.setTag('requestId', requestId);
      Sentry.setContext('request', {
        method: req.method,
        url: req.url,
        headers: sanitizeHeaders(req.headers, config.sensitiveHeaders),
      });
    }

    try {
      // Log request start
      if (config.enableRequestLogging) {
        console.log(`[${requestId}] ${req.method} ${path} - Started`);
      }

      // Execute the handler
      const response = await handler(req);

      // Calculate response time
      const responseTime = Date.now() - startTime;

      // Log successful response
      if (config.enableRequestLogging) {
        console.log(
          `[${requestId}] ${req.method} ${path} - ${response.status} (${responseTime}ms)`
        );
      }

      // Track performance metrics
      if (config.enablePerformanceTracking) {
        trackApiPerformance({
          method: req.method,
          path,
          statusCode: response.status,
          responseTime,
          requestId,
        });
      }

      // Add monitoring headers
      response.headers.set('X-Request-ID', requestId);
      response.headers.set('X-Response-Time', `${responseTime}ms`);

      return response;
    } catch (error) {
      const responseTime = Date.now() - startTime;

      // Log error
      console.error(`[${requestId}] ${req.method} ${path} - Error (${responseTime}ms):`, error);

      // Track error
      if (config.enableErrorTracking && error instanceof Error) {
        Sentry.withScope((scope) => {
          scope.setTag('requestId', requestId);
          scope.setContext('request', {
            method: req.method,
            path,
            responseTime,
          });
          Sentry.captureException(error);
        });
      }

      // Track error performance
      if (config.enablePerformanceTracking) {
        trackApiPerformance({
          method: req.method,
          path,
          statusCode: 500,
          responseTime,
          requestId,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }

      // Return error response
      return NextResponse.json(
        {
          error: 'Internal Server Error',
          requestId,
          timestamp: new Date().toISOString(),
        },
        {
          status: 500,
          headers: {
            'X-Request-ID': requestId,
            'X-Response-Time': `${responseTime}ms`,
          },
        }
      );
    }
  };
}

/**
 * Generate a unique request ID
 */
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Sanitize headers by removing sensitive information
 */
function sanitizeHeaders(
  headers: Headers,
  sensitiveHeaders: string[] = []
): Record<string, string> {
  const sanitized: Record<string, string> = {};
  
  headers.forEach((value, key) => {
    const lowerKey = key.toLowerCase();
    if (sensitiveHeaders.includes(lowerKey)) {
      sanitized[key] = '[REDACTED]';
    } else {
      sanitized[key] = value;
    }
  });

  return sanitized;
}

/**
 * Track API performance metrics
 */
function trackApiPerformance(metrics: {
  method: string;
  path: string;
  statusCode: number;
  responseTime: number;
  requestId: string;
  error?: string;
}) {
  // In a real implementation, you would send these metrics to your monitoring service
  // For now, we'll just log them
  if (process.env.NODE_ENV === 'development') {
    console.log('API Performance:', metrics);
  }

  // You could integrate with PostHog, DataDog, or other services here
  // Example:
  // posthog.capture('api_request', {
  //   method: metrics.method,
  //   path: metrics.path,
  //   status_code: metrics.statusCode,
  //   response_time: metrics.responseTime,
  //   error: metrics.error,
  // });
}

/**
 * Middleware for rate limiting with monitoring
 */
export function withRateLimit(
  handler: (req: NextRequest) => Promise<NextResponse>,
  options: {
    windowMs?: number;
    maxRequests?: number;
    keyGenerator?: (req: NextRequest) => string;
  } = {}
) {
  const {
    windowMs = 15 * 60 * 1000, // 15 minutes
    maxRequests = 100,
    keyGenerator = (req) => req.ip || 'anonymous',
  } = options;

  const requests = new Map<string, { count: number; resetTime: number }>();

  return withMonitoring(async (req: NextRequest) => {
    const key = keyGenerator(req);
    const now = Date.now();
    const windowStart = now - windowMs;

    // Clean up old entries
    Array.from(requests.entries()).forEach(([k, v]) => {
      if (v.resetTime < windowStart) {
        requests.delete(k);
      }
    });

    // Get or create request count for this key
    const requestData = requests.get(key) || { count: 0, resetTime: now + windowMs };

    // Check if rate limit exceeded
    if (requestData.count >= maxRequests) {
      // Track rate limit violation
      Sentry.addBreadcrumb({
        message: 'Rate limit exceeded',
        data: { key, count: requestData.count, limit: maxRequests },
        level: 'warning',
      });

      return NextResponse.json(
        {
          error: 'Rate limit exceeded',
          retryAfter: Math.ceil((requestData.resetTime - now) / 1000),
        },
        {
          status: 429,
          headers: {
            'Retry-After': Math.ceil((requestData.resetTime - now) / 1000).toString(),
            'X-RateLimit-Limit': maxRequests.toString(),
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': requestData.resetTime.toString(),
          },
        }
      );
    }

    // Increment request count
    requestData.count++;
    requests.set(key, requestData);

    // Execute handler
    const response = await handler(req);

    // Add rate limit headers
    response.headers.set('X-RateLimit-Limit', maxRequests.toString());
    response.headers.set('X-RateLimit-Remaining', (maxRequests - requestData.count).toString());
    response.headers.set('X-RateLimit-Reset', requestData.resetTime.toString());

    return response;
  });
}

/**
 * Health check endpoint with monitoring
 */
export function createHealthCheck() {
  return withMonitoring(async (req: NextRequest) => {
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
      environment: process.env.NEXT_PUBLIC_APP_ENV || 'development',
      uptime: process.uptime(),
      memory: process.memoryUsage(),
    };

    return NextResponse.json(health);
  }, {
    enableRequestLogging: false, // Don't log health checks
  });
}
