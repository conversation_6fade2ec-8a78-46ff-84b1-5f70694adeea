'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import LogRocket from 'logrocket';
import posthog from 'posthog-js';
import { PostHogProvider } from 'posthog-js/react';
import { Analytics } from '@vercel/analytics/react';
import { SpeedInsights } from '@vercel/speed-insights/next';
import { Sentry } from '../../sentry.client.config';

interface MonitoringConfig {
  sentryEnabled: boolean;
  logRocketEnabled: boolean;
  postHogEnabled: boolean;
  vercelAnalyticsEnabled: boolean;
  performanceMonitoringEnabled: boolean;
}

interface MonitoringContextType {
  config: MonitoringConfig;
  trackEvent: (event: string, properties?: Record<string, any>) => void;
  trackError: (error: Error, context?: Record<string, any>) => void;
  trackPerformance: (metric: string, value: number, tags?: Record<string, any>) => void;
  setUserContext: (user: { id: string; email?: string; name?: string }) => void;
}

const MonitoringContext = createContext<MonitoringContextType | null>(null);

export const useMonitoring = () => {
  const context = useContext(MonitoringContext);
  if (!context) {
    throw new Error('useMonitoring must be used within MonitoringProvider');
  }
  return context;
};

interface MonitoringProviderProps {
  children: React.ReactNode;
}

export function MonitoringProvider({ children }: MonitoringProviderProps) {
  const [config, setConfig] = useState<MonitoringConfig>({
    sentryEnabled: false,
    logRocketEnabled: false,
    postHogEnabled: false,
    vercelAnalyticsEnabled: false,
    performanceMonitoringEnabled: false,
  });

  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    initializeMonitoring();
  }, []);

  const initializeMonitoring = async () => {
    try {
      const newConfig: MonitoringConfig = {
        sentryEnabled: !!process.env.NEXT_PUBLIC_SENTRY_DSN && 
                      process.env.NEXT_PUBLIC_ENABLE_ERROR_REPORTING === 'true',
        logRocketEnabled: !!process.env.NEXT_PUBLIC_LOGROCKET_APP_ID && 
                         process.env.NEXT_PUBLIC_ENABLE_SESSION_REPLAY === 'true',
        postHogEnabled: !!process.env.NEXT_PUBLIC_POSTHOG_KEY && 
                       process.env.NEXT_PUBLIC_ENABLE_ANALYTICS === 'true',
        vercelAnalyticsEnabled: !!process.env.NEXT_PUBLIC_VERCEL_ANALYTICS_ID,
        performanceMonitoringEnabled: process.env.NEXT_PUBLIC_ENABLE_PERFORMANCE_MONITORING === 'true',
      };

      // Initialize LogRocket
      if (newConfig.logRocketEnabled) {
        LogRocket.init(process.env.NEXT_PUBLIC_LOGROCKET_APP_ID!);
        
        // Connect LogRocket to Sentry
        if (newConfig.sentryEnabled) {
          LogRocket.getSessionURL((sessionURL) => {
            Sentry.setContext('LogRocket', { sessionURL });
          });
        }
      }

      // Initialize PostHog
      if (newConfig.postHogEnabled && typeof window !== 'undefined') {
        posthog.init(process.env.NEXT_PUBLIC_POSTHOG_KEY!, {
          api_host: process.env.NEXT_PUBLIC_POSTHOG_HOST || 'https://app.posthog.com',
          loaded: (posthog) => {
            if (process.env.NODE_ENV === 'development') {
              posthog.debug();
            }
          },
        });
      }

      setConfig(newConfig);
      setIsInitialized(true);

      console.log('Monitoring initialized:', newConfig);
    } catch (error) {
      console.error('Failed to initialize monitoring:', error);
      setIsInitialized(true); // Still set to true to prevent blocking
    }
  };

  const trackEvent = (event: string, properties?: Record<string, any>) => {
    try {
      // PostHog
      if (config.postHogEnabled && typeof window !== 'undefined') {
        posthog.capture(event, properties);
      }

      // Sentry (for important events)
      if (config.sentryEnabled) {
        Sentry.addBreadcrumb({
          message: event,
          data: properties,
          level: 'info',
        });
      }

      // Console log in development
      if (process.env.NODE_ENV === 'development') {
        console.log('Event tracked:', event, properties);
      }
    } catch (error) {
      console.error('Failed to track event:', error);
    }
  };

  const trackError = (error: Error, context?: Record<string, any>) => {
    try {
      // Sentry
      if (config.sentryEnabled) {
        if (context) {
          Object.entries(context).forEach(([key, value]) => {
            Sentry.setContext(key, value);
          });
        }
        Sentry.captureException(error);
      }

      // LogRocket
      if (config.logRocketEnabled) {
        LogRocket.captureException(error);
      }

      // PostHog
      if (config.postHogEnabled && typeof window !== 'undefined') {
        posthog.capture('error', {
          error: error.message,
          stack: error.stack,
          ...context,
        });
      }

      // Console log in development
      if (process.env.NODE_ENV === 'development') {
        console.error('Error tracked:', error, context);
      }
    } catch (trackingError) {
      console.error('Failed to track error:', trackingError);
    }
  };

  const trackPerformance = (metric: string, value: number, tags?: Record<string, any>) => {
    try {
      // PostHog
      if (config.postHogEnabled && typeof window !== 'undefined') {
        posthog.capture('performance_metric', {
          metric,
          value,
          ...tags,
        });
      }

      // Sentry
      if (config.sentryEnabled) {
        Sentry.addBreadcrumb({
          message: `Performance: ${metric}`,
          data: { value, ...tags },
          level: 'info',
        });
      }

      // Console log in development
      if (process.env.NODE_ENV === 'development') {
        console.log('Performance tracked:', metric, value, tags);
      }
    } catch (error) {
      console.error('Failed to track performance:', error);
    }
  };

  const setUserContext = (user: { id: string; email?: string; name?: string }) => {
    try {
      // Sentry
      if (config.sentryEnabled) {
        Sentry.setUser(user);
      }

      // LogRocket
      if (config.logRocketEnabled) {
        LogRocket.identify(user.id, {
          email: user.email || '',
          name: user.name || '',
        });
      }

      // PostHog
      if (config.postHogEnabled && typeof window !== 'undefined') {
        posthog.identify(user.id, {
          email: user.email,
          name: user.name,
        });
      }

      console.log('User context set:', user);
    } catch (error) {
      console.error('Failed to set user context:', error);
    }
  };

  const contextValue: MonitoringContextType = {
    config,
    trackEvent,
    trackError,
    trackPerformance,
    setUserContext,
  };

  if (!isInitialized) {
    return <div>Initializing monitoring...</div>;
  }

  const content = (
    <MonitoringContext.Provider value={contextValue}>
      {children}
      {config.vercelAnalyticsEnabled && <Analytics />}
      {config.performanceMonitoringEnabled && <SpeedInsights />}
    </MonitoringContext.Provider>
  );

  // Wrap with PostHog provider if enabled
  if (config.postHogEnabled && typeof window !== 'undefined') {
    return (
      <PostHogProvider client={posthog}>
        {content}
      </PostHogProvider>
    );
  }

  return content;
}
