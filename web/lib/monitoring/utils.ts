/**
 * Monitoring utilities for performance tracking and error handling
 */

export interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  tags?: Record<string, any>;
}

export interface ErrorReport {
  error: Error;
  context: Record<string, any>;
  timestamp: number;
  userId?: string;
  sessionId?: string;
}

/**
 * Performance monitoring utilities
 */
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: PerformanceMetric[] = [];
  private observers: PerformanceObserver[] = [];

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  constructor() {
    if (typeof window !== 'undefined') {
      this.initializeObservers();
    }
  }

  private initializeObservers() {
    try {
      // Navigation timing
      if ('PerformanceObserver' in window) {
        const navObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.entryType === 'navigation') {
              const navEntry = entry as PerformanceNavigationTiming;
              this.recordMetric('page_load_time', navEntry.loadEventEnd - navEntry.fetchStart);
              this.recordMetric('dom_content_loaded', navEntry.domContentLoadedEventEnd - navEntry.fetchStart);
              this.recordMetric('first_byte', navEntry.responseStart - navEntry.fetchStart);
            }
          }
        });
        navObserver.observe({ entryTypes: ['navigation'] });
        this.observers.push(navObserver);

        // Paint timing
        const paintObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.recordMetric(entry.name.replace('-', '_'), entry.startTime);
          }
        });
        paintObserver.observe({ entryTypes: ['paint'] });
        this.observers.push(paintObserver);

        // Largest Contentful Paint
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          this.recordMetric('largest_contentful_paint', lastEntry.startTime);
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        this.observers.push(lcpObserver);

        // First Input Delay
        const fidObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            const fidEntry = entry as any;
            this.recordMetric('first_input_delay', fidEntry.processingStart - fidEntry.startTime);
          }
        });
        fidObserver.observe({ entryTypes: ['first-input'] });
        this.observers.push(fidObserver);

        // Cumulative Layout Shift
        let clsValue = 0;
        const clsObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            const layoutShiftEntry = entry as any;
            if (!layoutShiftEntry.hadRecentInput) {
              clsValue += layoutShiftEntry.value;
            }
          }
          this.recordMetric('cumulative_layout_shift', clsValue);
        });
        clsObserver.observe({ entryTypes: ['layout-shift'] });
        this.observers.push(clsObserver);
      }
    } catch (error) {
      console.warn('Failed to initialize performance observers:', error);
    }
  }

  recordMetric(name: string, value: number, tags?: Record<string, any>) {
    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      tags,
    };
    
    this.metrics.push(metric);
    
    // Keep only last 100 metrics to prevent memory leaks
    if (this.metrics.length > 100) {
      this.metrics = this.metrics.slice(-100);
    }

    // Log in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`Performance metric: ${name} = ${value}ms`, tags);
    }
  }

  getMetrics(): PerformanceMetric[] {
    return [...this.metrics];
  }

  getMetricsByName(name: string): PerformanceMetric[] {
    return this.metrics.filter(metric => metric.name === name);
  }

  getAverageMetric(name: string): number | null {
    const metrics = this.getMetricsByName(name);
    if (metrics.length === 0) return null;
    
    const sum = metrics.reduce((acc, metric) => acc + metric.value, 0);
    return sum / metrics.length;
  }

  clearMetrics() {
    this.metrics = [];
  }

  destroy() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    this.metrics = [];
  }
}

/**
 * Memory monitoring utilities
 */
export class MemoryMonitor {
  private static instance: MemoryMonitor;
  private intervalId: NodeJS.Timeout | null = null;
  private onMemoryWarning?: (usage: number) => void;

  static getInstance(): MemoryMonitor {
    if (!MemoryMonitor.instance) {
      MemoryMonitor.instance = new MemoryMonitor();
    }
    return MemoryMonitor.instance;
  }

  startMonitoring(onMemoryWarning?: (usage: number) => void) {
    if (typeof window === 'undefined' || !('memory' in performance)) {
      console.warn('Memory monitoring not supported in this environment');
      return;
    }

    this.onMemoryWarning = onMemoryWarning;

    this.intervalId = setInterval(() => {
      const memory = (performance as any).memory;
      if (memory) {
        const usedMB = memory.usedJSHeapSize / 1024 / 1024;
        const totalMB = memory.totalJSHeapSize / 1024 / 1024;
        const limitMB = memory.jsHeapSizeLimit / 1024 / 1024;

        const usagePercentage = (usedMB / limitMB) * 100;

        // Warn if memory usage is high
        const threshold = parseInt(process.env.NEXT_PUBLIC_PERFORMANCE_MEMORY_THRESHOLD || '80');
        if (usagePercentage > threshold && this.onMemoryWarning) {
          this.onMemoryWarning(usagePercentage);
        }

        if (process.env.NODE_ENV === 'development') {
          console.log(`Memory usage: ${usedMB.toFixed(2)}MB / ${limitMB.toFixed(2)}MB (${usagePercentage.toFixed(1)}%)`);
        }
      }
    }, 30000); // Check every 30 seconds
  }

  stopMonitoring() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  getCurrentMemoryUsage(): { used: number; total: number; limit: number } | null {
    if (typeof window === 'undefined' || !('memory' in performance)) {
      return null;
    }

    const memory = (performance as any).memory;
    if (!memory) return null;

    return {
      used: memory.usedJSHeapSize / 1024 / 1024, // MB
      total: memory.totalJSHeapSize / 1024 / 1024, // MB
      limit: memory.jsHeapSizeLimit / 1024 / 1024, // MB
    };
  }
}

/**
 * Network monitoring utilities
 */
export class NetworkMonitor {
  private static instance: NetworkMonitor;
  private isOnline: boolean = true;
  private onlineCallbacks: (() => void)[] = [];
  private offlineCallbacks: (() => void)[] = [];

  static getInstance(): NetworkMonitor {
    if (!NetworkMonitor.instance) {
      NetworkMonitor.instance = new NetworkMonitor();
    }
    return NetworkMonitor.instance;
  }

  constructor() {
    if (typeof window !== 'undefined') {
      this.isOnline = navigator.onLine;
      window.addEventListener('online', this.handleOnline.bind(this));
      window.addEventListener('offline', this.handleOffline.bind(this));
    }
  }

  private handleOnline() {
    this.isOnline = true;
    this.onlineCallbacks.forEach(callback => callback());
    console.log('Network: Back online');
  }

  private handleOffline() {
    this.isOnline = false;
    this.offlineCallbacks.forEach(callback => callback());
    console.log('Network: Gone offline');
  }

  getNetworkStatus(): boolean {
    return this.isOnline;
  }

  onOnline(callback: () => void) {
    this.onlineCallbacks.push(callback);
  }

  onOffline(callback: () => void) {
    this.offlineCallbacks.push(callback);
  }

  removeOnlineCallback(callback: () => void) {
    this.onlineCallbacks = this.onlineCallbacks.filter(cb => cb !== callback);
  }

  removeOfflineCallback(callback: () => void) {
    this.offlineCallbacks = this.offlineCallbacks.filter(cb => cb !== callback);
  }

  getConnectionInfo(): any {
    if (typeof window !== 'undefined' && 'connection' in navigator) {
      return (navigator as any).connection;
    }
    return null;
  }
}

/**
 * Error boundary utilities
 */
export function createErrorBoundaryLogger(componentName: string) {
  return (error: Error, errorInfo: any) => {
    console.error(`Error in ${componentName}:`, error, errorInfo);
    
    // You can integrate with your monitoring service here
    if (typeof window !== 'undefined' && (window as any).MonitoringProvider) {
      // This would be called by your error boundary
    }
  };
}

/**
 * Utility to measure function execution time
 */
export function measureExecutionTime<T>(
  fn: () => T,
  name: string,
  onComplete?: (duration: number) => void
): T {
  const start = performance.now();
  try {
    const result = fn();
    const duration = performance.now() - start;
    
    if (onComplete) {
      onComplete(duration);
    }
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`${name} executed in ${duration.toFixed(2)}ms`);
    }
    
    return result;
  } catch (error) {
    const duration = performance.now() - start;
    console.error(`${name} failed after ${duration.toFixed(2)}ms:`, error);
    throw error;
  }
}

/**
 * Utility to measure async function execution time
 */
export async function measureAsyncExecutionTime<T>(
  fn: () => Promise<T>,
  name: string,
  onComplete?: (duration: number) => void
): Promise<T> {
  const start = performance.now();
  try {
    const result = await fn();
    const duration = performance.now() - start;
    
    if (onComplete) {
      onComplete(duration);
    }
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`${name} executed in ${duration.toFixed(2)}ms`);
    }
    
    return result;
  } catch (error) {
    const duration = performance.now() - start;
    console.error(`${name} failed after ${duration.toFixed(2)}ms:`, error);
    throw error;
  }
}
