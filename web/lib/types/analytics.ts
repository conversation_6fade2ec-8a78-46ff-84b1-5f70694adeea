/**
 * Analytics and Dashboard Types
 */

export interface Dashboard {
  id: string;
  name: string;
  description?: string;
  layout: {
    columns: number;
    rows: number;
  };
  theme: 'light' | 'dark';
  refresh_interval?: number;
  is_public: boolean;
  is_default: boolean;
  allowed_users?: string[];
  project_id?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  widget_count?: number;
  widgets?: DashboardWidget[];
}

export interface DashboardWidget {
  id: string;
  dashboard_id: string;
  name: string;
  description?: string;
  widget_type: WidgetType;
  chart_type?: ChartType;
  data_source: DataSource;
  entity_filters: Record<string, any>;
  date_range: DateRange;
  chart_config: Record<string, any>;
  position: WidgetPosition;
  cached_data?: any;
  last_cached?: string;
  cache_duration?: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface WidgetPosition {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface DateRange {
  period?: string;
  start_date?: string;
  end_date?: string;
  custom_range?: {
    start: string;
    end: string;
  };
}

export interface TrendAnalysis {
  id: string;
  name: string;
  description?: string;
  analysis_type: string;
  entity_type: DataSource;
  field_name: string;
  time_period: {
    start_date: string;
    end_date: string;
  };
  algorithm: string;
  parameters: Record<string, any>;
  trend_direction?: 'increasing' | 'decreasing' | 'stable';
  trend_strength?: number;
  confidence_score?: number;
  correlation_coefficient?: number;
  r_squared?: number;
  data_points_count?: number;
  analysis_duration_ms?: number;
  results?: any;
  project_id?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface AnalyticsQuery {
  entity_type: DataSource;
  metrics: string[];
  filters?: Record<string, any>;
  date_range?: DateRange;
  group_by?: string[];
  aggregation_period?: AggregationPeriod;
}

export interface AnalyticsResult {
  query: AnalyticsQuery;
  data: any[];
  metadata: {
    processing_time_ms: number;
    entity_type: DataSource;
  };
  total_count: number;
  processing_time_ms: number;
  generated_at: string;
}

export interface ChartData {
  data: any[];
  metadata: {
    chart_type: string;
    total_value?: number;
    point_count?: number;
    [key: string]: any;
  };
}

export interface DashboardSummary {
  total_dashboards: number;
  public_dashboards: number;
  private_dashboards: number;
  total_widgets: number;
  widgets_by_type: Record<string, number>;
  most_used_chart_types: Array<{
    type: string;
    count: number;
  }>;
  recent_activity: Array<{
    dashboard_id: string;
    dashboard_name: string;
    action: string;
    timestamp: string;
  }>;
}

// Enums
export enum WidgetType {
  CHART = 'chart',
  METRIC = 'metric',
  TABLE = 'table',
  TEXT = 'text'
}

export enum ChartType {
  PIE = 'pie',
  BAR = 'bar',
  LINE = 'line',
  SCATTER = 'scatter',
  HEATMAP = 'heatmap',
  GAUGE = 'gauge',
  AREA = 'area',
  DONUT = 'donut',
  FUNNEL = 'funnel',
  RADAR = 'radar'
}

export enum DataSource {
  RISKS = 'risks',
  ACTIONS = 'actions',
  ISSUES = 'issues',
  DECISIONS = 'decisions',
  PROJECTS = 'projects',
  USERS = 'users'
}

export enum AggregationPeriod {
  HOURLY = 'hourly',
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  YEARLY = 'yearly'
}

// Create/Update types
export interface DashboardCreate {
  name: string;
  description?: string;
  layout?: {
    columns: number;
    rows: number;
  };
  theme?: 'light' | 'dark';
  refresh_interval?: number;
  is_public?: boolean;
  is_default?: boolean;
  allowed_users?: string[];
  project_id?: string;
}

export interface DashboardUpdate {
  name?: string;
  description?: string;
  layout?: {
    columns: number;
    rows: number;
  };
  theme?: 'light' | 'dark';
  refresh_interval?: number;
  is_public?: boolean;
  is_default?: boolean;
  allowed_users?: string[];
}

export interface DashboardWidgetCreate {
  dashboard_id: string;
  name: string;
  description?: string;
  widget_type: WidgetType;
  chart_type?: ChartType;
  data_source: DataSource;
  entity_filters?: Record<string, any>;
  date_range?: DateRange;
  chart_config?: Record<string, any>;
  position: WidgetPosition;
  cache_duration?: number;
  is_active?: boolean;
}

export interface DashboardWidgetUpdate {
  name?: string;
  description?: string;
  widget_type?: WidgetType;
  chart_type?: ChartType;
  data_source?: DataSource;
  entity_filters?: Record<string, any>;
  date_range?: DateRange;
  chart_config?: Record<string, any>;
  position?: WidgetPosition;
  cache_duration?: number;
  is_active?: boolean;
}

export interface TrendAnalysisCreate {
  name: string;
  description?: string;
  analysis_type: string;
  entity_type: DataSource;
  field_name: string;
  time_period: {
    start_date: string;
    end_date: string;
  };
  algorithm?: string;
  parameters?: Record<string, any>;
  project_id?: string;
}

// Chart processing types
export interface ChartProcessingRequest {
  data: any[];
  value_field?: string;
  label_field?: string;
  x_field?: string;
  y_field?: string;
  size_field?: string;
  color_field?: string;
  time_aggregation?: string;
  sort_by?: string;
  ascending?: boolean;
  max_slices?: number;
}

export interface GaugeChartRequest {
  current_value: number;
  min_value?: number;
  max_value?: number;
  thresholds?: Record<string, number>;
}

// Widget configuration types
export interface PieChartConfig {
  show_legend?: boolean;
  show_labels?: boolean;
  max_slices?: number;
  inner_radius?: number;
  outer_radius?: number;
}

export interface BarChartConfig {
  show_legend?: boolean;
  show_grid?: boolean;
  orientation?: 'horizontal' | 'vertical';
  stack_bars?: boolean;
  bar_size?: number;
}

export interface LineChartConfig {
  show_legend?: boolean;
  show_grid?: boolean;
  show_dots?: boolean;
  smooth_line?: boolean;
  fill_area?: boolean;
  stroke_width?: number;
}

export interface ScatterChartConfig {
  show_legend?: boolean;
  show_grid?: boolean;
  dot_size?: number;
  show_regression?: boolean;
}

export interface HeatmapConfig {
  show_legend?: boolean;
  color_scheme?: string;
  cell_size?: number;
  show_values?: boolean;
}

export interface GaugeConfig {
  show_value?: boolean;
  show_percentage?: boolean;
  color_scheme?: string;
  thresholds?: Record<string, number>;
}

// Layout types for react-grid-layout
export interface GridLayout {
  i: string;
  x: number;
  y: number;
  w: number;
  h: number;
  minW?: number;
  minH?: number;
  maxW?: number;
  maxH?: number;
  static?: boolean;
  isDraggable?: boolean;
  isResizable?: boolean;
}

export interface LayoutBreakpoint {
  lg: GridLayout[];
  md: GridLayout[];
  sm: GridLayout[];
  xs: GridLayout[];
  xxs: GridLayout[];
}

// API Response types
export interface ApiResponse<T> {
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  per_page: number;
  total_pages: number;
}

// Filter and search types
export interface DashboardFilters {
  project_id?: string;
  is_public?: boolean;
  created_by?: string;
  search?: string;
}

export interface WidgetFilters {
  dashboard_id?: string;
  widget_type?: WidgetType;
  data_source?: DataSource;
  is_active?: boolean;
}

export interface TrendAnalysisFilters {
  project_id?: string;
  analysis_type?: string;
  entity_type?: DataSource;
  created_by?: string;
}
