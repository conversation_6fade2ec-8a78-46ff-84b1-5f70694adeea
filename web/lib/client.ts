/**
 * API client for RAID application
 */

export const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';

interface ApiError {
  success: boolean;
  error: string;
  details?: any;
  type?: string;
}

class ApiClientError extends Error {
  public status: number;
  public details?: any;
  public type?: string;

  constructor(message: string, status: number, details?: any, type?: string) {
    super(message);
    this.name = 'ApiClientError';
    this.status = status;
    this.details = details;
    this.type = type;
  }
}

export async function api(path: string, init?: RequestInit) {
  const url = `${API_URL}${path}`;

  // Default headers
  const headers = {
    'Content-Type': 'application/json',
    ...(init?.headers || {})
  };

  try {
    const res = await fetch(url, {
      ...init,
      headers
    });

    // Handle different response types
    const contentType = res.headers.get('content-type');
    let responseData;

    if (contentType && contentType.includes('application/json')) {
      responseData = await res.json();
    } else {
      responseData = await res.text();
    }

    if (!res.ok) {
      // Handle API error responses
      if (typeof responseData === 'object' && responseData.error) {
        throw new ApiClientError(
          responseData.error,
          res.status,
          responseData.details,
          responseData.type
        );
      } else {
        throw new ApiClientError(
          `HTTP ${res.status}: ${responseData}`,
          res.status
        );
      }
    }

    return responseData;
  } catch (error) {
    if (error instanceof ApiClientError) {
      throw error;
    }

    // Handle network errors
    throw new ApiClientError(
      `Network error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      0
    );
  }
}

// Convenience methods for common HTTP verbs
export const apiClient = {
  get: (path: string, params?: Record<string, any>) => {
    const url = params ? `${path}?${new URLSearchParams(params)}` : path;
    return api(url);
  },

  post: (path: string, data?: any) =>
    api(path, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined
    }),

  put: (path: string, data?: any) =>
    api(path, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined
    }),

  patch: (path: string, data?: any) =>
    api(path, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined
    }),

  delete: (path: string) =>
    api(path, { method: 'DELETE' })
};
