/**
 * Production-ready performance monitoring utilities
 */

import React from 'react';
import { logger } from './logger';

// Performance metrics interface
interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  timestamp: number;
  context?: Record<string, any>;
}

// Performance thresholds
const PERFORMANCE_THRESHOLDS = {
  API_CALL: 2000, // 2 seconds
  CHART_RENDER: 1000, // 1 second
  DASHBOARD_LOAD: 3000, // 3 seconds
  WIDGET_LOAD: 1500, // 1.5 seconds
  DATABASE_QUERY: 500, // 500ms
  COMPONENT_RENDER: 100, // 100ms
};

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private observers: PerformanceObserver[] = [];
  private isEnabled: boolean;

  constructor() {
    this.isEnabled = typeof window !== 'undefined' && 'performance' in window;
    this.initializeObservers();
  }

  private initializeObservers(): void {
    if (!this.isEnabled) return;

    try {
      // Observe navigation timing
      const navObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordNavigationMetrics(entry as PerformanceNavigationTiming);
        }
      });
      navObserver.observe({ entryTypes: ['navigation'] });
      this.observers.push(navObserver);

      // Observe resource timing
      const resourceObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordResourceMetrics(entry as PerformanceResourceTiming);
        }
      });
      resourceObserver.observe({ entryTypes: ['resource'] });
      this.observers.push(resourceObserver);

      // Observe paint timing
      const paintObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordPaintMetrics(entry);
        }
      });
      paintObserver.observe({ entryTypes: ['paint'] });
      this.observers.push(paintObserver);

      // Observe largest contentful paint
      const lcpObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordLCPMetrics(entry);
        }
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
      this.observers.push(lcpObserver);

      // Observe layout shift
      const clsObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordCLSMetrics(entry);
        }
      });
      clsObserver.observe({ entryTypes: ['layout-shift'] });
      this.observers.push(clsObserver);

    } catch (error) {
      logger.warn('Failed to initialize performance observers', { error });
    }
  }

  private recordNavigationMetrics(entry: PerformanceNavigationTiming): void {
    const metrics = [
      { name: 'dns_lookup', value: entry.domainLookupEnd - entry.domainLookupStart },
      { name: 'tcp_connect', value: entry.connectEnd - entry.connectStart },
      { name: 'request_response', value: entry.responseEnd - entry.requestStart },
      { name: 'dom_processing', value: entry.domComplete - (entry as any).domLoading },
      { name: 'page_load', value: entry.loadEventEnd - (entry as any).navigationStart },
    ];

    metrics.forEach(metric => {
      this.recordMetric(metric.name, metric.value, 'ms', {
        type: 'navigation',
        url: window.location.href,
      });
    });
  }

  private recordResourceMetrics(entry: PerformanceResourceTiming): void {
    const duration = entry.responseEnd - entry.startTime;
    
    // Only track significant resources
    if (duration > 100) {
      this.recordMetric('resource_load', duration, 'ms', {
        type: 'resource',
        name: entry.name,
        size: entry.transferSize,
        cached: entry.transferSize === 0,
      });
    }
  }

  private recordPaintMetrics(entry: PerformanceEntry): void {
    this.recordMetric(entry.name.replace('-', '_'), entry.startTime, 'ms', {
      type: 'paint',
    });
  }

  private recordLCPMetrics(entry: any): void {
    this.recordMetric('largest_contentful_paint', entry.startTime, 'ms', {
      type: 'lcp',
      element: entry.element?.tagName,
      size: entry.size,
    });
  }

  private recordCLSMetrics(entry: any): void {
    if (!entry.hadRecentInput) {
      this.recordMetric('cumulative_layout_shift', entry.value, 'score', {
        type: 'cls',
        sources: entry.sources?.map((s: any) => s.node?.tagName),
      });
    }
  }

  recordMetric(name: string, value: number, unit: string = 'ms', context?: Record<string, any>): void {
    const metric: PerformanceMetric = {
      name,
      value,
      unit,
      timestamp: Date.now(),
      context,
    };

    this.metrics.push(metric);
    
    // Check thresholds
    const threshold = PERFORMANCE_THRESHOLDS[name.toUpperCase() as keyof typeof PERFORMANCE_THRESHOLDS];
    if (threshold && value > threshold) {
      logger.warn(`Performance threshold exceeded: ${name}`, {
        value,
        threshold,
        unit,
        context,
      });
    }

    // Log performance metric
    logger.performanceMetric(name, value, unit);

    // Keep only last 1000 metrics
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }
  }

  // Timing utilities
  startTiming(name: string): () => void {
    const start = performance.now();
    return () => {
      const duration = performance.now() - start;
      this.recordMetric(name, duration);
      return duration;
    };
  }

  async measureAsync<T>(name: string, fn: () => Promise<T>): Promise<T> {
    const endTiming = this.startTiming(name);
    try {
      const result = await fn();
      endTiming();
      return result;
    } catch (error) {
      endTiming();
      throw error;
    }
  }

  measureSync<T>(name: string, fn: () => T): T {
    const endTiming = this.startTiming(name);
    try {
      const result = fn();
      endTiming();
      return result;
    } catch (error) {
      endTiming();
      throw error;
    }
  }

  // Analytics-specific measurements
  measureChartRender<T>(chartType: string, dataPoints: number, fn: () => T): T {
    return this.measureSync(`chart_render_${chartType}`, () => {
      const result = fn();
      logger.chartRender(chartType, dataPoints, performance.now());
      return result;
    });
  }

  async measureDashboardLoad(dashboardId: string, widgetCount: number, fn: () => Promise<void>): Promise<void> {
    await this.measureAsync(`dashboard_load_${dashboardId}`, async () => {
      await fn();
      logger.dashboardLoad(dashboardId, widgetCount, performance.now());
    });
  }

  // Memory monitoring
  getMemoryUsage(): Record<string, number> | null {
    if (typeof window !== 'undefined' && 'memory' in performance) {
      const memory = (performance as any).memory;
      return {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        limit: memory.jsHeapSizeLimit,
      };
    }
    return null;
  }

  // Bundle size monitoring
  measureBundleSize(): void {
    if (typeof window !== 'undefined') {
      const scripts = Array.from(document.querySelectorAll('script[src]'));
      let totalSize = 0;

      scripts.forEach(script => {
        const src = (script as HTMLScriptElement).src;
        if (src.includes('/_next/static/')) {
          // Estimate bundle size from script tags
          fetch(src, { method: 'HEAD' })
            .then(response => {
              const size = parseInt(response.headers.get('content-length') || '0');
              totalSize += size;
              this.recordMetric('bundle_size', totalSize, 'bytes', {
                type: 'bundle',
                script: src,
              });
            })
            .catch(() => {
              // Ignore errors for bundle size measurement
            });
        }
      });
    }
  }

  // Get performance summary
  getSummary(): Record<string, any> {
    const recentMetrics = this.metrics.filter(m => 
      Date.now() - m.timestamp < 60000 // Last minute
    );

    const summary: Record<string, any> = {};

    // Group by metric name
    const grouped = recentMetrics.reduce((acc, metric) => {
      if (!acc[metric.name]) acc[metric.name] = [];
      acc[metric.name].push(metric.value);
      return acc;
    }, {} as Record<string, number[]>);

    // Calculate statistics
    Object.entries(grouped).forEach(([name, values]) => {
      summary[name] = {
        count: values.length,
        avg: values.reduce((a, b) => a + b, 0) / values.length,
        min: Math.min(...values),
        max: Math.max(...values),
        p95: this.percentile(values, 95),
      };
    });

    return summary;
  }

  private percentile(values: number[], p: number): number {
    const sorted = values.sort((a, b) => a - b);
    const index = Math.ceil((p / 100) * sorted.length) - 1;
    return sorted[index] || 0;
  }

  // Cleanup
  destroy(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    this.metrics = [];
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor();

// React component performance wrapper
export const withPerformanceMonitoring = <P extends object>(
  Component: React.ComponentType<P>,
  componentName: string
) => {
  return React.forwardRef<any, P>((props, ref) => {
    const renderStart = React.useRef<number>();

    React.useLayoutEffect(() => {
      renderStart.current = performance.now();
    });

    React.useEffect(() => {
      if (renderStart.current) {
        const renderTime = performance.now() - renderStart.current;
        performanceMonitor.recordMetric(`component_render_${componentName}`, renderTime);
      }
    });

    return React.createElement(Component, { ...props, ref } as any);
  });
};

// Hook for component performance monitoring
export const usePerformanceMonitoring = (componentName: string) => {
  const renderStart = React.useRef<number>();

  React.useLayoutEffect(() => {
    renderStart.current = performance.now();
  });

  React.useEffect(() => {
    if (renderStart.current) {
      const renderTime = performance.now() - renderStart.current;
      performanceMonitor.recordMetric(`component_render_${componentName}`, renderTime);
    }
  });

  return {
    startTiming: (name: string) => performanceMonitor.startTiming(`${componentName}_${name}`),
    recordMetric: (name: string, value: number, unit?: string) => 
      performanceMonitor.recordMetric(`${componentName}_${name}`, value, unit),
  };
};
