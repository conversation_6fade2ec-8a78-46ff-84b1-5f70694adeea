/**
 * Production-ready validation utilities
 */

import { z } from 'zod';
import { ChartType, DataSource, WidgetType, AggregationPeriod } from '../types/analytics';

// Base validation schemas
export const uuidSchema = z.string().uuid('Invalid UUID format');
export const emailSchema = z.string().email('Invalid email format');
export const urlSchema = z.string().url('Invalid URL format');

// Date validation
export const dateStringSchema = z.string().refine(
  (date) => !isNaN(Date.parse(date)),
  'Invalid date format'
);

export const dateRangeSchema = z.object({
  start_date: dateStringSchema,
  end_date: dateStringSchema,
}).refine(
  (data) => new Date(data.start_date) <= new Date(data.end_date),
  'Start date must be before end date'
);

// Analytics-specific validation schemas
export const dashboardCreateSchema = z.object({
  name: z.string()
    .min(1, 'Dashboard name is required')
    .max(100, 'Dashboard name must be less than 100 characters')
    .regex(/^[a-zA-Z0-9\s\-_]+$/, 'Dashboard name contains invalid characters'),
  description: z.string()
    .max(500, 'Description must be less than 500 characters')
    .optional(),
  layout: z.object({
    columns: z.number().min(1).max(24, 'Columns must be between 1 and 24'),
    rows: z.number().min(1).max(50, 'Rows must be between 1 and 50'),
  }).optional(),
  theme: z.enum(['light', 'dark']).optional(),
  refresh_interval: z.number()
    .min(10, 'Refresh interval must be at least 10 seconds')
    .max(3600, 'Refresh interval must be less than 1 hour')
    .optional(),
  is_public: z.boolean().optional(),
  is_default: z.boolean().optional(),
  allowed_users: z.array(uuidSchema).optional(),
  project_id: uuidSchema.optional(),
});

export const widgetPositionSchema = z.object({
  x: z.number().min(0, 'X position must be non-negative'),
  y: z.number().min(0, 'Y position must be non-negative'),
  width: z.number().min(1, 'Width must be at least 1').max(24, 'Width cannot exceed 24'),
  height: z.number().min(1, 'Height must be at least 1').max(20, 'Height cannot exceed 20'),
});

export const widgetCreateSchema = z.object({
  dashboard_id: uuidSchema,
  name: z.string()
    .min(1, 'Widget name is required')
    .max(100, 'Widget name must be less than 100 characters'),
  description: z.string()
    .max(300, 'Description must be less than 300 characters')
    .optional(),
  widget_type: z.nativeEnum(WidgetType),
  chart_type: z.nativeEnum(ChartType).optional(),
  data_source: z.nativeEnum(DataSource),
  entity_filters: z.record(z.string(), z.any()).optional(),
  date_range: z.object({
    period: z.string().optional(),
    start_date: dateStringSchema.optional(),
    end_date: dateStringSchema.optional(),
    custom_range: z.object({
      start: dateStringSchema,
      end: dateStringSchema,
    }).optional(),
  }).optional(),
  chart_config: z.record(z.string(), z.any()).optional(),
  position: widgetPositionSchema,
  cache_duration: z.number()
    .min(60, 'Cache duration must be at least 60 seconds')
    .max(86400, 'Cache duration cannot exceed 24 hours')
    .optional(),
  is_active: z.boolean().optional(),
});

export const analyticsQuerySchema = z.object({
  entity_type: z.nativeEnum(DataSource),
  metrics: z.array(z.string()).min(1, 'At least one metric is required'),
  filters: z.record(z.string(), z.any()).optional(),
  date_range: z.object({
    period: z.string().optional(),
    start_date: dateStringSchema.optional(),
    end_date: dateStringSchema.optional(),
  }).optional(),
  group_by: z.array(z.string()).optional(),
  aggregation_period: z.nativeEnum(AggregationPeriod).optional(),
});

// Security validation
export const sanitizeHtml = (input: string): string => {
  // Basic HTML sanitization - in production, use a library like DOMPurify
  return input
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
};

export const validateSqlInjection = (input: string): boolean => {
  const sqlPatterns = [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/i,
    /(--|\/\*|\*\/|;)/,
    /(\b(OR|AND)\b.*=.*)/i,
  ];
  
  return !sqlPatterns.some(pattern => pattern.test(input));
};

export const validateXSS = (input: string): boolean => {
  const xssPatterns = [
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
    /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
  ];
  
  return !xssPatterns.some(pattern => pattern.test(input));
};

// Input sanitization
export const sanitizeInput = (input: any): any => {
  if (typeof input === 'string') {
    // Trim whitespace
    input = input.trim();
    
    // Check for SQL injection
    if (!validateSqlInjection(input)) {
      throw new Error('Input contains potentially malicious SQL patterns');
    }
    
    // Check for XSS
    if (!validateXSS(input)) {
      throw new Error('Input contains potentially malicious script patterns');
    }
    
    return sanitizeHtml(input);
  }
  
  if (Array.isArray(input)) {
    return input.map(sanitizeInput);
  }
  
  if (typeof input === 'object' && input !== null) {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(input)) {
      sanitized[sanitizeInput(key)] = sanitizeInput(value);
    }
    return sanitized;
  }
  
  return input;
};

// Rate limiting validation
export const rateLimitSchema = z.object({
  requests: z.number().min(1).max(1000),
  windowMs: z.number().min(1000).max(3600000), // 1 second to 1 hour
});

// File upload validation
export const fileUploadSchema = z.object({
  filename: z.string()
    .min(1, 'Filename is required')
    .max(255, 'Filename too long')
    .regex(/^[a-zA-Z0-9\-_\.]+$/, 'Invalid filename characters'),
  size: z.number()
    .min(1, 'File cannot be empty')
    .max(10 * 1024 * 1024, 'File size cannot exceed 10MB'), // 10MB limit
  type: z.string()
    .regex(/^(image\/(jpeg|png|gif|webp)|application\/(pdf|json|csv))$/, 'Invalid file type'),
});

// Environment validation
export const environmentSchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'test']),
  NEXT_PUBLIC_API_URL: urlSchema,
  NEXT_PUBLIC_APP_URL: urlSchema,
  DATABASE_URL: z.string().min(1, 'Database URL is required'),
  SECRET_KEY: z.string().min(32, 'Secret key must be at least 32 characters'),
});

// Validation helper functions
export const validateEnvironment = () => {
  try {
    environmentSchema.parse({
      NODE_ENV: process.env.NODE_ENV,
      NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
      NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
      DATABASE_URL: process.env.DATABASE_URL,
      SECRET_KEY: process.env.SECRET_KEY,
    });
  } catch (error) {
    console.error('Environment validation failed:', error);
    throw new Error('Invalid environment configuration');
  }
};

export const validateDashboardCreate = (data: any) => {
  return dashboardCreateSchema.parse(sanitizeInput(data));
};

export const validateWidgetCreate = (data: any) => {
  return widgetCreateSchema.parse(sanitizeInput(data));
};

export const validateAnalyticsQuery = (data: any) => {
  return analyticsQuerySchema.parse(sanitizeInput(data));
};

// Custom validation errors
export class ValidationError extends Error {
  constructor(
    message: string,
    public field?: string,
    public code?: string
  ) {
    super(message);
    this.name = 'ValidationError';
  }
}

export class SecurityError extends Error {
  constructor(message: string, public type: 'XSS' | 'SQL_INJECTION' | 'UNAUTHORIZED') {
    super(message);
    this.name = 'SecurityError';
  }
}

// Validation middleware for React components
export const withValidation = <T extends Record<string, any>>(
  schema: z.ZodSchema<T>,
  onError?: (error: z.ZodError) => void
) => {
  return (data: any): T => {
    try {
      return schema.parse(data);
    } catch (error) {
      if (error instanceof z.ZodError) {
        onError?.(error);
        throw new ValidationError(
          error.issues.map(e => `${e.path.join('.')}: ${e.message}`).join(', ')
        );
      }
      throw error;
    }
  };
};
