/**
 * Production-ready caching utilities
 */

import { logger } from './logger';
import { cache as cacheConfig } from '../config/production';

// Cache entry interface
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
  key: string;
  hits: number;
  lastAccessed: number;
}

// Cache statistics
interface CacheStats {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  evictions: number;
  size: number;
  hitRate: number;
}

class MemoryCache {
  private cache = new Map<string, CacheEntry<any>>();
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    evictions: 0,
    size: 0,
    hitRate: 0,
  };
  private maxSize: number;
  private defaultTTL: number;

  constructor(maxSize = cacheConfig.maxSize, defaultTTL = cacheConfig.ttl) {
    this.maxSize = maxSize;
    this.defaultTTL = defaultTTL;
    
    // Cleanup expired entries periodically
    setInterval(() => this.cleanup(), 60000); // Every minute
  }

  // Get item from cache
  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      this.stats.misses++;
      this.updateHitRate();
      return null;
    }

    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      this.stats.misses++;
      this.stats.evictions++;
      this.updateStats();
      return null;
    }

    // Update access statistics
    entry.hits++;
    entry.lastAccessed = Date.now();
    this.stats.hits++;
    this.updateHitRate();

    logger.debug(`Cache hit: ${key}`, {
      hits: entry.hits,
      age: Date.now() - entry.timestamp,
    });

    return entry.data;
  }

  // Set item in cache
  set<T>(key: string, data: T, ttl = this.defaultTTL): void {
    // Evict if at capacity
    if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
      this.evictLRU();
    }

    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl,
      key,
      hits: 0,
      lastAccessed: Date.now(),
    };

    this.cache.set(key, entry);
    this.stats.sets++;
    this.updateStats();

    logger.debug(`Cache set: ${key}`, {
      ttl,
      size: this.cache.size,
    });
  }

  // Delete item from cache
  delete(key: string): boolean {
    const deleted = this.cache.delete(key);
    if (deleted) {
      this.stats.deletes++;
      this.updateStats();
      logger.debug(`Cache delete: ${key}`);
    }
    return deleted;
  }

  // Clear all cache
  clear(): void {
    const size = this.cache.size;
    this.cache.clear();
    this.stats.deletes += size;
    this.updateStats();
    logger.info('Cache cleared', { entriesRemoved: size });
  }

  // Check if key exists
  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;
    
    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      this.stats.evictions++;
      this.updateStats();
      return false;
    }
    
    return true;
  }

  // Get cache statistics
  getStats(): CacheStats {
    return { ...this.stats };
  }

  // Get cache size
  size(): number {
    return this.cache.size;
  }

  // Cleanup expired entries
  private cleanup(): void {
    const now = Date.now();
    let evicted = 0;

    Array.from(this.cache.entries()).forEach(([key, entry]) => {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
        evicted++;
      }
    });

    if (evicted > 0) {
      this.stats.evictions += evicted;
      this.updateStats();
      logger.debug(`Cache cleanup: ${evicted} entries evicted`);
    }
  }

  // Evict least recently used entry
  private evictLRU(): void {
    let oldestKey = '';
    let oldestTime = Date.now();

    Array.from(this.cache.entries()).forEach(([key, entry]) => {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    });

    if (oldestKey) {
      this.cache.delete(oldestKey);
      this.stats.evictions++;
      this.updateStats();
      logger.debug(`Cache LRU eviction: ${oldestKey}`);
    }
  }

  // Update statistics
  private updateStats(): void {
    this.stats.size = this.cache.size;
    this.updateHitRate();
  }

  private updateHitRate(): void {
    const total = this.stats.hits + this.stats.misses;
    this.stats.hitRate = total > 0 ? this.stats.hits / total : 0;
  }
}

// Global cache instances
export const memoryCache = new MemoryCache();

// Browser storage cache
class BrowserStorageCache {
  private storage: Storage;
  private prefix: string;

  constructor(storage: Storage, prefix = 'raid_cache_') {
    this.storage = storage;
    this.prefix = prefix;
  }

  get<T>(key: string): T | null {
    try {
      const item = this.storage.getItem(this.prefix + key);
      if (!item) return null;

      const parsed = JSON.parse(item);
      
      // Check if expired
      if (Date.now() > parsed.expiry) {
        this.delete(key);
        return null;
      }

      return parsed.data;
    } catch (error) {
      logger.warn('Browser cache get error', { key, error });
      return null;
    }
  }

  set<T>(key: string, data: T, ttl = cacheConfig.ttl): void {
    try {
      const item = {
        data,
        expiry: Date.now() + ttl,
      };
      
      this.storage.setItem(this.prefix + key, JSON.stringify(item));
    } catch (error) {
      logger.warn('Browser cache set error', { key, error });
      
      // Try to free up space
      if (error instanceof Error && error.name === 'QuotaExceededError') {
        this.cleanup();
        try {
          this.storage.setItem(this.prefix + key, JSON.stringify({
            data,
            expiry: Date.now() + ttl,
          }));
        } catch {
          logger.error('Browser cache quota exceeded after cleanup', { key });
        }
      }
    }
  }

  delete(key: string): void {
    try {
      this.storage.removeItem(this.prefix + key);
    } catch (error) {
      logger.warn('Browser cache delete error', { key, error });
    }
  }

  clear(): void {
    try {
      const keys = Object.keys(this.storage).filter(key => 
        key.startsWith(this.prefix)
      );
      
      keys.forEach(key => this.storage.removeItem(key));
      logger.info('Browser cache cleared', { entriesRemoved: keys.length });
    } catch (error) {
      logger.warn('Browser cache clear error', { error });
    }
  }

  private cleanup(): void {
    try {
      const keys = Object.keys(this.storage).filter(key => 
        key.startsWith(this.prefix)
      );
      
      let removed = 0;
      keys.forEach(key => {
        try {
          const item = this.storage.getItem(key);
          if (item) {
            const parsed = JSON.parse(item);
            if (Date.now() > parsed.expiry) {
              this.storage.removeItem(key);
              removed++;
            }
          }
        } catch {
          // Remove invalid entries
          this.storage.removeItem(key);
          removed++;
        }
      });

      if (removed > 0) {
        logger.debug(`Browser cache cleanup: ${removed} entries removed`);
      }
    } catch (error) {
      logger.warn('Browser cache cleanup error', { error });
    }
  }
}

// Browser cache instances
export const localStorageCache = typeof window !== 'undefined' 
  ? new BrowserStorageCache(localStorage)
  : null;

export const sessionStorageCache = typeof window !== 'undefined'
  ? new BrowserStorageCache(sessionStorage)
  : null;

// Cache strategies
export const cacheStrategies = {
  // Cache first, then network
  cacheFirst: async <T>(
    key: string,
    fetcher: () => Promise<T>,
    ttl?: number
  ): Promise<T> => {
    const cached = memoryCache.get<T>(key);
    if (cached) return cached;

    const data = await fetcher();
    memoryCache.set(key, data, ttl);
    return data;
  },

  // Network first, fallback to cache
  networkFirst: async <T>(
    key: string,
    fetcher: () => Promise<T>,
    ttl?: number
  ): Promise<T> => {
    try {
      const data = await fetcher();
      memoryCache.set(key, data, ttl);
      return data;
    } catch (error) {
      const cached = memoryCache.get<T>(key);
      if (cached) {
        logger.warn('Network failed, using cached data', { key, error });
        return cached;
      }
      throw error;
    }
  },

  // Stale while revalidate
  staleWhileRevalidate: async <T>(
    key: string,
    fetcher: () => Promise<T>,
    ttl?: number
  ): Promise<T> => {
    const cached = memoryCache.get<T>(key);
    
    // Return cached data immediately if available
    if (cached) {
      // Revalidate in background
      fetcher()
        .then(data => memoryCache.set(key, data, ttl))
        .catch(error => logger.warn('Background revalidation failed', { key, error }));
      
      return cached;
    }

    // No cache, fetch and cache
    const data = await fetcher();
    memoryCache.set(key, data, ttl);
    return data;
  },
};

// Cache utilities
export const cacheUtils = {
  // Generate cache key
  generateKey: (...parts: (string | number | boolean)[]): string => {
    return parts.map(part => String(part)).join(':');
  },

  // Invalidate cache by pattern
  invalidatePattern: (pattern: RegExp): number => {
    let removed = 0;
    Array.from(memoryCache['cache'].keys()).forEach(key => {
      if (pattern.test(key)) {
        memoryCache.delete(key);
        removed++;
      }
    });
    logger.debug(`Cache pattern invalidation: ${removed} entries removed`, { pattern: pattern.source });
    return removed;
  },

  // Warm cache
  warmCache: async <T>(
    key: string,
    fetcher: () => Promise<T>,
    ttl?: number
  ): Promise<void> => {
    try {
      const data = await fetcher();
      memoryCache.set(key, data, ttl);
      logger.debug(`Cache warmed: ${key}`);
    } catch (error) {
      logger.warn('Cache warming failed', { key, error });
    }
  },

  // Get cache health
  getHealth: () => {
    const stats = memoryCache.getStats();
    return {
      healthy: stats.hitRate > 0.7, // 70% hit rate threshold
      stats,
      recommendations: [
        stats.hitRate < 0.5 && 'Consider increasing cache TTL',
        stats.evictions > stats.sets * 0.1 && 'Consider increasing cache size',
        stats.size === 0 && 'Cache is empty, check if caching is working',
      ].filter(Boolean),
    };
  },
};
