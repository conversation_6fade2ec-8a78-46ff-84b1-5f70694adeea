/**
 * Production-ready logging utility
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  context?: Record<string, any>;
  error?: Error;
  userId?: string;
  sessionId?: string;
  requestId?: string;
}

class Logger {
  private level: LogLevel;
  private isDevelopment: boolean;
  private sessionId: string;

  constructor() {
    this.level = process.env.NODE_ENV === 'production' ? LogLevel.INFO : LogLevel.DEBUG;
    this.isDevelopment = process.env.NODE_ENV !== 'production';
    this.sessionId = this.generateSessionId();
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.level;
  }

  private formatMessage(entry: LogEntry): string {
    const levelName = LogLevel[entry.level];
    const timestamp = entry.timestamp;
    const context = entry.context ? ` | Context: ${JSON.stringify(entry.context)}` : '';
    const error = entry.error ? ` | Error: ${entry.error.message}\n${entry.error.stack}` : '';
    
    return `[${timestamp}] ${levelName}: ${entry.message}${context}${error}`;
  }

  private createLogEntry(
    level: LogLevel,
    message: string,
    context?: Record<string, any>,
    error?: Error
  ): LogEntry {
    return {
      timestamp: new Date().toISOString(),
      level,
      message,
      context: {
        ...context,
        sessionId: this.sessionId,
        userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : undefined,
        url: typeof window !== 'undefined' ? window.location.href : undefined,
      },
      error,
      userId: this.getCurrentUserId(),
      sessionId: this.sessionId,
      requestId: this.generateRequestId(),
    };
  }

  private getCurrentUserId(): string | undefined {
    // Get user ID from auth context or localStorage
    if (typeof window !== 'undefined') {
      try {
        const user = localStorage.getItem('user');
        return user ? JSON.parse(user).id : undefined;
      } catch {
        return undefined;
      }
    }
    return undefined;
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async sendToRemoteLogging(entry: LogEntry): Promise<void> {
    if (!this.isDevelopment && entry.level >= LogLevel.WARN) {
      try {
        // Send to remote logging service (e.g., Sentry, LogRocket, etc.)
        await fetch('/api/logs', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(entry),
        });
      } catch (error) {
        // Fallback to console if remote logging fails
        console.error('Failed to send log to remote service:', error);
      }
    }
  }

  debug(message: string, context?: Record<string, any>): void {
    if (!this.shouldLog(LogLevel.DEBUG)) return;

    const entry = this.createLogEntry(LogLevel.DEBUG, message, context);
    
    if (this.isDevelopment) {
      console.debug(this.formatMessage(entry));
    }
  }

  info(message: string, context?: Record<string, any>): void {
    if (!this.shouldLog(LogLevel.INFO)) return;

    const entry = this.createLogEntry(LogLevel.INFO, message, context);
    
    if (this.isDevelopment) {
      console.info(this.formatMessage(entry));
    }
    
    this.sendToRemoteLogging(entry);
  }

  warn(message: string, context?: Record<string, any>, error?: Error): void {
    if (!this.shouldLog(LogLevel.WARN)) return;

    const entry = this.createLogEntry(LogLevel.WARN, message, context, error);
    
    console.warn(this.formatMessage(entry));
    this.sendToRemoteLogging(entry);
  }

  error(message: string, context?: Record<string, any>, error?: Error): void {
    if (!this.shouldLog(LogLevel.ERROR)) return;

    const entry = this.createLogEntry(LogLevel.ERROR, message, context, error);
    
    console.error(this.formatMessage(entry));
    this.sendToRemoteLogging(entry);
  }

  // Analytics-specific logging methods
  analyticsEvent(event: string, data?: Record<string, any>): void {
    this.info(`Analytics Event: ${event}`, {
      eventType: 'analytics',
      eventData: data,
    });
  }

  performanceMetric(metric: string, value: number, unit: string = 'ms'): void {
    this.info(`Performance Metric: ${metric}`, {
      metricType: 'performance',
      metric,
      value,
      unit,
    });
  }

  userAction(action: string, details?: Record<string, any>): void {
    this.info(`User Action: ${action}`, {
      actionType: 'user_interaction',
      actionDetails: details,
    });
  }

  apiCall(method: string, url: string, duration: number, status: number): void {
    const level = status >= 400 ? LogLevel.WARN : LogLevel.INFO;
    const message = `API Call: ${method} ${url} - ${status} (${duration}ms)`;
    
    if (level === LogLevel.WARN) {
      this.warn(message, { method, url, duration, status });
    } else {
      this.info(message, { method, url, duration, status });
    }
  }

  chartRender(chartType: string, dataPoints: number, renderTime: number): void {
    this.debug(`Chart Rendered: ${chartType}`, {
      chartType,
      dataPoints,
      renderTime,
      eventType: 'chart_render',
    });
  }

  dashboardLoad(dashboardId: string, widgetCount: number, loadTime: number): void {
    this.info(`Dashboard Loaded: ${dashboardId}`, {
      dashboardId,
      widgetCount,
      loadTime,
      eventType: 'dashboard_load',
    });
  }

  // Error boundary logging
  componentError(componentName: string, error: Error, errorInfo?: any): void {
    this.error(`Component Error in ${componentName}`, {
      componentName,
      errorInfo,
      eventType: 'component_error',
    }, error);
  }
}

// Global logger instance
export const logger = new Logger();

// React Error Boundary helper
export const logComponentError = (error: Error, errorInfo: any, componentName: string) => {
  logger.componentError(componentName, error, errorInfo);
};

// Performance monitoring helpers
export const measurePerformance = async <T>(
  operation: string,
  fn: () => Promise<T>
): Promise<T> => {
  const start = performance.now();
  try {
    const result = await fn();
    const duration = performance.now() - start;
    logger.performanceMetric(operation, duration);
    return result;
  } catch (error) {
    const duration = performance.now() - start;
    logger.error(`Performance measurement failed for ${operation}`, {
      operation,
      duration,
    }, error as Error);
    throw error;
  }
};

// API call wrapper with logging
export const loggedApiCall = async <T>(
  method: string,
  url: string,
  apiCall: () => Promise<T>
): Promise<T> => {
  const start = performance.now();
  try {
    const result = await apiCall();
    const duration = performance.now() - start;
    logger.apiCall(method, url, duration, 200);
    return result;
  } catch (error: any) {
    const duration = performance.now() - start;
    const status = error.response?.status || 500;
    logger.apiCall(method, url, duration, status);
    throw error;
  }
};
