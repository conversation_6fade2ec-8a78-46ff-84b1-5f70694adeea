/**
 * Analytics API Client
 */

import axios from 'axios';
import { logger, loggedApiCall } from '../utils/logger';
import { performanceMonitor } from '../utils/performance';
import { validateDashboardCreate, validateWidgetCreate, validateAnalyticsQuery, sanitizeInput } from '../utils/validation';
import {
  Dashboard,
  DashboardCreate,
  DashboardUpdate,
  DashboardWidget,
  DashboardWidgetCreate,
  DashboardWidgetUpdate,
  TrendAnalysis,
  TrendAnalysisCreate,
  AnalyticsQuery,
  AnalyticsResult,
  ChartData,
  DashboardSummary,
  ChartProcessingRequest,
  GaugeChartRequest,
  DashboardFilters,
  WidgetFilters,
  TrendAnalysisFilters,
  ApiResponse,
  PaginatedResponse
} from '../types/analytics';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: `${API_BASE_URL}/api/v1`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for auth and logging
apiClient.interceptors.request.use(
  (config) => {
    // Add auth token
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Add request ID for tracing
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    config.headers['X-Request-ID'] = requestId;

    // Log request
    logger.info(`API Request: ${config.method?.toUpperCase()} ${config.url}`, {
      requestId,
      method: config.method,
      url: config.url,
      data: config.data ? Object.keys(config.data) : undefined,
    });

    // Start performance timing
    (config as any).metadata = { startTime: performance.now(), requestId };

    return config;
  },
  (error) => {
    logger.error('API Request Error', { error: error.message });
    return Promise.reject(error);
  }
);

// Response interceptor for logging and error handling
apiClient.interceptors.response.use(
  (response) => {
    const duration = performance.now() - (response.config as any).metadata?.startTime;
    const requestId = (response.config as any).metadata?.requestId;

    logger.apiCall(
      response.config.method?.toUpperCase() || 'GET',
      response.config.url || '',
      duration,
      response.status
    );

    performanceMonitor.recordMetric('api_call', duration, 'ms', {
      method: response.config.method,
      url: response.config.url,
      status: response.status,
      requestId,
    });

    return response;
  },
  (error) => {
    const duration = (error.config as any)?.metadata?.startTime
      ? performance.now() - (error.config as any).metadata.startTime
      : 0;
    const requestId = (error.config as any)?.metadata?.requestId;

    logger.apiCall(
      error.config?.method?.toUpperCase() || 'GET',
      error.config?.url || '',
      duration,
      error.response?.status || 500
    );

    // Enhanced error logging
    logger.error('API Response Error', {
      requestId,
      method: error.config?.method,
      url: error.config?.url,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      duration,
    });

    return Promise.reject(error);
  }
);

// Dashboard API
export const dashboardApi = {
  // Get all dashboards
  async getDashboards(filters?: DashboardFilters): Promise<Dashboard[]> {
    const params = new URLSearchParams();
    if (filters?.project_id) params.append('project_id', filters.project_id);
    if (filters?.is_public !== undefined) params.append('is_public', filters.is_public.toString());
    if (filters?.created_by) params.append('created_by', filters.created_by);
    if (filters?.search) params.append('search', filters.search);

    const response = await apiClient.get<Dashboard[]>(`/dashboards?${params.toString()}`);
    return response.data;
  },

  // Get dashboard by ID
  async getDashboard(id: string): Promise<Dashboard> {
    const response = await apiClient.get<Dashboard>(`/dashboards/${id}`);
    return response.data;
  },

  // Create dashboard
  async createDashboard(data: DashboardCreate): Promise<Dashboard> {
    const validatedData = validateDashboardCreate(data);
    const response = await loggedApiCall('POST', '/dashboards', () =>
      apiClient.post<Dashboard>('/dashboards', validatedData)
    );
    logger.userAction('dashboard_created', { dashboardName: validatedData.name });
    return response.data;
  },

  // Update dashboard
  async updateDashboard(id: string, data: DashboardUpdate): Promise<Dashboard> {
    const response = await apiClient.put<Dashboard>(`/dashboards/${id}`, data);
    return response.data;
  },

  // Delete dashboard
  async deleteDashboard(id: string): Promise<void> {
    await apiClient.delete(`/dashboards/${id}`);
  },

  // Get dashboard widgets
  async getDashboardWidgets(dashboardId: string): Promise<DashboardWidget[]> {
    const response = await apiClient.get<DashboardWidget[]>(`/dashboards/${dashboardId}/widgets`);
    return response.data;
  },
};

// Widget API
export const widgetApi = {
  // Get widget by ID
  async getWidget(id: string): Promise<DashboardWidget> {
    const response = await apiClient.get<DashboardWidget>(`/widgets/${id}`);
    return response.data;
  },

  // Create widget
  async createWidget(data: DashboardWidgetCreate): Promise<DashboardWidget> {
    const validatedData = validateWidgetCreate(data);
    const response = await loggedApiCall('POST', '/widgets', () =>
      apiClient.post<DashboardWidget>('/widgets', validatedData)
    );
    logger.userAction('widget_created', {
      widgetName: validatedData.name,
      widgetType: validatedData.widget_type,
      chartType: validatedData.chart_type,
    });
    return response.data;
  },

  // Update widget
  async updateWidget(id: string, data: DashboardWidgetUpdate): Promise<DashboardWidget> {
    const response = await apiClient.put<DashboardWidget>(`/widgets/${id}`, data);
    return response.data;
  },

  // Delete widget
  async deleteWidget(id: string): Promise<void> {
    await apiClient.delete(`/widgets/${id}`);
  },

  // Get widget data
  async getWidgetData(id: string, forceRefresh = false): Promise<ChartData> {
    const params = forceRefresh ? '?force_refresh=true' : '';
    const response = await apiClient.get<ChartData>(`/widgets/${id}/data${params}`);
    return response.data;
  },
};

// Analytics API
export const analyticsApi = {
  // Execute analytics query
  async executeQuery(query: AnalyticsQuery): Promise<AnalyticsResult> {
    const validatedQuery = validateAnalyticsQuery(query);
    const response = await performanceMonitor.measureAsync('analytics_query', () =>
      loggedApiCall('POST', '/analytics/query', () =>
        apiClient.post<AnalyticsResult>('/analytics/query', validatedQuery)
      )
    );
    logger.analyticsEvent('query_executed', {
      entityType: validatedQuery.entity_type,
      metrics: validatedQuery.metrics,
      hasFilters: !!validatedQuery.filters,
    });
    return response.data;
  },

  // Get dashboard summary
  async getDashboardSummary(projectId?: string): Promise<DashboardSummary> {
    const params = projectId ? `?project_id=${projectId}` : '';
    const response = await apiClient.get<DashboardSummary>(`/analytics/summary${params}`);
    return response.data;
  },
};

// Trend Analysis API
export const trendApi = {
  // Get all trend analyses
  async getTrendAnalyses(filters?: TrendAnalysisFilters): Promise<TrendAnalysis[]> {
    const params = new URLSearchParams();
    if (filters?.project_id) params.append('project_id', filters.project_id);
    if (filters?.analysis_type) params.append('analysis_type', filters.analysis_type);
    if (filters?.entity_type) params.append('entity_type', filters.entity_type);
    if (filters?.created_by) params.append('created_by', filters.created_by);

    const response = await apiClient.get<TrendAnalysis[]>(`/trends?${params.toString()}`);
    return response.data;
  },

  // Get trend analysis by ID
  async getTrendAnalysis(id: string): Promise<TrendAnalysis> {
    const response = await apiClient.get<TrendAnalysis>(`/trends/${id}`);
    return response.data;
  },

  // Create trend analysis
  async createTrendAnalysis(data: TrendAnalysisCreate): Promise<TrendAnalysis> {
    const response = await apiClient.post<TrendAnalysis>('/trends', data);
    return response.data;
  },

  // Delete trend analysis
  async deleteTrendAnalysis(id: string): Promise<void> {
    await apiClient.delete(`/trends/${id}`);
  },
};

// Chart Processing API
export const chartApi = {
  // Process pie chart data
  async processPieChart(data: ChartProcessingRequest): Promise<ChartData> {
    const response = await apiClient.post<ChartData>('/charts/process/pie', data);
    return response.data;
  },

  // Process bar chart data
  async processBarChart(data: ChartProcessingRequest): Promise<ChartData> {
    const response = await apiClient.post<ChartData>('/charts/process/bar', data);
    return response.data;
  },

  // Process line chart data
  async processLineChart(data: ChartProcessingRequest): Promise<ChartData> {
    const response = await apiClient.post<ChartData>('/charts/process/line', data);
    return response.data;
  },

  // Process scatter chart data
  async processScatterChart(data: ChartProcessingRequest): Promise<ChartData> {
    const response = await apiClient.post<ChartData>('/charts/process/scatter', data);
    return response.data;
  },

  // Process heatmap data
  async processHeatmap(data: ChartProcessingRequest): Promise<ChartData> {
    const response = await apiClient.post<ChartData>('/charts/process/heatmap', data);
    return response.data;
  },

  // Process gauge chart data
  async processGaugeChart(data: GaugeChartRequest): Promise<ChartData> {
    const response = await apiClient.post<ChartData>('/charts/process/gauge', data);
    return response.data;
  },
};

// Utility functions
export const analyticsUtils = {
  // Format date for API
  formatDate(date: Date): string {
    return date.toISOString();
  },

  // Parse date from API
  parseDate(dateString: string): Date {
    return new Date(dateString);
  },

  // Get predefined date ranges
  getDateRanges() {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    
    return {
      today: {
        start_date: this.formatDate(today),
        end_date: this.formatDate(now),
      },
      yesterday: {
        start_date: this.formatDate(new Date(today.getTime() - 24 * 60 * 60 * 1000)),
        end_date: this.formatDate(today),
      },
      last_7_days: {
        start_date: this.formatDate(new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)),
        end_date: this.formatDate(now),
      },
      last_30_days: {
        start_date: this.formatDate(new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)),
        end_date: this.formatDate(now),
      },
      last_90_days: {
        start_date: this.formatDate(new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000)),
        end_date: this.formatDate(now),
      },
      this_month: {
        start_date: this.formatDate(new Date(now.getFullYear(), now.getMonth(), 1)),
        end_date: this.formatDate(now),
      },
      last_month: {
        start_date: this.formatDate(new Date(now.getFullYear(), now.getMonth() - 1, 1)),
        end_date: this.formatDate(new Date(now.getFullYear(), now.getMonth(), 0)),
      },
      this_year: {
        start_date: this.formatDate(new Date(now.getFullYear(), 0, 1)),
        end_date: this.formatDate(now),
      },
    };
  },

  // Validate widget position
  validateWidgetPosition(position: { x: number; y: number; width: number; height: number }): boolean {
    return (
      position.x >= 0 &&
      position.y >= 0 &&
      position.width > 0 &&
      position.height > 0 &&
      position.width <= 12 && // Assuming 12-column grid
      position.height <= 20   // Reasonable height limit
    );
  },

  // Generate default widget position
  generateDefaultPosition(existingWidgets: DashboardWidget[] = []): { x: number; y: number; width: number; height: number } {
    const defaultWidth = 6;
    const defaultHeight = 4;
    
    // Find the next available position
    let x = 0;
    let y = 0;
    
    // Simple algorithm to find next position
    const occupied = existingWidgets.map(w => ({
      x: w.position.x,
      y: w.position.y,
      width: w.position.width,
      height: w.position.height,
    }));
    
    // Find first available spot
    while (true) {
      const isOccupied = occupied.some(pos => 
        x < pos.x + pos.width &&
        x + defaultWidth > pos.x &&
        y < pos.y + pos.height &&
        y + defaultHeight > pos.y
      );
      
      if (!isOccupied) {
        break;
      }
      
      x += defaultWidth;
      if (x >= 12) {
        x = 0;
        y += defaultHeight;
      }
    }
    
    return { x, y, width: defaultWidth, height: defaultHeight };
  },
};

// Error handling wrapper
export const withErrorHandling = async <T>(
  apiCall: () => Promise<T>,
  errorMessage = 'An error occurred'
): Promise<T | null> => {
  try {
    return await apiCall();
  } catch (error) {
    console.error(errorMessage, error);
    
    if (axios.isAxiosError(error)) {
      const message = error.response?.data?.detail || error.response?.data?.message || error.message;
      throw new Error(message);
    }
    
    throw error;
  }
};
