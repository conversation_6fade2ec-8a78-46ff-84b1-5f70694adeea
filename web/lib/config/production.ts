/**
 * Production configuration and environment management
 */

import { z } from 'zod';

// Environment schema validation
const envSchema = z.object({
  // App configuration
  NODE_ENV: z.enum(['development', 'production', 'test']),
  NEXT_PUBLIC_APP_NAME: z.string().default('RAID Analytics'),
  NEXT_PUBLIC_APP_VERSION: z.string().default('1.0.0'),
  
  // API configuration
  NEXT_PUBLIC_API_URL: z.string().url(),
  NEXT_PUBLIC_API_TIMEOUT: z.string().default('30000').transform(Number),
  
  // Authentication
  NEXT_PUBLIC_AUTH_DOMAIN: z.string().optional(),
  NEXT_PUBLIC_AUTH_CLIENT_ID: z.string().optional(),
  NEXT_PUBLIC_AUTH_AUDIENCE: z.string().optional(),
  
  // Analytics and monitoring
  NEXT_PUBLIC_ANALYTICS_ID: z.string().optional(),
  NEXT_PUBLIC_SENTRY_DSN: z.string().optional(),
  NEXT_PUBLIC_HOTJAR_ID: z.string().optional(),
  
  // Feature flags
  NEXT_PUBLIC_ENABLE_ANALYTICS: z.string().default('true').transform(Boolean),
  NEXT_PUBLIC_ENABLE_REAL_TIME: z.string().default('true').transform(Boolean),
  NEXT_PUBLIC_ENABLE_EXPORT: z.string().default('true').transform(Boolean),
  NEXT_PUBLIC_ENABLE_SHARING: z.string().default('true').transform(Boolean),
  
  // Performance
  NEXT_PUBLIC_CACHE_TTL: z.string().default('300000').transform(Number), // 5 minutes
  NEXT_PUBLIC_MAX_WIDGETS_PER_DASHBOARD: z.string().default('20').transform(Number),
  NEXT_PUBLIC_MAX_DATA_POINTS: z.string().default('1000').transform(Number),
  
  // Security
  NEXT_PUBLIC_CSP_NONCE: z.string().optional(),
  NEXT_PUBLIC_ALLOWED_ORIGINS: z.string().optional(),
});

// Parse and validate environment variables
const parseEnv = () => {
  try {
    return envSchema.parse(process.env);
  } catch (error) {
    console.error('❌ Invalid environment configuration:', error);
    throw new Error('Environment validation failed');
  }
};

// Configuration object
export const config = parseEnv();

// Feature flags
export const features = {
  analytics: config.NEXT_PUBLIC_ENABLE_ANALYTICS,
  realTime: config.NEXT_PUBLIC_ENABLE_REAL_TIME,
  export: config.NEXT_PUBLIC_ENABLE_EXPORT,
  sharing: config.NEXT_PUBLIC_ENABLE_SHARING,
} as const;

// Performance settings
export const performance = {
  apiTimeout: config.NEXT_PUBLIC_API_TIMEOUT,
  cacheTTL: config.NEXT_PUBLIC_CACHE_TTL,
  maxWidgetsPerDashboard: config.NEXT_PUBLIC_MAX_WIDGETS_PER_DASHBOARD,
  maxDataPoints: config.NEXT_PUBLIC_MAX_DATA_POINTS,
} as const;

// Security settings
export const security = {
  cspNonce: config.NEXT_PUBLIC_CSP_NONCE,
  allowedOrigins: config.NEXT_PUBLIC_ALLOWED_ORIGINS?.split(',') || [],
} as const;

// API configuration
export const api = {
  baseURL: config.NEXT_PUBLIC_API_URL,
  timeout: config.NEXT_PUBLIC_API_TIMEOUT,
  retryAttempts: 3,
  retryDelay: 1000,
} as const;

// Monitoring configuration
export const monitoring = {
  sentry: {
    dsn: config.NEXT_PUBLIC_SENTRY_DSN,
    environment: config.NODE_ENV,
    tracesSampleRate: config.NODE_ENV === 'production' ? 0.1 : 1.0,
  },
  analytics: {
    id: config.NEXT_PUBLIC_ANALYTICS_ID,
  },
  hotjar: {
    id: config.NEXT_PUBLIC_HOTJAR_ID,
  },
} as const;

// Chart configuration
export const charts = {
  defaultColors: [
    '#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6',
    '#06b6d4', '#f97316', '#84cc16', '#ec4899', '#6b7280'
  ],
  maxDataPoints: performance.maxDataPoints,
  animationDuration: 300,
  refreshInterval: 30000, // 30 seconds
} as const;

// Dashboard configuration
export const dashboard = {
  maxWidgets: performance.maxWidgetsPerDashboard,
  gridColumns: 12,
  gridRowHeight: 60,
  gridMargin: [16, 16] as [number, number],
  breakpoints: {
    lg: 1200,
    md: 996,
    sm: 768,
    xs: 480,
    xxs: 0,
  },
  cols: {
    lg: 12,
    md: 10,
    sm: 6,
    xs: 4,
    xxs: 2,
  },
} as const;

// Cache configuration
export const cache = {
  ttl: performance.cacheTTL,
  maxSize: 100, // Maximum number of cached items
  strategies: {
    dashboard: 'stale-while-revalidate',
    widget: 'cache-first',
    analytics: 'network-first',
  },
} as const;

// Rate limiting configuration
export const rateLimiting = {
  api: {
    requests: 100,
    windowMs: 60000, // 1 minute
  },
  analytics: {
    requests: 50,
    windowMs: 60000,
  },
  export: {
    requests: 10,
    windowMs: 60000,
  },
} as const;

// Error handling configuration
export const errorHandling = {
  maxRetries: 3,
  retryDelay: 1000,
  timeoutMs: api.timeout,
  enableErrorBoundary: true,
  enableErrorReporting: config.NODE_ENV === 'production',
} as const;

// Development configuration
export const development = {
  enableDebugLogs: config.NODE_ENV === 'development',
  enablePerformanceMonitoring: true,
  enableMockData: config.NODE_ENV === 'development',
  showErrorDetails: config.NODE_ENV !== 'production',
} as const;

// Production optimizations
export const production = {
  enableCompression: config.NODE_ENV === 'production',
  enableMinification: config.NODE_ENV === 'production',
  enableTreeShaking: config.NODE_ENV === 'production',
  enableCodeSplitting: true,
  enableServiceWorker: config.NODE_ENV === 'production',
} as const;

// Accessibility configuration
export const accessibility = {
  enableHighContrast: false,
  enableReducedMotion: false,
  enableScreenReader: true,
  keyboardNavigation: true,
} as const;

// Internationalization configuration
export const i18n = {
  defaultLocale: 'en',
  locales: ['en', 'es', 'fr', 'de'],
  enableRTL: false,
} as const;

// Export validation function
export const validateConfig = () => {
  const requiredInProduction = [
    'NEXT_PUBLIC_API_URL',
  ];

  if (config.NODE_ENV === 'production') {
    const missing = requiredInProduction.filter(key => !process.env[key]);
    if (missing.length > 0) {
      throw new Error(`Missing required environment variables in production: ${missing.join(', ')}`);
    }
  }

  // Validate API URL is reachable
  if (typeof window === 'undefined') {
    // Server-side validation
    try {
      new URL(config.NEXT_PUBLIC_API_URL);
    } catch {
      throw new Error('Invalid API URL format');
    }
  }

  return true;
};

// Initialize configuration
export const initializeConfig = () => {
  try {
    validateConfig();
    
    if (config.NODE_ENV === 'development') {
      console.log('🔧 Configuration loaded:', {
        environment: config.NODE_ENV,
        apiUrl: config.NEXT_PUBLIC_API_URL,
        features: Object.entries(features).filter(([, enabled]) => enabled).map(([name]) => name),
      });
    }
    
    return true;
  } catch (error) {
    console.error('❌ Configuration initialization failed:', error);
    return false;
  }
};

// Runtime configuration checks
export const checkRuntimeConfig = () => {
  const checks = {
    apiConnection: false,
    authentication: false,
    monitoring: false,
  };

  // Check API connection
  if (typeof window !== 'undefined') {
    fetch(`${config.NEXT_PUBLIC_API_URL}/health`, { method: 'HEAD' })
      .then(() => { checks.apiConnection = true; })
      .catch(() => { checks.apiConnection = false; });
  }

  // Check authentication
  checks.authentication = !!(config.NEXT_PUBLIC_AUTH_DOMAIN && config.NEXT_PUBLIC_AUTH_CLIENT_ID);

  // Check monitoring
  checks.monitoring = !!(config.NEXT_PUBLIC_SENTRY_DSN || config.NEXT_PUBLIC_ANALYTICS_ID);

  return checks;
};

// Export all configurations
export default {
  config,
  features,
  performance,
  security,
  api,
  monitoring,
  charts,
  dashboard,
  cache,
  rateLimiting,
  errorHandling,
  development,
  production,
  accessibility,
  i18n,
  validateConfig,
  initializeConfig,
  checkRuntimeConfig,
};
