{"name": "raid-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:all": "npm run test && npm run test:e2e", "env:setup": "cd .. && scripts/setup-env.sh", "env:validate": "node ../scripts/validate-env.js", "build:production": "NODE_ENV=production npm run build", "build:staging": "NODE_ENV=production NEXT_PUBLIC_APP_ENV=staging npm run build"}, "dependencies": {"@auth0/nextjs-auth0": "^3.5.0", "@clerk/nextjs": "^6.7.3", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@radix-ui/react-tabs": "^1.1.13", "@sentry/nextjs": "^10.10.0", "@sentry/tracing": "^7.120.4", "@tanstack/react-query": "^5.51.0", "@types/react-grid-layout": "^1.3.5", "@types/recharts": "^2.0.1", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "axios": "^1.7.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "logrocket": "^10.1.0", "logrocket-react": "^6.0.3", "lucide-react": "^0.451.0", "next": "14.2.25", "posthog-js": "^1.261.8", "react": "18.2.0", "react-dom": "18.2.0", "react-grid-layout": "^1.5.2", "recharts": "^3.1.2", "tailwind-merge": "^3.3.1", "zod": "^4.1.5"}, "devDependencies": {"@playwright/test": "^1.48.0", "@tanstack/react-query-devtools": "^5.87.3", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.13", "@types/node": "^20.11.30", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "autoprefixer": "^10.4.19", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "msw": "^2.4.9", "postcss": "^8.4.38", "tailwindcss": "^3.4.3", "typescript": "^5.4.5", "vitest": "^3.2.4"}}