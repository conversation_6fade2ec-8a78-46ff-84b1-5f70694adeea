'use client';
import { useState, useMemo, useCallback } from 'react';

export interface FilterState {
  search: string;
  status: string[];
  priority: string[];
  owner: string[];
  category: string[];
  severity: string[];
  assignee: string[];
  decidedBy: string[];
  scoreRange: [number, number];
  progressRange: [number, number];
  dateRange: {
    created_start?: string;
    created_end?: string;
    updated_start?: string;
    updated_end?: string;
    due_start?: string;
    due_end?: string;
  };
  searchMode: 'contains' | 'exact' | 'starts_with' | 'ends_with';
  searchFields: string[];
  caseSensitive: boolean;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

export interface SavedFilter {
  id: string;
  name: string;
  description?: string;
  filter_data: FilterState;
  is_public: boolean;
  project_id?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export function useRAIDFilters(data: any[]) {
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    status: [],
    priority: [],
    owner: [],
    category: [],
    severity: [],
    assignee: [],
    decidedBy: [],
    scoreRange: [0, 100],
    progressRange: [0, 100],
    dateRange: {},
    searchMode: 'contains',
    searchFields: [],
    caseSensitive: false,
    sortBy: 'createdAt',
    sortOrder: 'desc'
  });

  const [savedFilters, setSavedFilters] = useState<SavedFilter[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const filteredData = useMemo(() => {
    let result = data.filter(item => {
      // Search filter with advanced options
      if (filters.search) {
        const searchTerm = filters.caseSensitive ? filters.search : filters.search.toLowerCase();
        const searchableFields = filters.searchFields.length > 0
          ? filters.searchFields.map(field => item[field]).filter(Boolean)
          : [
              item.title,
              item.description,
              item.owner,
              item.rationale,
              item.decidedBy,
              item.assignee
            ].filter(Boolean);

        const matchesSearch = searchableFields.some(field => {
          const fieldValue = filters.caseSensitive ? field : field.toLowerCase();

          switch (filters.searchMode) {
            case 'exact':
              return fieldValue === searchTerm;
            case 'starts_with':
              return fieldValue.startsWith(searchTerm);
            case 'ends_with':
              return fieldValue.endsWith(searchTerm);
            default: // contains
              return fieldValue.includes(searchTerm);
          }
        });

        if (!matchesSearch) return false;
      }

      // Multi-value filters
      if (filters.status.length > 0 && !filters.status.includes(item.status)) {
        return false;
      }

      if (filters.priority.length > 0 && !filters.priority.includes(item.priority)) {
        return false;
      }

      if (filters.owner.length > 0 && item.owner && !filters.owner.includes(item.owner)) {
        return false;
      }

      if (filters.category.length > 0 && !filters.category.includes(item.category)) {
        return false;
      }

      if (filters.severity.length > 0 && item.severity && !filters.severity.includes(item.severity)) {
        return false;
      }

      if (filters.assignee.length > 0 && item.assignee && !filters.assignee.includes(item.assignee)) {
        return false;
      }

      if (filters.decidedBy.length > 0 && item.decidedBy && !filters.decidedBy.includes(item.decidedBy)) {
        return false;
      }

      // Category filter
      const itemCategory = item.category || item.type;
      if (filters.category.length > 0 && itemCategory && !filters.category.includes(itemCategory)) {
        return false;
      }

      // Severity filter
      if (filters.severity.length > 0 && item.severity && !filters.severity.includes(item.severity)) {
        return false;
      }

      // Score range filter - only apply to risk items
      if ((item.category === 'risks' || item.type === 'risks') && item.score !== undefined) {
        if (item.score < filters.scoreRange[0] || item.score > filters.scoreRange[1]) {
          return false;
        }
      }


      // Score range filter
      if (item.score !== undefined) {
        if (item.score < filters.scoreRange[0] || item.score > filters.scoreRange[1]) {
          return false;
        }
      }

      // Progress range filter
      if (item.progress !== undefined) {
        if (item.progress < filters.progressRange[0] || item.progress > filters.progressRange[1]) {
          return false;
        }
      }

      // Date range filters
      if (filters.dateRange.created_start || filters.dateRange.created_end) {
        const createdDate = new Date(item.createdAt);
        if (filters.dateRange.created_start) {
          const startDate = new Date(filters.dateRange.created_start);
          if (createdDate < startDate) return false;
        }
        if (filters.dateRange.created_end) {
          const endDate = new Date(filters.dateRange.created_end);
          if (createdDate > endDate) return false;
        }
      }

      if (filters.dateRange.updated_start || filters.dateRange.updated_end) {
        const updatedDate = new Date(item.updatedAt);
        if (filters.dateRange.updated_start) {
          const startDate = new Date(filters.dateRange.updated_start);
          if (updatedDate < startDate) return false;
        }
        if (filters.dateRange.updated_end) {
          const endDate = new Date(filters.dateRange.updated_end);
          if (updatedDate > endDate) return false;
        }
      }

      if (item.dueAt && (filters.dateRange.due_start || filters.dateRange.due_end)) {
        const dueDate = new Date(item.dueAt);
        if (filters.dateRange.due_start) {
          const startDate = new Date(filters.dateRange.due_start);
          if (dueDate < startDate) return false;
        }
        if (filters.dateRange.due_end) {
          const endDate = new Date(filters.dateRange.due_end);
          if (dueDate > endDate) return false;
        }
      }

      return true;
    });

    // Apply sorting
    result.sort((a, b) => {
      const aValue = a[filters.sortBy];
      const bValue = b[filters.sortBy];

      if (aValue === bValue) return 0;

      const comparison = aValue < bValue ? -1 : 1;
      return filters.sortOrder === 'asc' ? comparison : -comparison;
    });

    return result;
  }, [data, filters]);

  const resetFilters = useCallback(() => {
    setFilters({
      search: '',
      status: [],
      priority: [],
      owner: [],
      category: [],
      severity: [],
      assignee: [],
      decidedBy: [],
      scoreRange: [0, 100],
      progressRange: [0, 100],
      dateRange: {},
      searchMode: 'contains',
      searchFields: [],
      caseSensitive: false,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    });
  }, []);

  const updateFilter = useCallback((key: keyof FilterState, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  }, []);

  const applyFilter = useCallback((filterData: Partial<FilterState>) => {
    setFilters(prev => ({
      ...prev,
      ...filterData
    }));
  }, []);

  const saveFilter = useCallback(async (
    name: string,
    description?: string,
    isPublic: boolean = false,
    projectId?: string
  ) => {
    setIsLoading(true);
    try {
      // TODO: Implement API call to save filter
      const response = await fetch(`/api/v1/projects/${projectId}/saved-filters`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // Add auth headers
        },
        body: JSON.stringify({
          name,
          description,
          filter_data: filters,
          is_public: isPublic
        })
      });

      if (response.ok) {
        const savedFilter = await response.json();
        setSavedFilters(prev => [...prev, savedFilter]);
        return savedFilter;
      }
    } catch (error) {
      console.error('Failed to save filter:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [filters]);

  const loadSavedFilters = useCallback(async (projectId?: string) => {
    setIsLoading(true);
    try {
      // TODO: Implement API call to load saved filters
      const response = await fetch(`/api/v1/projects/${projectId}/saved-filters`, {
        headers: {
          // Add auth headers
        }
      });

      if (response.ok) {
        const filters = await response.json();
        setSavedFilters(filters);
      }
    } catch (error) {
      console.error('Failed to load saved filters:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const applySavedFilter = useCallback((savedFilter: SavedFilter) => {
    setFilters(savedFilter.filter_data);
  }, []);

  const deleteSavedFilter = useCallback(async (filterId: string) => {
    setIsLoading(true);
    try {
      // TODO: Implement API call to delete filter
      const response = await fetch(`/api/v1/saved-filters/${filterId}`, {
        method: 'DELETE',
        headers: {
          // Add auth headers
        }
      });

      if (response.ok) {
        setSavedFilters(prev => prev.filter(f => f.id !== filterId));
      }
    } catch (error) {
      console.error('Failed to delete filter:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const getActiveFilterCount = useCallback(() => {
    let count = 0;
    if (filters.search) count++;
    if (filters.status.length > 0) count++;
    if (filters.priority.length > 0) count++;
    if (filters.owner.length > 0) count++;
    if (filters.category.length > 0) count++;
    if (filters.severity.length > 0) count++;
    if (filters.assignee.length > 0) count++;
    if (filters.decidedBy.length > 0) count++;
    if (filters.scoreRange[0] > 0 || filters.scoreRange[1] < 100) count++;
    if (filters.progressRange[0] > 0 || filters.progressRange[1] < 100) count++;
    if (Object.values(filters.dateRange).some(Boolean)) count++;
    if (filters.searchMode !== 'contains') count++;
    if (filters.searchFields.length > 0) count++;
    if (filters.caseSensitive) count++;
    if (filters.sortBy !== 'createdAt' || filters.sortOrder !== 'desc') count++;
    return count;
  }, [filters]);

  return {
    filters,
    setFilters,
    filteredData,
    resetFilters,
    updateFilter,
    applyFilter,
    savedFilters,
    saveFilter,
    loadSavedFilters,
    applySavedFilter,
    deleteSavedFilter,
    isLoading,
    activeFilterCount: getActiveFilterCount()
  };
}
