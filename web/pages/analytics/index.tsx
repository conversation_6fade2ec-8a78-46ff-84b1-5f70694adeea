/**
 * Analytics Dashboard Page
 */

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { Dashboard } from '../../lib/types/analytics';
import { dashboardApi, withErrorHandling } from '../../lib/api/analytics';
import DashboardView from '../../components/analytics/DashboardView';

const AnalyticsPage: React.FC = () => {
  const router = useRouter();
  const [dashboards, setDashboards] = useState<Dashboard[]>([]);
  const [selectedDashboard, setSelectedDashboard] = useState<Dashboard | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);

  // Load dashboards
  const loadDashboards = async () => {
    setLoading(true);
    setError(null);

    try {
      const data = await withErrorHandling(
        () => dashboardApi.getDashboards(),
        'Failed to load dashboards'
      );

      if (data) {
        setDashboards(data);
        
        // Select default dashboard or first dashboard
        const defaultDashboard = data.find(d => d.is_default) || data[0];
        if (defaultDashboard) {
          setSelectedDashboard(defaultDashboard);
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  // Load dashboards on mount
  useEffect(() => {
    loadDashboards();
  }, []);

  // Handle dashboard selection
  const handleDashboardSelect = (dashboard: Dashboard) => {
    setSelectedDashboard(dashboard);
    router.push(`/analytics?dashboard=${dashboard.id}`, undefined, { shallow: true });
  };

  // Handle create dashboard
  const handleCreateDashboard = () => {
    setShowCreateModal(true);
  };

  // Handle dashboard change
  const handleDashboardChange = (dashboard: Dashboard) => {
    setSelectedDashboard(dashboard);
    setDashboards(prev => prev.map(d => d.id === dashboard.id ? dashboard : d));
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Head>
          <title>Analytics Dashboard - RAID</title>
        </Head>
        
        <div className="flex items-center justify-center h-64">
          <div className="flex flex-col items-center space-y-2">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <p className="text-sm text-gray-500">Loading analytics...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Head>
          <title>Analytics Dashboard - RAID</title>
        </Head>
        
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="text-red-500 mb-2">
              <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <p className="text-sm text-gray-600 mb-4">Failed to load analytics</p>
            <p className="text-xs text-gray-400 mb-4">{error}</p>
            <button
              onClick={loadDashboards}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Head>
        <title>Analytics Dashboard - RAID</title>
        <meta name="description" content="Advanced analytics and data visualization for RAID management" />
      </Head>

      <div className="flex h-screen">
        {/* Sidebar */}
        <div className="w-64 bg-white shadow-sm border-r border-gray-200 flex flex-col">
          {/* Header */}
          <div className="p-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Analytics</h2>
            <p className="text-sm text-gray-600">Data visualization & insights</p>
          </div>

          {/* Dashboard List */}
          <div className="flex-1 overflow-y-auto">
            <div className="p-4">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-sm font-medium text-gray-700">Dashboards</h3>
                <button
                  onClick={handleCreateDashboard}
                  className="text-blue-600 hover:text-blue-700"
                  title="Create new dashboard"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                </button>
              </div>

              {dashboards.length === 0 ? (
                <div className="text-center py-8">
                  <svg className="w-8 h-8 mx-auto text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                  <p className="text-sm text-gray-500">No dashboards yet</p>
                  <button
                    onClick={handleCreateDashboard}
                    className="mt-2 text-sm text-blue-600 hover:text-blue-700"
                  >
                    Create your first dashboard
                  </button>
                </div>
              ) : (
                <div className="space-y-1">
                  {dashboards.map((dashboard) => (
                    <button
                      key={dashboard.id}
                      onClick={() => handleDashboardSelect(dashboard)}
                      className={`w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${
                        selectedDashboard?.id === dashboard.id
                          ? 'bg-blue-100 text-blue-700 border border-blue-200'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <span className="font-medium truncate">{dashboard.name}</span>
                        {dashboard.is_default && (
                          <span className="text-xs bg-green-100 text-green-700 px-1 rounded">
                            Default
                          </span>
                        )}
                      </div>
                      {dashboard.description && (
                        <p className="text-xs text-gray-500 mt-1 truncate">
                          {dashboard.description}
                        </p>
                      )}
                      <div className="flex items-center justify-between mt-1">
                        <span className="text-xs text-gray-400">
                          {dashboard.widget_count || 0} widgets
                        </span>
                        {dashboard.is_public ? (
                          <svg className="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        ) : (
                          <svg className="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                          </svg>
                        )}
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Footer */}
          <div className="p-4 border-t border-gray-200">
            <div className="text-xs text-gray-500">
              <p>Analytics powered by</p>
              <p className="font-medium">RAID Dashboard System</p>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {selectedDashboard ? (
            <div className="flex-1 p-6 overflow-auto">
              <DashboardView
                dashboardId={selectedDashboard.id}
                onDashboardChange={handleDashboardChange}
              />
            </div>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <svg className="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Welcome to Analytics</h3>
                <p className="text-gray-600 mb-4">
                  Create your first dashboard to start visualizing your RAID data
                </p>
                <button
                  onClick={handleCreateDashboard}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Create Dashboard
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Create Dashboard Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-medium mb-4">Create Dashboard</h3>
            <p className="text-gray-600 mb-4">Dashboard creation modal coming soon...</p>
            <div className="flex justify-end space-x-2">
              <button
                onClick={() => setShowCreateModal(false)}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AnalyticsPage;
