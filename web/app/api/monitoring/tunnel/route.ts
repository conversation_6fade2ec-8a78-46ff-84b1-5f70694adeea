import { NextRequest, NextResponse } from 'next/server';

/**
 * Sentry tunnel route for better privacy and ad-blocker bypass
 * This proxies Sentry requests through your domain
 */
export async function POST(req: NextRequest) {
  try {
    const envelope = await req.text();
    const pieces = envelope.split('\n');
    const header = JSON.parse(pieces[0]);
    
    // Extract DSN from the header
    const dsn = header.dsn;
    if (!dsn) {
      return NextResponse.json({ error: 'No DSN found' }, { status: 400 });
    }

    // Parse DSN to get the project ID and host
    const dsnMatch = dsn.match(/https:\/\/(.+)@(.+)\/(.+)/);
    if (!dsnMatch) {
      return NextResponse.json({ error: 'Invalid DSN format' }, { status: 400 });
    }

    const [, key, host, projectId] = dsnMatch;
    const sentryUrl = `https://${host}/api/${projectId}/envelope/`;

    // Forward the request to Sentry
    const response = await fetch(sentryUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-sentry-envelope',
        'X-Sentry-Auth': `Sentry sentry_version=7, sentry_key=${key}, sentry_client=sentry.javascript.nextjs/7.0.0`,
      },
      body: envelope,
    });

    if (!response.ok) {
      console.error('Failed to forward to Sentry:', response.status, response.statusText);
      return NextResponse.json(
        { error: 'Failed to forward to Sentry' },
        { status: response.status }
      );
    }

    return new NextResponse(null, { status: 200 });
  } catch (error) {
    console.error('Sentry tunnel error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
