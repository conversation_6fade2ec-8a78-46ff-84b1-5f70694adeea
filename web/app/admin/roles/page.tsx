'use client';
import * as React from 'react';
import { useAuth } from '@clerk/nextjs';
import { useAuthFetch } from '@/lib/client-auth';
import { Button } from '@/components/ui/button';

export default function RolesAdminPage() {
  const { getToken, isLoaded } = useAuth();
  const authFetch = useAuthFetch();
  const [me, setMe] = React.useState<any>(null);
  const [users, setUsers] = React.useState<any[]>([]);
  const api = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5050';

  React.useEffect(() => {
    (async () => {
      const token = await getToken();
      const res = await fetch(`${api}/me`, { headers: token ? { Authorization: `Bearer ${token}` } : {} });
      const meJson = await res.json();
      setMe(meJson);
      if (meJson.role === 'Admin') {
        const list = await authFetch(`${api}/users`);
        setUsers(await list.json());
      }
    })();
  }, [isLoaded]);

  if (!me) return <div className="text-sm text-gray-500">Loading…</div>;
  if (me.role !== 'Admin') return <div className="text-sm text-gray-500">You need Admin role to view this page.</div>;

  async function changeRole(id: string, role: string) {
    await authFetch(`${api}/users/${id}`, { method: 'PATCH', body: JSON.stringify({ role }) });
    setUsers(users.map(u => u.id === id ? { ...u, role } : u));
  }

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">Role Management</h2>
      <table className="min-w-full text-sm">
        <thead>
          <tr className="text-left text-gray-500"><th className="p-2">Name</th><th className="p-2">Email</th><th className="p-2">Role</th><th className="p-2">Action</th></tr>
        </thead>
        <tbody>
          {users.map(u => (
            <tr key={u.id} className="border-t">
              <td className="p-2">{u.name || u.id}</td>
              <td className="p-2">{u.email || '-'}</td>
              <td className="p-2">{u.role}</td>
              <td className="p-2">
                <select className="rounded-xl border px-2 py-1" value={u.role} onChange={(e) => changeRole(u.id, e.target.value)}>
                  <option>Viewer</option>
                  <option>Contributor</option>
                  <option>PM</option>
                  <option>Admin</option>
                </select>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      <div className="text-xs text-gray-400">Users appear here after they hit /me once while signed in.</div>
    </div>
  );
}
