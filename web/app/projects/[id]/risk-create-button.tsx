'use client';
import * as React from 'react';
import { Modal } from '@/components/ui/modal';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { useAuthFetch } from '@/lib/client-auth';
import { useToast } from '@/components/ui/toast';

export default function RiskCreateButton({ projectId }: { projectId: string }) {
  const [open, setOpen] = React.useState(false);
  const [title, setTitle] = React.useState('');
  const [trigger, setTrigger] = React.useState('');
  const [owner, setOwner] = React.useState('');
  const [dueAt, setDueAt] = React.useState('');
  const [response, setResponse] = React.useState('');
  const [probability, setProbability] = React.useState<number>(0.3);
  const [impact, setImpact] = React.useState<number>(3);
  const [status, setStatus] = React.useState<'Open' | 'In progress' | 'Resolved'>('Open');
  const [notifyAnyway, setNotifyAnyway] = React.useState(false);
  const [preview, setPreview] = React.useState<any>(null);
  const [busy, setBusy] = React.useState(false);
  const { addToast } = useToast();
  const [error, setError] = React.useState<string | null>(null);
  const authFetch = useAuthFetch();
  const api = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5050';
  const score = (Number(probability) || 0) * (Number(impact) || 0);

  React.useEffect(() => {
    const controller = new AbortController();
    (async () => {
      try {
        const res = await fetch(`${api}/notifications/preview/high-risk`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ projectId, title, probability, impact }),
          signal: controller.signal,
        });
        setPreview(await res.json());
      } catch {}
    })();
    return () => controller.abort();
  }, [api, projectId, title, probability, impact]);

  async function createRisk() {
    setBusy(true); setError(null);
    try {
      const body = { title, trigger, probability, impact, response, owner, status, dueAt: dueAt ? new Date(dueAt) : undefined, notifyAnyway };
      const res = await authFetch(`${api}/projects/${projectId}/risks`, { method: 'POST', body: JSON.stringify(body) });
      if (!res.ok) throw new Error(await res.text());
      setOpen(false);
      addToast({ variant: 'success', title: 'Risk created', description: preview?.willNotify || notifyAnyway ? 'Notification sent to Slack/Teams.' : 'Saved without alert.' });
      location.reload();
    } catch (e: any) {
      setError(e?.message || 'Failed to create risk');
      addToast({ variant: 'error', title: 'Failed to create risk', description: String(e?.message || '') });
    } finally {
      setBusy(false);
    }
  }

  return (
    <>
      <Button size="sm" onClick={() => setOpen(true)}>New Risk</Button>
      <Modal open={open} onClose={() => setOpen(false)} title="Create Risk">
        <div className="space-y-4">
          {error && <div className="rounded-xl border border-red-200 bg-red-50 p-2 text-sm text-red-700">{error}</div>}

          <div className="grid grid-cols-1 gap-3 md:grid-cols-2">
            <div>
              <label className="text-sm text-gray-600">Title</label>
              <Input value={title} onChange={(e) => setTitle(e.target.value)} placeholder="e.g., Data feed outages could delay analytics" />
            </div>
            <div>
              <label className="text-sm text-gray-600">Trigger</label>
              <Input value={trigger} onChange={(e) => setTrigger(e.target.value)} placeholder="e.g., Upstream API rate limits" />
            </div>
            <div>
              <label className="text-sm text-gray-600">Owner</label>
              <Input value={owner} onChange={(e) => setOwner(e.target.value)} placeholder="e.g., Priya" />
            </div>
            <div>
              <label className="text-sm text-gray-600">Due date</label>
              <Input type="date" value={dueAt} onChange={(e) => setDueAt(e.target.value)} />
            </div>
            <div>
              <label className="text-sm text-gray-600">Probability (0–1)</label>
              <Input type="number" step="0.1" min="0" max="1" value={probability} onChange={(e) => setProbability(Number(e.target.value))} />
            </div>
            <div>
              <label className="text-sm text-gray-600">Impact (1–5)</label>
              <Input type="number" step="1" min="1" max="5" value={impact} onChange={(e) => setImpact(Number(e.target.value))} />
            </div>
            <div className="md:col-span-2">
              <label className="text-sm text-gray-600">Response / Mitigation</label>
              <Textarea rows={3} value={response} onChange={(e) => setResponse(e.target.value)} placeholder="e.g., Cache results; implement backoff; add monitoring" />
            </div>
          </div>

          <div className="rounded-2xl border p-3">
            <div className="flex items-center justify-between">
              <div className="font-medium">Live scoring</div>
              <div className="text-sm text-gray-500">score = probability × impact</div>
            </div>
            <div className="mt-2 flex items-center gap-3">
              <div className="rounded-xl bg-gray-100 px-3 py-1 text-sm">Score: <span className="font-semibold">{score.toFixed(1)}</span></div>
              {preview && <div className={"rounded-xl px-3 py-1 text-sm " + ((preview.willNotify || notifyAnyway) ? "bg-red-100 text-red-700" : "bg-gray-100 text-gray-700")}>
                {(preview.willNotify || notifyAnyway) ? (notifyAnyway ? 'Notification will be sent (forced)' : `Notification will be sent (≥ ${preview.threshold})`) : `Below notify threshold (${preview.threshold})`}
              </div>}
            </div>
          </div>

          <div className="grid grid-cols-1 gap-3 md:grid-cols-2">
            <div className="rounded-2xl border p-3">
              <div className="mb-2 font-semibold">Slack preview</div>
              <pre className="whitespace-pre-wrap text-sm">{preview?.slackText || '—'}</pre>
            </div>
            <div className="rounded-2xl border p-3">
              <div className="mb-2 font-semibold">Teams preview</div>
              <pre className="whitespace-pre-wrap text-sm">{preview ? JSON.stringify(preview.teamsCard, null, 2) : '—'}</pre>
            </div>
          </div>

          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setOpen(false)}>Cancel</Button>
            <Button onClick={createRisk} disabled={busy || !title.trim()}>Create</Button>
          </div>
        </div>
      </Modal>
    </>
  );
}
