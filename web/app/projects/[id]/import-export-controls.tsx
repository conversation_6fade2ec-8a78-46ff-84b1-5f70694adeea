'use client';
import * as React from 'react';
import { Button } from '@/components/ui/button';

export default function ImportExportControls({ projectId }: { projectId: string }) {
  const api = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5050';

  async function exportXlsx() {
    const res = await fetch(`${api}/projects/${projectId}/export?format=xlsx`, { cache: 'no-store' });
    const blob = await res.blob();
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a'); a.href = url; a.download = 'raid-export.xlsx'; a.click();
    URL.revokeObjectURL(url);
  }

  async function exportRisksCsv() {
    const res = await fetch(`${api}/projects/${projectId}/export?format=csv&category=risks`, { cache: 'no-store' });
    const blob = await res.blob();
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a'); a.href = url; a.download = 'risks.csv'; a.click();
    URL.revokeObjectURL(url);
  }

  async function importRisksJson(e: React.ChangeEvent<HTMLInputElement>) {
    const file = e.target.files?.[0];
    if (!file) return;
    const text = await file.text();
    const items = text.trim().startsWith('[') ? JSON.parse(text) : [];
    await fetch(`${api}/projects/${projectId}/import?category=risks`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(items),
    });
    location.reload();
  }

  return (
    <div className="flex gap-2">
      <Button variant="outline" size="sm" onClick={exportXlsx}>Export XLSX</Button>
      <Button variant="outline" size="sm" onClick={exportRisksCsv}>Export Risks CSV</Button>
      <label className="text-sm underline cursor-pointer">
        Import Risks (JSON)
        <input type="file" accept="application/json" className="hidden" onChange={importRisksJson} />
      </label>
    </div>
  );
}
