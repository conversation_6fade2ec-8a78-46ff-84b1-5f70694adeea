'use client';
import * as React from 'react';
import { useAuthFetch } from '@/lib/client-auth';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/toast';
import { useRouter } from 'next/navigation';

export default function ConvertToIssueButton({ riskId, projectId }: { riskId: string; projectId: string }) {
  const authFetch = useAuthFetch();
  const router = useRouter();
  const api = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5050';
  const [busy, setBusy] = React.useState(false);
  const { addToast } = useToast();
  const [err, setErr] = React.useState<string | null>(null);

  async function convert() {
    setBusy(true); setErr(null);
    try {
      const res = await authFetch(`${api}/risks/${riskId}/convert-to-issue`, { method: 'POST' });
      if (!res.ok) throw new Error(await res.text());
      addToast({ variant: 'success', title: 'Converted to Issue' });
      // go back to project issues list
      router.push(`/projects/${projectId}#issues`);
    } catch (e: any) {
      setErr(e?.message || 'Failed to convert');
      addToast({ variant: 'error', title: 'Conversion failed', description: String(e?.message || '') });
    } finally {
      setBusy(false);
    }
  }

  return (
    <div className="space-y-2">
      {err && <div className="rounded-xl border border-red-200 bg-red-50 p-2 text-sm text-red-700">{err}</div>}
      <Button onClick={convert} disabled={busy}>Convert Risk → Issue</Button>
    </div>
  );
}
