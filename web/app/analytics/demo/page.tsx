/**
 * Analytics Demo Page - Shows working charts without backend
 */

'use client';

import React, { useState } from 'react';
import { ChartType, WidgetType } from '../../../lib/types/analytics';
import ChartWrapper from '../../../components/analytics/charts/ChartWrapper';

const AnalyticsDemoPage: React.FC = () => {
  const [selectedChart, setSelectedChart] = useState<ChartType>(ChartType.PIE);

  // Sample data for different chart types
  const sampleData = {
    pie: {
      data: [
        { label: 'High Priority', value: 25, percentage: 35.7, color: '#ef4444' },
        { label: 'Medium Priority', value: 30, percentage: 42.9, color: '#f59e0b' },
        { label: 'Low Priority', value: 15, percentage: 21.4, color: '#10b981' },
      ],
      metadata: {
        chart_type: 'pie',
        total_value: 70,
        point_count: 3,
        processing_time_ms: 45,
      },
    },
    bar: {
      data: [
        { x: 'Risks', y: 15, count: 15, avg: 1.0, color: '#ef4444' },
        { x: 'Actions', y: 23, count: 23, avg: 1.0, color: '#3b82f6' },
        { x: 'Issues', y: 18, count: 18, avg: 1.0, color: '#f59e0b' },
        { x: 'Decisions', y: 12, count: 12, avg: 1.0, color: '#10b981' },
      ],
      metadata: {
        chart_type: 'bar',
        total_value: 68,
        point_count: 4,
        processing_time_ms: 32,
      },
    },
    line: {
      data: [
        { x: '2024-01-01', y: 10, count: 10, avg: 1.0 },
        { x: '2024-01-02', y: 15, count: 15, avg: 1.0 },
        { x: '2024-01-03', y: 12, count: 12, avg: 1.0 },
        { x: '2024-01-04', y: 18, count: 18, avg: 1.0 },
        { x: '2024-01-05', y: 22, count: 22, avg: 1.0 },
        { x: '2024-01-06', y: 20, count: 20, avg: 1.0 },
        { x: '2024-01-07', y: 25, count: 25, avg: 1.0 },
      ],
      metadata: {
        chart_type: 'line',
        total_value: 122,
        point_count: 7,
        processing_time_ms: 28,
      },
    },
    scatter: {
      data: [
        { x: 5, y: 10, size: 15, group: 'High Priority', color: '#ef4444' },
        { x: 8, y: 15, size: 20, group: 'High Priority', color: '#ef4444' },
        { x: 12, y: 8, size: 12, group: 'Medium Priority', color: '#f59e0b' },
        { x: 15, y: 18, size: 25, group: 'Medium Priority', color: '#f59e0b' },
        { x: 20, y: 12, size: 18, group: 'Low Priority', color: '#10b981' },
        { x: 25, y: 22, size: 30, group: 'Low Priority', color: '#10b981' },
      ],
      metadata: {
        chart_type: 'scatter',
        correlation: 0.75,
        point_count: 6,
        processing_time_ms: 38,
      },
    },
    gauge: {
      data: {
        value: 75,
        percentage: 75,
        normalized: 0.75,
        status: 'good',
      },
      metadata: {
        min_value: 0,
        max_value: 100,
        thresholds: {
          warning: 60,
          critical: 80,
        },
        chart_type: 'gauge',
      },
    },
  };

  const chartTypes = [
    { type: ChartType.PIE, label: 'Pie Chart' },
    { type: ChartType.BAR, label: 'Bar Chart' },
    { type: ChartType.LINE, label: 'Line Chart' },
    { type: ChartType.SCATTER, label: 'Scatter Plot' },
    { type: ChartType.GAUGE, label: 'Gauge Chart' },
    { type: ChartType.DONUT, label: 'Donut Chart' },
    { type: ChartType.AREA, label: 'Area Chart' },
  ];

  const getCurrentData = () => {
    switch (selectedChart) {
      case ChartType.PIE:
      case ChartType.DONUT:
        return sampleData.pie;
      case ChartType.BAR:
        return sampleData.bar;
      case ChartType.LINE:
      case ChartType.AREA:
        return sampleData.line;
      case ChartType.SCATTER:
        return sampleData.scatter;
      case ChartType.GAUGE:
        return sampleData.gauge;
      default:
        return sampleData.pie;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Analytics Demo</h1>
          <p className="text-gray-600">
            Interactive demonstration of the analytics dashboard system with sample data
          </p>
        </div>

        {/* Chart Type Selector */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Chart Types</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-2">
            {chartTypes.map(({ type, label }) => (
              <button
                key={type}
                onClick={() => setSelectedChart(type)}
                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  selectedChart === type
                    ? 'bg-blue-100 text-blue-700 border border-blue-200'
                    : 'bg-gray-50 text-gray-700 hover:bg-gray-100 border border-gray-200'
                }`}
              >
                {label}
              </button>
            ))}
          </div>
        </div>

        {/* Chart Display */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">
              {chartTypes.find(ct => ct.type === selectedChart)?.label}
            </h2>
            <div className="text-sm text-gray-500">
              Sample data visualization
            </div>
          </div>
          
          <div className="h-96 w-full">
            <ChartWrapper
              chartType={selectedChart}
              data={getCurrentData()}
              width={800}
              height={384}
              loading={false}
            />
          </div>
        </div>

        {/* Features Overview */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center mb-3">
              <svg className="w-6 h-6 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              <h3 className="text-lg font-semibold text-gray-900">Interactive Charts</h3>
            </div>
            <p className="text-gray-600">
              Multiple chart types including pie, bar, line, scatter, and gauge charts with interactive tooltips and legends.
            </p>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center mb-3">
              <svg className="w-6 h-6 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
              </svg>
              <h3 className="text-lg font-semibold text-gray-900">Drag & Drop Layout</h3>
            </div>
            <p className="text-gray-600">
              Responsive grid layout with drag-and-drop functionality for customizing dashboard widget positions.
            </p>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center mb-3">
              <svg className="w-6 h-6 text-purple-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <h3 className="text-lg font-semibold text-gray-900">Real-time Updates</h3>
            </div>
            <p className="text-gray-600">
              Live data processing with intelligent caching and automatic refresh capabilities for up-to-date insights.
            </p>
          </div>
        </div>

        {/* Implementation Status */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-3">Implementation Status</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-blue-800 mb-2">✅ Completed</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Complete backend API with 20+ endpoints</li>
                <li>• Database models and services</li>
                <li>• Chart components (Pie, Bar, Line, Scatter, Gauge)</li>
                <li>• Dashboard grid layout system</li>
                <li>• React Query integration</li>
                <li>• TypeScript types and API client</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-blue-800 mb-2">🚧 In Progress</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Widget creation and editing modals</li>
                <li>• Dashboard creation interface</li>
                <li>• Advanced chart configurations</li>
                <li>• Trend analysis components</li>
                <li>• Real-time data connections</li>
                <li>• Heatmap and advanced chart types</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsDemoPage;
