/**
 * Analytics Dashboard Page (App Router)
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { Dashboard } from '../../lib/types/analytics';
import { dashboardApi, widgetApi, withErrorHandling } from '../../lib/api/analytics';
import DashboardView from '../../components/analytics/DashboardView';

const AnalyticsPage: React.FC = () => {
  const searchParams = useSearchParams();
  const [dashboards, setDashboards] = useState<Dashboard[]>([]);
  const [selectedDashboard, setSelectedDashboard] = useState<Dashboard | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [isCreatingDemo, setIsCreatingDemo] = useState(false);
  const [showDemoMode, setShowDemoMode] = useState(false);

  // Create demo dashboard with sample data
  const createDemoDashboard = async () => {
    setIsCreatingDemo(true);
    try {
      const demoDashboard = {
        name: "RAID Overview Dashboard",
        description: "A comprehensive overview of your RAID items with key metrics and trends",
        is_default: true,
        is_public: false,
        layout_config: {
          cols: 12,
          rowHeight: 150,
          margin: [10, 10]
        },
        refresh_interval: 300000, // 5 minutes
        filters: {}
      };

      const dashboard = await withErrorHandling(
        () => dashboardApi.createDashboard(demoDashboard),
        'Failed to create demo dashboard'
      );

      if (dashboard) {
        // Create sample widgets
        const widgets = [
          {
            dashboard_id: dashboard.id,
            name: "Risk Priority Distribution",
            widget_type: "chart",
            chart_type: "pie",
            data_source: "risks",
            position: { x: 0, y: 0, w: 6, h: 2 },
            config: {
              field: "priority",
              title: "Risk Priority Distribution",
              colors: ["#ef4444", "#f59e0b", "#10b981"]
            }
          },
          {
            dashboard_id: dashboard.id,
            name: "RAID Items Overview",
            widget_type: "chart",
            chart_type: "bar",
            data_source: "mixed",
            position: { x: 6, y: 0, w: 6, h: 2 },
            config: {
              title: "RAID Items Count",
              categories: ["Risks", "Actions", "Issues", "Decisions"]
            }
          },
          {
            dashboard_id: dashboard.id,
            name: "Action Status Tracking",
            widget_type: "chart",
            chart_type: "line",
            data_source: "actions",
            position: { x: 0, y: 2, w: 12, h: 2 },
            config: {
              field: "status",
              title: "Action Completion Trend",
              timeRange: "30d"
            }
          }
        ];

        // Create widgets
        for (const widgetData of widgets) {
          await withErrorHandling(
            () => widgetApi.createWidget(widgetData),
            'Failed to create demo widget'
          );
        }

        // Reload dashboards to include the new one
        await loadDashboards();
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create demo dashboard');
    } finally {
      setIsCreatingDemo(false);
    }
  };

  // Load dashboards
  const loadDashboards = async () => {
    setLoading(true);
    setError(null);

    try {
      const data = await withErrorHandling(
        () => dashboardApi.getDashboards(),
        'Failed to load dashboards'
      );

      if (data) {
        setDashboards(data);

        // Check for dashboard ID in URL params
        const dashboardId = searchParams?.get('dashboard');
        if (dashboardId) {
          const dashboard = data.find(d => d.id === dashboardId);
          if (dashboard) {
            setSelectedDashboard(dashboard);
            return;
          }
        }

        // Select default dashboard or first dashboard
        const defaultDashboard = data.find(d => d.is_default) || data[0];
        if (defaultDashboard) {
          setSelectedDashboard(defaultDashboard);
        }
      }
    } catch (err) {
      console.error('Dashboard loading error:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  // Load dashboards on mount
  useEffect(() => {
    loadDashboards();
  }, []);

  // Handle dashboard selection
  const handleDashboardSelect = (dashboard: Dashboard) => {
    setSelectedDashboard(dashboard);
    // Update URL without page reload
    const url = new URL(window.location.href);
    url.searchParams.set('dashboard', dashboard.id);
    window.history.pushState({}, '', url.toString());
  };

  // Handle create dashboard
  const handleCreateDashboard = () => {
    setShowCreateModal(true);
  };

  // Handle dashboard change
  const handleDashboardChange = (dashboard: Dashboard) => {
    setSelectedDashboard(dashboard);
    setDashboards(prev => prev.map(d => d.id === dashboard.id ? dashboard : d));
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="flex items-center justify-center h-64">
          <div className="flex flex-col items-center space-y-2">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <p className="text-sm text-gray-500">Loading analytics...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error && !showDemoMode) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="flex items-center justify-center h-64">
          <div className="text-center max-w-md">
            <div className="text-red-500 mb-2">
              <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <p className="text-sm text-gray-600 mb-2">Failed to load analytics</p>
            <p className="text-xs text-gray-400 mb-4">{error}</p>
            <div className="space-y-2">
              <button
                onClick={loadDashboards}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Try Again
              </button>
              <button
                onClick={() => setShowDemoMode(true)}
                className="w-full px-4 py-2 text-blue-600 border border-blue-600 rounded-md hover:bg-blue-50"
              >
                View Demo Mode
              </button>
            </div>
            <p className="text-xs text-gray-500 mt-3">
              Demo mode shows sample charts without connecting to the backend
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Demo mode - show sample analytics without backend
  if (showDemoMode) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="flex h-screen">
          {/* Demo Sidebar */}
          <div className="w-64 bg-white shadow-sm border-r border-gray-200 flex flex-col">
            <div className="p-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Analytics Demo</h2>
              <p className="text-sm text-gray-600">Sample dashboard preview</p>
            </div>
            <div className="flex-1 p-4">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
                <h3 className="text-sm font-medium text-blue-900 mb-1">Demo Mode</h3>
                <p className="text-xs text-blue-700">
                  This shows sample analytics data. Connect to your backend to see real data.
                </p>
              </div>
              <button
                onClick={() => setShowDemoMode(false)}
                className="w-full px-3 py-2 text-sm text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Back to Analytics
              </button>
            </div>
          </div>

          {/* Demo Content */}
          <div className="flex-1 p-6 overflow-auto">
            <div className="mb-6">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">RAID Analytics Demo</h1>
              <p className="text-gray-600">Sample dashboard showing analytics capabilities</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
              {/* Sample Chart 1 */}
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Risk Priority Distribution</h3>
                <div className="h-64 flex items-center justify-center bg-gray-50 rounded">
                  <div className="text-center">
                    <svg className="w-16 h-16 mx-auto text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    <p className="text-sm text-gray-500">Pie Chart</p>
                    <p className="text-xs text-gray-400">High: 35% | Medium: 45% | Low: 20%</p>
                  </div>
                </div>
              </div>

              {/* Sample Chart 2 */}
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">RAID Items Overview</h3>
                <div className="h-64 flex items-center justify-center bg-gray-50 rounded">
                  <div className="text-center">
                    <svg className="w-16 h-16 mx-auto text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    <p className="text-sm text-gray-500">Bar Chart</p>
                    <p className="text-xs text-gray-400">Risks: 15 | Actions: 23 | Issues: 8 | Decisions: 12</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Sample Chart 3 */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Trend Analysis</h3>
              <div className="h-64 flex items-center justify-center bg-gray-50 rounded">
                <div className="text-center">
                  <svg className="w-16 h-16 mx-auto text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                  </svg>
                  <p className="text-sm text-gray-500">Line Chart</p>
                  <p className="text-xs text-gray-400">30-day trend showing completion rates</p>
                </div>
              </div>
            </div>

            <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="text-sm font-medium text-blue-900 mb-2">Ready to get started?</h4>
              <p className="text-sm text-blue-700 mb-3">
                This demo shows what your analytics could look like. To see real data, ensure your backend is running and create some RAID items.
              </p>
              <a
                href="/analytics/demo"
                className="inline-flex items-center px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                View Interactive Demo
                <svg className="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex h-screen">
        {/* Sidebar */}
        <div className="w-64 bg-white shadow-sm border-r border-gray-200 flex flex-col">
          {/* Header */}
          <div className="p-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Analytics</h2>
            <p className="text-sm text-gray-600">Data visualization & insights</p>
          </div>

          {/* Dashboard List */}
          <div className="flex-1 overflow-y-auto">
            <div className="p-4">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-sm font-medium text-gray-700">Dashboards</h3>
                <button
                  onClick={handleCreateDashboard}
                  className="text-blue-600 hover:text-blue-700"
                  title="Create new dashboard"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                </button>
              </div>

              {dashboards.length === 0 ? (
                <div className="text-center py-8">
                  <svg className="w-8 h-8 mx-auto text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                  <p className="text-sm text-gray-500 mb-3">No dashboards yet</p>
                  <div className="space-y-2">
                    <button
                      onClick={createDemoDashboard}
                      disabled={isCreatingDemo}
                      className="block w-full px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isCreatingDemo ? (
                        <span className="flex items-center justify-center">
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Creating Demo...
                        </span>
                      ) : (
                        'Create Demo Dashboard'
                      )}
                    </button>
                    <button
                      onClick={handleCreateDashboard}
                      className="block w-full px-3 py-2 text-sm text-blue-600 border border-blue-600 rounded-md hover:bg-blue-50"
                    >
                      Create Custom Dashboard
                    </button>
                  </div>
                </div>
              ) : (
                <div className="space-y-1">
                  {dashboards.map((dashboard) => (
                    <button
                      key={dashboard.id}
                      onClick={() => handleDashboardSelect(dashboard)}
                      className={`w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${
                        selectedDashboard?.id === dashboard.id
                          ? 'bg-blue-100 text-blue-700 border border-blue-200'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <span className="font-medium truncate">{dashboard.name}</span>
                        {dashboard.is_default && (
                          <span className="text-xs bg-green-100 text-green-700 px-1 rounded">
                            Default
                          </span>
                        )}
                      </div>
                      {dashboard.description && (
                        <p className="text-xs text-gray-500 mt-1 truncate">
                          {dashboard.description}
                        </p>
                      )}
                      <div className="flex items-center justify-between mt-1">
                        <span className="text-xs text-gray-400">
                          {dashboard.widget_count || 0} widgets
                        </span>
                        {dashboard.is_public ? (
                          <svg className="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        ) : (
                          <svg className="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                          </svg>
                        )}
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Footer */}
          <div className="p-4 border-t border-gray-200">
            <div className="text-xs text-gray-500">
              <p>Analytics powered by</p>
              <p className="font-medium">RAID Dashboard System</p>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {selectedDashboard ? (
            <div className="flex-1 p-6 overflow-auto">
              <DashboardView
                dashboardId={selectedDashboard.id}
                onDashboardChange={handleDashboardChange}
              />
            </div>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center max-w-md">
                <svg className="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Welcome to Analytics</h3>
                <p className="text-gray-600 mb-6">
                  Get started with analytics by creating a dashboard to visualize your RAID data
                </p>
                <div className="space-y-3">
                  <button
                    onClick={createDemoDashboard}
                    disabled={isCreatingDemo}
                    className="w-full px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
                  >
                    {isCreatingDemo ? (
                      <span className="flex items-center justify-center">
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Creating Demo Dashboard...
                      </span>
                    ) : (
                      'Create Demo Dashboard'
                    )}
                  </button>
                  <button
                    onClick={handleCreateDashboard}
                    className="w-full px-6 py-3 text-blue-600 border border-blue-600 rounded-md hover:bg-blue-50 font-medium"
                  >
                    Create Custom Dashboard
                  </button>
                  <button
                    onClick={() => setShowDemoMode(true)}
                    className="w-full px-6 py-3 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50 font-medium"
                  >
                    View Demo Mode
                  </button>
                  <div className="mt-4 text-sm text-gray-500">
                    <p>Demo dashboard includes sample charts for risks, actions, issues, and decisions</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Create Dashboard Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-medium mb-4">Create Dashboard</h3>
            <p className="text-gray-600 mb-4">Dashboard creation modal coming soon...</p>
            <div className="flex justify-end space-x-2">
              <button
                onClick={() => setShowCreateModal(false)}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AnalyticsPage;
