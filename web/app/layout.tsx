import '../styles/globals.css';
import { ReactQueryProvider } from './react-query-provider';
import { ToastProvider } from '@/components/ui/toast';
import AppHeader from '@/components/AppHeader';
import { ClerkProvider } from '@clerk/nextjs';
import { MonitoringProvider } from '@/lib/monitoring/MonitoringProvider';
import { MonitoringToggle } from '@/components/monitoring/MonitoringDashboard';

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <ClerkProvider>
      <html lang="en">
        <body>
          <MonitoringProvider>
            <div className="container py-6 space-y-6">
              <AppHeader />
              <ToastProvider>
                <ReactQueryProvider>{children}</ReactQueryProvider>
              </ToastProvider>
            </div>
            <MonitoringToggle />
          </MonitoringProvider>
        </body>
      </html>
    </ClerkProvider>
  );
}
