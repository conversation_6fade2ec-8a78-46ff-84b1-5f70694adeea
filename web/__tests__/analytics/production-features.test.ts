/**
 * Tests for production-ready analytics features
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { logger } from '../../lib/utils/logger';
import { performanceMonitor } from '../../lib/utils/performance';
import { memoryCache, cacheStrategies } from '../../lib/utils/cache';
import { 
  validateDashboardCreate, 
  validateWidgetCreate, 
  validateAnalyticsQuery,
  sanitizeInput,
  ValidationError,
  SecurityError 
} from '../../lib/utils/validation';
import { config, features, initializeConfig } from '../../lib/config/production';

// Mock console methods
const mockConsole = {
  log: vi.fn(),
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
  debug: vi.fn(),
};

beforeEach(() => {
  vi.clearAllMocks();
  Object.assign(console, mockConsole);
  memoryCache.clear();
});

afterEach(() => {
  vi.restoreAllMocks();
});

describe('Logger', () => {
  it('should log messages with correct levels', () => {
    logger.info('Test info message', { data: 'test' });
    logger.warn('Test warning', { warning: true });
    logger.error('Test error', { error: true });
    
    expect(mockConsole.info).toHaveBeenCalledWith(
      expect.stringContaining('Test info message'),
      expect.objectContaining({ data: 'test' })
    );
    expect(mockConsole.warn).toHaveBeenCalledWith(
      expect.stringContaining('Test warning'),
      expect.objectContaining({ warning: true })
    );
    expect(mockConsole.error).toHaveBeenCalledWith(
      expect.stringContaining('Test error'),
      expect.objectContaining({ error: true })
    );
  });

  it('should log user actions', () => {
    logger.userAction('dashboard_created', { name: 'Test Dashboard' });
    
    expect(mockConsole.info).toHaveBeenCalledWith(
      expect.stringContaining('USER_ACTION'),
      expect.objectContaining({
        action: 'dashboard_created',
        data: { name: 'Test Dashboard' }
      })
    );
  });

  it('should log API calls', () => {
    logger.apiCall('GET', '/api/dashboards', 150, 200);
    
    expect(mockConsole.info).toHaveBeenCalledWith(
      expect.stringContaining('API_CALL'),
      expect.objectContaining({
        method: 'GET',
        url: '/api/dashboards',
        duration: 150,
        status: 200
      })
    );
  });
});

describe('Performance Monitor', () => {
  it('should record metrics', () => {
    performanceMonitor.recordMetric('test_metric', 100, 'ms');
    
    const summary = performanceMonitor.getSummary();
    expect(summary.test_metric).toBeDefined();
    expect(summary.test_metric.count).toBe(1);
    expect(summary.test_metric.avg).toBe(100);
  });

  it('should measure sync functions', () => {
    const result = performanceMonitor.measureSync('test_sync', () => {
      return 'test result';
    });
    
    expect(result).toBe('test result');
    const summary = performanceMonitor.getSummary();
    expect(summary.test_sync).toBeDefined();
  });

  it('should measure async functions', async () => {
    const result = await performanceMonitor.measureAsync('test_async', async () => {
      await new Promise(resolve => setTimeout(resolve, 10));
      return 'async result';
    });
    
    expect(result).toBe('async result');
    const summary = performanceMonitor.getSummary();
    expect(summary.test_async).toBeDefined();
  });

  it('should warn on threshold exceeded', () => {
    performanceMonitor.recordMetric('API_CALL', 3000, 'ms'); // Exceeds 2000ms threshold
    
    expect(mockConsole.warn).toHaveBeenCalledWith(
      expect.stringContaining('Performance threshold exceeded'),
      expect.objectContaining({
        value: 3000,
        threshold: 2000
      })
    );
  });
});

describe('Memory Cache', () => {
  it('should store and retrieve data', () => {
    memoryCache.set('test_key', { data: 'test' });
    const result = memoryCache.get('test_key');
    
    expect(result).toEqual({ data: 'test' });
  });

  it('should respect TTL', async () => {
    memoryCache.set('ttl_key', 'test_data', 50); // 50ms TTL
    
    expect(memoryCache.get('ttl_key')).toBe('test_data');
    
    await new Promise(resolve => setTimeout(resolve, 60));
    expect(memoryCache.get('ttl_key')).toBeNull();
  });

  it('should track cache statistics', () => {
    memoryCache.set('stats_key', 'data');
    memoryCache.get('stats_key'); // Hit
    memoryCache.get('nonexistent'); // Miss
    
    const stats = memoryCache.getStats();
    expect(stats.hits).toBe(1);
    expect(stats.misses).toBe(1);
    expect(stats.sets).toBe(1);
  });

  it('should evict LRU when at capacity', () => {
    const smallCache = new (memoryCache.constructor as any)(2); // Max 2 items
    
    smallCache.set('key1', 'data1');
    smallCache.set('key2', 'data2');
    smallCache.set('key3', 'data3'); // Should evict key1
    
    expect(smallCache.get('key1')).toBeNull();
    expect(smallCache.get('key2')).toBe('data2');
    expect(smallCache.get('key3')).toBe('data3');
  });
});

describe('Cache Strategies', () => {
  it('should implement cache-first strategy', async () => {
    const fetcher = vi.fn().mockResolvedValue('fresh_data');
    
    // First call should fetch and cache
    const result1 = await cacheStrategies.cacheFirst('cache_first_key', fetcher);
    expect(result1).toBe('fresh_data');
    expect(fetcher).toHaveBeenCalledTimes(1);
    
    // Second call should use cache
    const result2 = await cacheStrategies.cacheFirst('cache_first_key', fetcher);
    expect(result2).toBe('fresh_data');
    expect(fetcher).toHaveBeenCalledTimes(1); // Not called again
  });

  it('should implement network-first strategy', async () => {
    const fetcher = vi.fn()
      .mockResolvedValueOnce('network_data')
      .mockRejectedValueOnce(new Error('Network error'));
    
    // First call should fetch from network
    const result1 = await cacheStrategies.networkFirst('network_first_key', fetcher);
    expect(result1).toBe('network_data');
    
    // Second call should fallback to cache when network fails
    const result2 = await cacheStrategies.networkFirst('network_first_key', fetcher);
    expect(result2).toBe('network_data'); // From cache
  });
});

describe('Validation', () => {
  it('should validate dashboard creation data', () => {
    const validData = {
      name: 'Test Dashboard',
      description: 'A test dashboard',
      layout: { columns: 12, rows: 10 },
      is_public: false,
    };
    
    expect(() => validateDashboardCreate(validData)).not.toThrow();
  });

  it('should reject invalid dashboard data', () => {
    const invalidData = {
      name: '', // Empty name
      description: 'A'.repeat(600), // Too long
    };
    
    expect(() => validateDashboardCreate(invalidData)).toThrow(ValidationError);
  });

  it('should sanitize HTML input', () => {
    const maliciousInput = '<script>alert("xss")</script>';
    const sanitized = sanitizeInput(maliciousInput);
    
    expect(sanitized).not.toContain('<script>');
    expect(sanitized).toContain('&lt;script&gt;');
  });

  it('should detect SQL injection attempts', () => {
    const sqlInjection = "'; DROP TABLE users; --";
    
    expect(() => sanitizeInput(sqlInjection)).toThrow(SecurityError);
  });

  it('should detect XSS attempts', () => {
    const xssAttempt = '<img src="x" onerror="alert(1)">';
    
    expect(() => sanitizeInput(xssAttempt)).toThrow(SecurityError);
  });
});

describe('Configuration', () => {
  it('should load configuration from environment', () => {
    expect(config).toBeDefined();
    expect(config.NODE_ENV).toBeDefined();
    expect(features).toBeDefined();
  });

  it('should initialize configuration successfully', () => {
    const result = initializeConfig();
    expect(result).toBe(true);
  });

  it('should validate required production variables', () => {
    const originalEnv = process.env.NODE_ENV;
    const originalApiUrl = process.env.NEXT_PUBLIC_API_URL;
    
    process.env.NODE_ENV = 'production';
    delete process.env.NEXT_PUBLIC_API_URL;
    
    expect(() => {
      // Re-import to trigger validation
      delete require.cache[require.resolve('../../lib/config/production')];
      require('../../lib/config/production');
    }).toThrow();
    
    // Restore
    process.env.NODE_ENV = originalEnv;
    process.env.NEXT_PUBLIC_API_URL = originalApiUrl;
  });
});

describe('Error Handling', () => {
  it('should create validation errors with context', () => {
    const error = new ValidationError('Invalid input', 'name', 'REQUIRED');
    
    expect(error.message).toBe('Invalid input');
    expect(error.field).toBe('name');
    expect(error.code).toBe('REQUIRED');
    expect(error.name).toBe('ValidationError');
  });

  it('should create security errors with type', () => {
    const error = new SecurityError('XSS detected', 'XSS');
    
    expect(error.message).toBe('XSS detected');
    expect(error.type).toBe('XSS');
    expect(error.name).toBe('SecurityError');
  });
});

describe('Integration', () => {
  it('should work together: cache + validation + logging', async () => {
    const validData = {
      name: 'Integration Test Dashboard',
      description: 'Testing integration',
    };
    
    // Validate data
    const validated = validateDashboardCreate(validData);
    expect(validated.name).toBe('Integration Test Dashboard');
    
    // Cache the validated data
    memoryCache.set('integration_test', validated);
    
    // Retrieve from cache
    const cached = memoryCache.get('integration_test');
    expect(cached).toEqual(validated);
    
    // Log the operation
    logger.userAction('integration_test', { success: true });
    
    expect(mockConsole.info).toHaveBeenCalledWith(
      expect.stringContaining('USER_ACTION'),
      expect.objectContaining({
        action: 'integration_test',
        data: { success: true }
      })
    );
  });
});
