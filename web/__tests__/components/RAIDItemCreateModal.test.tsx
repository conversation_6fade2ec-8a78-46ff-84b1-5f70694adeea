import { screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { render, mockProject } from '../utils/test-utils'
import RAIDItemCreateModal from '../../components/RAIDItemCreateModal'

// Mock the API client
jest.mock('../../lib/client', () => ({
  createRisk: jest.fn(),
  createAction: jest.fn(),
  createIssue: jest.fn(),
  createDecision: jest.fn(),
}))

const mockClient = require('../../lib/client')

describe('RAIDItemCreateModal', () => {
  const defaultProps = {
    isOpen: true,
    onClose: jest.fn(),
    onSuccess: jest.fn(),
    projectId: 'test-project-1',
    type: 'risk' as const,
  }

  beforeEach(() => {
    jest.clearAllMocks()
    mockClient.createRisk.mockResolvedValue({ id: 'new-risk-1', title: 'New Risk' })
    mockClient.createAction.mockResolvedValue({ id: 'new-action-1', title: 'New Action' })
    mockClient.createIssue.mockResolvedValue({ id: 'new-issue-1', title: 'New Issue' })
    mockClient.createDecision.mockResolvedValue({ id: 'new-decision-1', title: 'New Decision' })
  })

  it('renders modal when open', () => {
    render(<RAIDItemCreateModal {...defaultProps} />)
    
    expect(screen.getByText('Create New Risk')).toBeInTheDocument()
    expect(screen.getByRole('dialog')).toBeInTheDocument()
  })

  it('does not render when closed', () => {
    render(<RAIDItemCreateModal {...defaultProps} isOpen={false} />)
    
    expect(screen.queryByRole('dialog')).not.toBeInTheDocument()
  })

  it('displays correct title for each RAID type', () => {
    const { rerender } = render(<RAIDItemCreateModal {...defaultProps} type="risk" />)
    expect(screen.getByText('Create New Risk')).toBeInTheDocument()
    
    rerender(<RAIDItemCreateModal {...defaultProps} type="action" />)
    expect(screen.getByText('Create New Action')).toBeInTheDocument()
    
    rerender(<RAIDItemCreateModal {...defaultProps} type="issue" />)
    expect(screen.getByText('Create New Issue')).toBeInTheDocument()
    
    rerender(<RAIDItemCreateModal {...defaultProps} type="decision" />)
    expect(screen.getByText('Create New Decision')).toBeInTheDocument()
  })

  it('shows required form fields', () => {
    render(<RAIDItemCreateModal {...defaultProps} />)
    
    expect(screen.getByLabelText(/title/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/description/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/category/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/priority/i)).toBeInTheDocument()
  })

  it('shows risk-specific fields when type is risk', () => {
    render(<RAIDItemCreateModal {...defaultProps} type="risk" />)
    
    expect(screen.getByLabelText(/probability/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/impact/i)).toBeInTheDocument()
  })

  it('shows action-specific fields when type is action', () => {
    render(<RAIDItemCreateModal {...defaultProps} type="action" />)
    
    expect(screen.getByLabelText(/assignee/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/due date/i)).toBeInTheDocument()
  })

  it('shows issue-specific fields when type is issue', () => {
    render(<RAIDItemCreateModal {...defaultProps} type="issue" />)
    
    expect(screen.getByLabelText(/severity/i)).toBeInTheDocument()
  })

  it('shows decision-specific fields when type is decision', () => {
    render(<RAIDItemCreateModal {...defaultProps} type="decision" />)
    
    expect(screen.getByLabelText(/rationale/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/decided by/i)).toBeInTheDocument()
  })

  it('validates required fields', async () => {
    const user = userEvent.setup()
    render(<RAIDItemCreateModal {...defaultProps} />)
    
    // Try to submit without filling required fields
    const submitButton = screen.getByRole('button', { name: /create/i })
    await user.click(submitButton)
    
    expect(screen.getByText(/title is required/i)).toBeInTheDocument()
    expect(screen.getByText(/description is required/i)).toBeInTheDocument()
  })

  it('creates risk successfully', async () => {
    const user = userEvent.setup()
    render(<RAIDItemCreateModal {...defaultProps} type="risk" />)
    
    // Fill out the form
    await user.type(screen.getByLabelText(/title/i), 'New Test Risk')
    await user.type(screen.getByLabelText(/description/i), 'Risk description')
    await user.selectOptions(screen.getByLabelText(/category/i), 'Technical')
    await user.selectOptions(screen.getByLabelText(/priority/i), 'High')
    await user.selectOptions(screen.getByLabelText(/probability/i), 'Medium')
    await user.selectOptions(screen.getByLabelText(/impact/i), 'High')
    
    // Submit the form
    const submitButton = screen.getByRole('button', { name: /create/i })
    await user.click(submitButton)
    
    await waitFor(() => {
      expect(mockClient.createRisk).toHaveBeenCalledWith({
        projectId: 'test-project-1',
        title: 'New Test Risk',
        description: 'Risk description',
        category: 'Technical',
        priority: 'High',
        probability: 'Medium',
        impact: 'High',
      })
      expect(defaultProps.onSuccess).toHaveBeenCalled()
      expect(defaultProps.onClose).toHaveBeenCalled()
    })
  })

  it('creates action successfully', async () => {
    const user = userEvent.setup()
    render(<RAIDItemCreateModal {...defaultProps} type="action" />)
    
    // Fill out the form
    await user.type(screen.getByLabelText(/title/i), 'New Test Action')
    await user.type(screen.getByLabelText(/description/i), 'Action description')
    await user.selectOptions(screen.getByLabelText(/category/i), 'Process')
    await user.selectOptions(screen.getByLabelText(/priority/i), 'Medium')
    await user.type(screen.getByLabelText(/assignee/i), 'test-user')
    
    // Submit the form
    const submitButton = screen.getByRole('button', { name: /create/i })
    await user.click(submitButton)
    
    await waitFor(() => {
      expect(mockClient.createAction).toHaveBeenCalledWith({
        projectId: 'test-project-1',
        title: 'New Test Action',
        description: 'Action description',
        category: 'Process',
        priority: 'Medium',
        assignee: 'test-user',
      })
      expect(defaultProps.onSuccess).toHaveBeenCalled()
    })
  })

  it('handles creation error', async () => {
    const user = userEvent.setup()
    mockClient.createRisk.mockRejectedValue(new Error('Creation failed'))
    
    render(<RAIDItemCreateModal {...defaultProps} type="risk" />)
    
    // Fill out the form
    await user.type(screen.getByLabelText(/title/i), 'New Test Risk')
    await user.type(screen.getByLabelText(/description/i), 'Risk description')
    
    // Submit the form
    const submitButton = screen.getByRole('button', { name: /create/i })
    await user.click(submitButton)
    
    await waitFor(() => {
      expect(screen.getByText(/failed to create risk/i)).toBeInTheDocument()
    })
  })

  it('closes modal when cancel button is clicked', async () => {
    const user = userEvent.setup()
    render(<RAIDItemCreateModal {...defaultProps} />)
    
    const cancelButton = screen.getByRole('button', { name: /cancel/i })
    await user.click(cancelButton)
    
    expect(defaultProps.onClose).toHaveBeenCalled()
  })

  it('closes modal when clicking outside', async () => {
    const user = userEvent.setup()
    render(<RAIDItemCreateModal {...defaultProps} />)
    
    // Click on the backdrop
    const backdrop = screen.getByTestId('modal-backdrop')
    await user.click(backdrop)
    
    expect(defaultProps.onClose).toHaveBeenCalled()
  })

  it('resets form when modal is reopened', async () => {
    const { rerender } = render(<RAIDItemCreateModal {...defaultProps} isOpen={false} />)
    
    // Open modal and fill form
    rerender(<RAIDItemCreateModal {...defaultProps} isOpen={true} />)
    
    const titleInput = screen.getByLabelText(/title/i) as HTMLInputElement
    await userEvent.type(titleInput, 'Test Title')
    
    expect(titleInput.value).toBe('Test Title')
    
    // Close and reopen modal
    rerender(<RAIDItemCreateModal {...defaultProps} isOpen={false} />)
    rerender(<RAIDItemCreateModal {...defaultProps} isOpen={true} />)
    
    const newTitleInput = screen.getByLabelText(/title/i) as HTMLInputElement
    expect(newTitleInput.value).toBe('')
  })

  it('shows loading state during submission', async () => {
    const user = userEvent.setup()
    
    // Make the API call hang
    mockClient.createRisk.mockImplementation(() => new Promise(() => {}))
    
    render(<RAIDItemCreateModal {...defaultProps} type="risk" />)
    
    // Fill out the form
    await user.type(screen.getByLabelText(/title/i), 'New Test Risk')
    await user.type(screen.getByLabelText(/description/i), 'Risk description')
    
    // Submit the form
    const submitButton = screen.getByRole('button', { name: /create/i })
    await user.click(submitButton)
    
    expect(screen.getByText(/creating/i)).toBeInTheDocument()
    expect(submitButton).toBeDisabled()
  })

  it('displays field validation errors', async () => {
    const user = userEvent.setup()
    render(<RAIDItemCreateModal {...defaultProps} />)
    
    // Fill title with too short text
    const titleInput = screen.getByLabelText(/title/i)
    await user.type(titleInput, 'ab')
    await user.tab() // Trigger blur event
    
    expect(screen.getByText(/title must be at least 3 characters/i)).toBeInTheDocument()
  })
})
