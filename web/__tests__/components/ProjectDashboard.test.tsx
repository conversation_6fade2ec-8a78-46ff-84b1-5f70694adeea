import { screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { render, mockProject, mockRisk, mockAction, mockIssue, mockDecision } from '../utils/test-utils'
import ProjectDashboard from '../../components/ProjectDashboard'

// Mock the API client
jest.mock('../../lib/client', () => ({
  getProject: jest.fn(),
  getRisks: jest.fn(),
  getActions: jest.fn(),
  getIssues: jest.fn(),
  getDecisions: jest.fn(),
}))

const mockClient = require('../../lib/client')

describe('ProjectDashboard', () => {
  const defaultProps = {
    projectId: 'test-project-1',
  }

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks()
    
    // Setup default mock responses
    mockClient.getProject.mockResolvedValue(mockProject)
    mockClient.getRisks.mockResolvedValue([mockRisk])
    mockClient.getActions.mockResolvedValue([mockAction])
    mockClient.getIssues.mockResolvedValue([mockIssue])
    mockClient.getDecisions.mockResolvedValue([mockDecision])
  })

  it('renders project dashboard with project name', async () => {
    render(<ProjectDashboard {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('Test Project')).toBeInTheDocument()
    })
  })

  it('displays loading state initially', () => {
    render(<ProjectDashboard {...defaultProps} />)
    
    expect(screen.getByText(/loading/i)).toBeInTheDocument()
  })

  it('displays RAID items when loaded', async () => {
    render(<ProjectDashboard {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('Test Risk')).toBeInTheDocument()
      expect(screen.getByText('Test Action')).toBeInTheDocument()
      expect(screen.getByText('Test Issue')).toBeInTheDocument()
      expect(screen.getByText('Test Decision')).toBeInTheDocument()
    })
  })

  it('handles project loading error', async () => {
    mockClient.getProject.mockRejectedValue(new Error('Failed to load project'))
    
    render(<ProjectDashboard {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByText(/error loading project/i)).toBeInTheDocument()
    })
  })

  it('displays empty state when no RAID items', async () => {
    mockClient.getRisks.mockResolvedValue([])
    mockClient.getActions.mockResolvedValue([])
    mockClient.getIssues.mockResolvedValue([])
    mockClient.getDecisions.mockResolvedValue([])
    
    render(<ProjectDashboard {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByText(/no risks found/i)).toBeInTheDocument()
      expect(screen.getByText(/no actions found/i)).toBeInTheDocument()
      expect(screen.getByText(/no issues found/i)).toBeInTheDocument()
      expect(screen.getByText(/no decisions found/i)).toBeInTheDocument()
    })
  })

  it('allows switching between RAID tabs', async () => {
    const user = userEvent.setup()
    render(<ProjectDashboard {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('Test Project')).toBeInTheDocument()
    })
    
    // Click on Actions tab
    const actionsTab = screen.getByRole('tab', { name: /actions/i })
    await user.click(actionsTab)
    
    expect(screen.getByText('Test Action')).toBeInTheDocument()
    
    // Click on Issues tab
    const issuesTab = screen.getByRole('tab', { name: /issues/i })
    await user.click(issuesTab)
    
    expect(screen.getByText('Test Issue')).toBeInTheDocument()
    
    // Click on Decisions tab
    const decisionsTab = screen.getByRole('tab', { name: /decisions/i })
    await user.click(decisionsTab)
    
    expect(screen.getByText('Test Decision')).toBeInTheDocument()
  })

  it('displays correct RAID item counts', async () => {
    render(<ProjectDashboard {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('Risks (1)')).toBeInTheDocument()
      expect(screen.getByText('Actions (1)')).toBeInTheDocument()
      expect(screen.getByText('Issues (1)')).toBeInTheDocument()
      expect(screen.getByText('Decisions (1)')).toBeInTheDocument()
    })
  })

  it('shows create buttons for each RAID category', async () => {
    render(<ProjectDashboard {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('Test Project')).toBeInTheDocument()
    })
    
    expect(screen.getByRole('button', { name: /create risk/i })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /create action/i })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /create issue/i })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /create decision/i })).toBeInTheDocument()
  })

  it('opens create modal when create button is clicked', async () => {
    const user = userEvent.setup()
    render(<ProjectDashboard {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('Test Project')).toBeInTheDocument()
    })
    
    const createRiskButton = screen.getByRole('button', { name: /create risk/i })
    await user.click(createRiskButton)
    
    expect(screen.getByText(/create new risk/i)).toBeInTheDocument()
  })

  it('filters RAID items based on search input', async () => {
    const user = userEvent.setup()
    
    // Mock multiple items for filtering
    mockClient.getRisks.mockResolvedValue([
      { ...mockRisk, title: 'Database Risk' },
      { ...mockRisk, id: 'risk-2', title: 'Security Risk' },
    ])
    
    render(<ProjectDashboard {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('Database Risk')).toBeInTheDocument()
      expect(screen.getByText('Security Risk')).toBeInTheDocument()
    })
    
    // Search for "Database"
    const searchInput = screen.getByPlaceholderText(/search/i)
    await user.type(searchInput, 'Database')
    
    await waitFor(() => {
      expect(screen.getByText('Database Risk')).toBeInTheDocument()
      expect(screen.queryByText('Security Risk')).not.toBeInTheDocument()
    })
  })

  it('displays priority indicators correctly', async () => {
    render(<ProjectDashboard {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('Test Risk')).toBeInTheDocument()
    })
    
    // Check for priority indicators (assuming they're displayed as badges or colors)
    expect(screen.getByText('High')).toBeInTheDocument() // Risk priority
    expect(screen.getByText('Medium')).toBeInTheDocument() // Action priority
  })

  it('shows status indicators for RAID items', async () => {
    render(<ProjectDashboard {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('Test Risk')).toBeInTheDocument()
    })
    
    // Check for status indicators
    expect(screen.getAllByText('Open')).toHaveLength(4) // All items have "Open" status
  })

  it('handles refresh functionality', async () => {
    const user = userEvent.setup()
    render(<ProjectDashboard {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('Test Project')).toBeInTheDocument()
    })
    
    // Clear mock call history
    jest.clearAllMocks()
    
    // Click refresh button
    const refreshButton = screen.getByRole('button', { name: /refresh/i })
    await user.click(refreshButton)
    
    // Verify API calls were made again
    expect(mockClient.getProject).toHaveBeenCalledWith('test-project-1')
    expect(mockClient.getRisks).toHaveBeenCalled()
    expect(mockClient.getActions).toHaveBeenCalled()
    expect(mockClient.getIssues).toHaveBeenCalled()
    expect(mockClient.getDecisions).toHaveBeenCalled()
  })

  it('displays project metadata correctly', async () => {
    render(<ProjectDashboard {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('Test Project')).toBeInTheDocument()
      expect(screen.getByText('A test project for RAID management')).toBeInTheDocument()
      expect(screen.getByText('Weekly')).toBeInTheDocument() // Cadence
      expect(screen.getByText('Active')).toBeInTheDocument() // Status
    })
  })
})
