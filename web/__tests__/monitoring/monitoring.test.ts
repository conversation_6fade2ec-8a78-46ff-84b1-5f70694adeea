import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals';
import { PerformanceMonitor, MemoryMonitor, NetworkMonitor } from '../../lib/monitoring/utils';

// Mock performance API
const mockPerformanceObserver = jest.fn();
const mockObserve = jest.fn();
const mockDisconnect = jest.fn();

global.PerformanceObserver = jest.fn().mockImplementation((callback) => {
  mockPerformanceObserver.mockImplementation(callback);
  return {
    observe: mockObserve,
    disconnect: mockDisconnect,
  };
});

// Mock performance.memory
Object.defineProperty(global.performance, 'memory', {
  value: {
    usedJSHeapSize: 50 * 1024 * 1024, // 50MB
    totalJSHeapSize: 100 * 1024 * 1024, // 100MB
    jsHeapSizeLimit: 200 * 1024 * 1024, // 200MB
  },
  configurable: true,
});

// Mock navigator
Object.defineProperty(global.navigator, 'onLine', {
  value: true,
  writable: true,
});

describe('PerformanceMonitor', () => {
  let performanceMonitor: PerformanceMonitor;

  beforeEach(() => {
    performanceMonitor = PerformanceMonitor.getInstance();
    performanceMonitor.clearMetrics();
  });

  afterEach(() => {
    performanceMonitor.destroy();
  });

  it('should record metrics correctly', () => {
    performanceMonitor.recordMetric('test_metric', 100, { tag: 'test' });
    
    const metrics = performanceMonitor.getMetrics();
    expect(metrics).toHaveLength(1);
    expect(metrics[0].name).toBe('test_metric');
    expect(metrics[0].value).toBe(100);
    expect(metrics[0].tags).toEqual({ tag: 'test' });
  });

  it('should calculate average metrics correctly', () => {
    performanceMonitor.recordMetric('test_metric', 100);
    performanceMonitor.recordMetric('test_metric', 200);
    performanceMonitor.recordMetric('test_metric', 300);
    
    const average = performanceMonitor.getAverageMetric('test_metric');
    expect(average).toBe(200);
  });

  it('should return null for non-existent metrics', () => {
    const average = performanceMonitor.getAverageMetric('non_existent');
    expect(average).toBeNull();
  });

  it('should limit metrics to prevent memory leaks', () => {
    // Record more than 100 metrics
    for (let i = 0; i < 150; i++) {
      performanceMonitor.recordMetric('test_metric', i);
    }
    
    const metrics = performanceMonitor.getMetrics();
    expect(metrics).toHaveLength(100);
  });

  it('should filter metrics by name', () => {
    performanceMonitor.recordMetric('metric_a', 100);
    performanceMonitor.recordMetric('metric_b', 200);
    performanceMonitor.recordMetric('metric_a', 300);
    
    const metricsA = performanceMonitor.getMetricsByName('metric_a');
    expect(metricsA).toHaveLength(2);
    expect(metricsA.every(m => m.name === 'metric_a')).toBe(true);
  });
});

describe('MemoryMonitor', () => {
  let memoryMonitor: MemoryMonitor;

  beforeEach(() => {
    memoryMonitor = MemoryMonitor.getInstance();
  });

  afterEach(() => {
    memoryMonitor.stopMonitoring();
  });

  it('should get current memory usage', () => {
    const usage = memoryMonitor.getCurrentMemoryUsage();
    
    expect(usage).not.toBeNull();
    expect(usage!.used).toBe(50); // 50MB
    expect(usage!.total).toBe(100); // 100MB
    expect(usage!.limit).toBe(200); // 200MB
  });

  it('should start and stop monitoring', () => {
    const mockCallback = jest.fn();
    
    memoryMonitor.startMonitoring(mockCallback);
    expect(memoryMonitor['intervalId']).not.toBeNull();
    
    memoryMonitor.stopMonitoring();
    expect(memoryMonitor['intervalId']).toBeNull();
  });
});

describe('NetworkMonitor', () => {
  let networkMonitor: NetworkMonitor;

  beforeEach(() => {
    networkMonitor = NetworkMonitor.getInstance();
  });

  it('should get network status', () => {
    expect(networkMonitor.getNetworkStatus()).toBe(true);
  });

  it('should handle online/offline events', () => {
    const onlineCallback = jest.fn();
    const offlineCallback = jest.fn();
    
    networkMonitor.onOnline(onlineCallback);
    networkMonitor.onOffline(offlineCallback);
    
    // Simulate going offline
    Object.defineProperty(navigator, 'onLine', { value: false });
    networkMonitor['handleOffline']();
    
    expect(networkMonitor.getNetworkStatus()).toBe(false);
    expect(offlineCallback).toHaveBeenCalled();
    
    // Simulate going online
    Object.defineProperty(navigator, 'onLine', { value: true });
    networkMonitor['handleOnline']();
    
    expect(networkMonitor.getNetworkStatus()).toBe(true);
    expect(onlineCallback).toHaveBeenCalled();
  });

  it('should remove callbacks', () => {
    const callback = jest.fn();
    
    networkMonitor.onOnline(callback);
    expect(networkMonitor['onlineCallbacks']).toContain(callback);
    
    networkMonitor.removeOnlineCallback(callback);
    expect(networkMonitor['onlineCallbacks']).not.toContain(callback);
  });
});

describe('Monitoring Integration', () => {
  it('should handle missing performance API gracefully', () => {
    // Temporarily remove PerformanceObserver
    const originalPO = global.PerformanceObserver;
    delete (global as any).PerformanceObserver;
    
    expect(() => {
      const monitor = PerformanceMonitor.getInstance();
      monitor.recordMetric('test', 100);
    }).not.toThrow();
    
    // Restore PerformanceObserver
    global.PerformanceObserver = originalPO;
  });

  it('should handle missing memory API gracefully', () => {
    // Temporarily remove performance.memory
    const originalMemory = (global.performance as any).memory;
    delete (global.performance as any).memory;
    
    const memoryMonitor = MemoryMonitor.getInstance();
    const usage = memoryMonitor.getCurrentMemoryUsage();
    
    expect(usage).toBeNull();
    
    // Restore performance.memory
    (global.performance as any).memory = originalMemory;
  });
});

// Mock environment variables for testing
describe('Environment Configuration', () => {
  const originalEnv = process.env;

  beforeEach(() => {
    jest.resetModules();
    process.env = { ...originalEnv };
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  it('should respect feature flags', () => {
    process.env.NEXT_PUBLIC_ENABLE_ANALYTICS = 'false';
    process.env.NEXT_PUBLIC_ENABLE_ERROR_REPORTING = 'false';
    
    // Test that monitoring respects these flags
    expect(process.env.NEXT_PUBLIC_ENABLE_ANALYTICS).toBe('false');
    expect(process.env.NEXT_PUBLIC_ENABLE_ERROR_REPORTING).toBe('false');
  });

  it('should use default thresholds when not configured', () => {
    delete process.env.NEXT_PUBLIC_PERFORMANCE_MEMORY_THRESHOLD;
    
    // Should use default threshold of 80
    const defaultThreshold = parseInt(process.env.NEXT_PUBLIC_PERFORMANCE_MEMORY_THRESHOLD || '80');
    expect(defaultThreshold).toBe(80);
  });
});
