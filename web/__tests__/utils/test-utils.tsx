import React, { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'

// Mock data for testing
export const mockProject = {
  id: 'test-project-1',
  name: 'Test Project',
  description: 'A test project for RAID management',
  cadence: 'weekly',
  categories: ['risks', 'actions'],
  status: 'active',
  owner: 'test-user-id',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
}

export const mockRisk = {
  id: 'test-risk-1',
  projectId: 'test-project-1',
  title: 'Test Risk',
  description: 'A test risk',
  category: 'Technical',
  probability: 'Medium',
  impact: 'High',
  status: 'Open',
  owner: 'test-user-id',
  priority: 'High',
  score: 75,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
}

export const mockAction = {
  id: 'test-action-1',
  projectId: 'test-project-1',
  title: 'Test Action',
  description: 'A test action',
  category: 'Process',
  status: 'Open',
  priority: 'Medium',
  owner: 'test-user-id',
  assignee: 'test-user-id',
  progress: 25,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
}

export const mockIssue = {
  id: 'test-issue-1',
  projectId: 'test-project-1',
  title: 'Test Issue',
  description: 'A test issue',
  category: 'Bug',
  severity: 'Medium',
  status: 'Open',
  owner: 'test-user-id',
  priority: 'Medium',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
}

export const mockDecision = {
  id: 'test-decision-1',
  projectId: 'test-project-1',
  title: 'Test Decision',
  description: 'A test decision',
  rationale: 'Test rationale',
  status: 'Proposed',
  priority: 'Medium',
  category: 'Strategic',
  decidedBy: 'test-user-id',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
}

export const mockUser = {
  id: 'test-user-id',
  username: 'testuser',
  email: '<EMAIL>',
  full_name: 'Test User',
  role: 'contributor',
  is_active: true,
  is_verified: true,
  createdAt: '2024-01-01T00:00:00Z',
}

export const mockAuditEntry = {
  id: 'test-audit-1',
  itemType: 'project',
  itemId: 'test-project-1',
  action: 'create',
  changeSet: { name: { to: 'Test Project' } },
  changedBy: 'test-user-id',
  changedAt: '2024-01-01T00:00:00Z',
  reason: 'Project created',
}

// Create a test query client
const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: Infinity,
      },
      mutations: {
        retry: false,
      },
    },
  })

// Custom render function that includes providers
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  queryClient?: QueryClient
}

export function renderWithProviders(
  ui: ReactElement,
  options: CustomRenderOptions = {}
) {
  const { queryClient = createTestQueryClient(), ...renderOptions } = options

  function Wrapper({ children }: { children: React.ReactNode }) {
    return (
      <QueryClientProvider client={queryClient}>
        {children}
      </QueryClientProvider>
    )
  }

  return {
    ...render(ui, { wrapper: Wrapper, ...renderOptions }),
    queryClient,
  }
}

// Helper function to wait for loading states
export const waitForLoadingToFinish = () =>
  new Promise((resolve) => setTimeout(resolve, 0))

// Mock API responses
export const mockApiResponses = {
  projects: {
    list: [mockProject],
    single: mockProject,
    create: mockProject,
    update: { ...mockProject, name: 'Updated Project' },
    delete: { success: true, message: 'Project deleted successfully' },
  },
  risks: {
    list: [mockRisk],
    single: mockRisk,
    create: mockRisk,
    update: { ...mockRisk, title: 'Updated Risk' },
    delete: { success: true, message: 'Risk deleted successfully' },
  },
  actions: {
    list: [mockAction],
    single: mockAction,
    create: mockAction,
    update: { ...mockAction, title: 'Updated Action' },
    delete: { success: true, message: 'Action deleted successfully' },
  },
  issues: {
    list: [mockIssue],
    single: mockIssue,
    create: mockIssue,
    update: { ...mockIssue, title: 'Updated Issue' },
    delete: { success: true, message: 'Issue deleted successfully' },
  },
  decisions: {
    list: [mockDecision],
    single: mockDecision,
    create: mockDecision,
    update: { ...mockDecision, title: 'Updated Decision' },
    delete: { success: true, message: 'Decision deleted successfully' },
  },
  auth: {
    login: {
      access_token: 'mock-token',
      token_type: 'bearer',
      user: mockUser,
    },
    register: mockUser,
    me: mockUser,
    refresh: {
      access_token: 'new-mock-token',
      token_type: 'bearer',
    },
  },
  audit: {
    list: [mockAuditEntry],
    item: [mockAuditEntry],
    user: [mockAuditEntry],
    recent: [mockAuditEntry],
  },
}

// Helper to create mock handlers for MSW
export const createMockHandlers = () => {
  const { rest } = require('msw')
  const baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api/v1'

  return [
    // Auth endpoints
    rest.post(`${baseURL}/auth/login`, (req, res, ctx) => {
      return res(ctx.json(mockApiResponses.auth.login))
    }),
    rest.post(`${baseURL}/auth/register`, (req, res, ctx) => {
      return res(ctx.status(201), ctx.json(mockApiResponses.auth.register))
    }),
    rest.get(`${baseURL}/auth/me`, (req, res, ctx) => {
      return res(ctx.json(mockApiResponses.auth.me))
    }),

    // Projects endpoints
    rest.get(`${baseURL}/projects`, (req, res, ctx) => {
      return res(ctx.json(mockApiResponses.projects.list))
    }),
    rest.get(`${baseURL}/projects/:id`, (req, res, ctx) => {
      return res(ctx.json(mockApiResponses.projects.single))
    }),
    rest.post(`${baseURL}/projects`, (req, res, ctx) => {
      return res(ctx.json(mockApiResponses.projects.create))
    }),
    rest.put(`${baseURL}/projects/:id`, (req, res, ctx) => {
      return res(ctx.json(mockApiResponses.projects.update))
    }),
    rest.delete(`${baseURL}/projects/:id`, (req, res, ctx) => {
      return res(ctx.json(mockApiResponses.projects.delete))
    }),

    // RAID endpoints
    rest.get(`${baseURL}/raid/risks`, (req, res, ctx) => {
      return res(ctx.json(mockApiResponses.risks.list))
    }),
    rest.get(`${baseURL}/raid/actions`, (req, res, ctx) => {
      return res(ctx.json(mockApiResponses.actions.list))
    }),
    rest.get(`${baseURL}/raid/issues`, (req, res, ctx) => {
      return res(ctx.json(mockApiResponses.issues.list))
    }),
    rest.get(`${baseURL}/raid/decisions`, (req, res, ctx) => {
      return res(ctx.json(mockApiResponses.decisions.list))
    }),

    // Audit endpoints
    rest.get(`${baseURL}/audit`, (req, res, ctx) => {
      return res(ctx.json(mockApiResponses.audit.list))
    }),
    rest.get(`${baseURL}/audit/recent`, (req, res, ctx) => {
      return res(ctx.json(mockApiResponses.audit.recent))
    }),
  ]
}

// Re-export everything from testing library
export * from '@testing-library/react'
export { renderWithProviders as render }
