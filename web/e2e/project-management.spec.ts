import { test, expect } from '@playwright/test'

test.describe('Project Management', () => {
  test.beforeEach(async ({ page }) => {
    // Sign in before each test
    await page.goto('/sign-in')
    await page.getByLabel(/username/i).fill('testuser')
    await page.getByLabel(/password/i).fill('testpassword123')
    await page.getByRole('button', { name: /sign in/i }).click()
    await page.waitForURL(/dashboard|projects/)
  })

  test('should display projects list', async ({ page }) => {
    await page.goto('/projects')
    
    await expect(page).toHaveTitle(/projects/i)
    await expect(page.getByRole('heading', { name: /projects/i })).toBeVisible()
    await expect(page.getByRole('button', { name: /create project/i })).toBeVisible()
  })

  test('should create new project', async ({ page }) => {
    await page.goto('/projects')
    
    // Click create project button
    await page.getByRole('button', { name: /create project/i }).click()
    
    // Fill in project details
    const projectName = `Test Project ${Date.now()}`
    await page.getByLabel(/name/i).fill(projectName)
    await page.getByLabel(/description/i).fill('A test project for E2E testing')
    await page.getByLabel(/cadence/i).selectOption('weekly')
    
    // Submit form
    await page.getByRole('button', { name: /create/i }).click()
    
    // Should redirect to project dashboard
    await expect(page).toHaveURL(/projects\/[^\/]+/)
    await expect(page.getByText(projectName)).toBeVisible()
  })

  test('should edit existing project', async ({ page }) => {
    // First create a project
    await page.goto('/projects')
    await page.getByRole('button', { name: /create project/i }).click()
    
    const originalName = `Test Project ${Date.now()}`
    await page.getByLabel(/name/i).fill(originalName)
    await page.getByLabel(/description/i).fill('Original description')
    await page.getByRole('button', { name: /create/i }).click()
    
    // Wait for project page
    await page.waitForURL(/projects\/[^\/]+/)
    
    // Click edit button
    await page.getByRole('button', { name: /edit project/i }).click()
    
    // Update project details
    const updatedName = `Updated ${originalName}`
    await page.getByLabel(/name/i).clear()
    await page.getByLabel(/name/i).fill(updatedName)
    await page.getByLabel(/description/i).clear()
    await page.getByLabel(/description/i).fill('Updated description')
    
    // Save changes
    await page.getByRole('button', { name: /save/i }).click()
    
    // Verify changes
    await expect(page.getByText(updatedName)).toBeVisible()
    await expect(page.getByText('Updated description')).toBeVisible()
  })

  test('should delete project', async ({ page }) => {
    // First create a project
    await page.goto('/projects')
    await page.getByRole('button', { name: /create project/i }).click()
    
    const projectName = `Test Project ${Date.now()}`
    await page.getByLabel(/name/i).fill(projectName)
    await page.getByLabel(/description/i).fill('Project to be deleted')
    await page.getByRole('button', { name: /create/i }).click()
    
    // Wait for project page
    await page.waitForURL(/projects\/[^\/]+/)
    
    // Click delete button
    await page.getByRole('button', { name: /delete project/i }).click()
    
    // Confirm deletion
    await page.getByRole('button', { name: /confirm delete/i }).click()
    
    // Should redirect to projects list
    await expect(page).toHaveURL(/projects$/)
    
    // Project should not be in the list
    await expect(page.getByText(projectName)).not.toBeVisible()
  })

  test('should filter projects by status', async ({ page }) => {
    await page.goto('/projects')
    
    // Create active project
    await page.getByRole('button', { name: /create project/i }).click()
    await page.getByLabel(/name/i).fill('Active Project')
    await page.getByLabel(/status/i).selectOption('active')
    await page.getByRole('button', { name: /create/i }).click()
    await page.goto('/projects')
    
    // Create inactive project
    await page.getByRole('button', { name: /create project/i }).click()
    await page.getByLabel(/name/i).fill('Inactive Project')
    await page.getByLabel(/status/i).selectOption('inactive')
    await page.getByRole('button', { name: /create/i }).click()
    await page.goto('/projects')
    
    // Filter by active status
    await page.getByLabel(/filter by status/i).selectOption('active')
    
    await expect(page.getByText('Active Project')).toBeVisible()
    await expect(page.getByText('Inactive Project')).not.toBeVisible()
    
    // Filter by inactive status
    await page.getByLabel(/filter by status/i).selectOption('inactive')
    
    await expect(page.getByText('Active Project')).not.toBeVisible()
    await expect(page.getByText('Inactive Project')).toBeVisible()
  })

  test('should search projects', async ({ page }) => {
    await page.goto('/projects')
    
    // Create projects with different names
    const projects = ['Alpha Project', 'Beta Project', 'Gamma Project']
    
    for (const projectName of projects) {
      await page.getByRole('button', { name: /create project/i }).click()
      await page.getByLabel(/name/i).fill(projectName)
      await page.getByRole('button', { name: /create/i }).click()
      await page.goto('/projects')
    }
    
    // Search for "Alpha"
    await page.getByPlaceholder(/search projects/i).fill('Alpha')
    
    await expect(page.getByText('Alpha Project')).toBeVisible()
    await expect(page.getByText('Beta Project')).not.toBeVisible()
    await expect(page.getByText('Gamma Project')).not.toBeVisible()
    
    // Clear search
    await page.getByPlaceholder(/search projects/i).clear()
    
    // All projects should be visible again
    await expect(page.getByText('Alpha Project')).toBeVisible()
    await expect(page.getByText('Beta Project')).toBeVisible()
    await expect(page.getByText('Gamma Project')).toBeVisible()
  })

  test('should display project statistics', async ({ page }) => {
    // Create a project
    await page.goto('/projects')
    await page.getByRole('button', { name: /create project/i }).click()
    
    const projectName = `Stats Project ${Date.now()}`
    await page.getByLabel(/name/i).fill(projectName)
    await page.getByRole('button', { name: /create/i }).click()
    
    // Wait for project page
    await page.waitForURL(/projects\/[^\/]+/)
    
    // Check for statistics display
    await expect(page.getByText(/risks/i)).toBeVisible()
    await expect(page.getByText(/actions/i)).toBeVisible()
    await expect(page.getByText(/issues/i)).toBeVisible()
    await expect(page.getByText(/decisions/i)).toBeVisible()
    
    // Should show counts (initially 0)
    await expect(page.getByText('0')).toBeVisible()
  })

  test('should handle project access permissions', async ({ page }) => {
    // This test would need to be implemented based on your permission system
    // For now, we'll just check that the project page loads for the owner
    
    await page.goto('/projects')
    await page.getByRole('button', { name: /create project/i }).click()
    
    const projectName = `Permission Project ${Date.now()}`
    await page.getByLabel(/name/i).fill(projectName)
    await page.getByRole('button', { name: /create/i }).click()
    
    // Owner should have full access
    await expect(page.getByRole('button', { name: /edit project/i })).toBeVisible()
    await expect(page.getByRole('button', { name: /delete project/i })).toBeVisible()
  })

  test('should navigate between project views', async ({ page }) => {
    // Create a project
    await page.goto('/projects')
    await page.getByRole('button', { name: /create project/i }).click()
    
    const projectName = `Navigation Project ${Date.now()}`
    await page.getByLabel(/name/i).fill(projectName)
    await page.getByRole('button', { name: /create/i }).click()
    
    // Wait for project page
    await page.waitForURL(/projects\/[^\/]+/)
    
    // Navigate to different RAID tabs
    await page.getByRole('tab', { name: /risks/i }).click()
    await expect(page.getByText(/no risks found/i)).toBeVisible()
    
    await page.getByRole('tab', { name: /actions/i }).click()
    await expect(page.getByText(/no actions found/i)).toBeVisible()
    
    await page.getByRole('tab', { name: /issues/i }).click()
    await expect(page.getByText(/no issues found/i)).toBeVisible()
    
    await page.getByRole('tab', { name: /decisions/i }).click()
    await expect(page.getByText(/no decisions found/i)).toBeVisible()
  })
})
