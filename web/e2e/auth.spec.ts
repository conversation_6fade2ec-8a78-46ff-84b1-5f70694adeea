import { test, expect } from '@playwright/test'

test.describe('Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Start each test from the home page
    await page.goto('/')
  })

  test('should display sign in page', async ({ page }) => {
    await page.goto('/sign-in')
    
    await expect(page).toHaveTitle(/sign in/i)
    await expect(page.getByRole('heading', { name: /sign in/i })).toBeVisible()
    await expect(page.getByLabel(/username/i)).toBeVisible()
    await expect(page.getByLabel(/password/i)).toBeVisible()
    await expect(page.getByRole('button', { name: /sign in/i })).toBeVisible()
  })

  test('should display sign up page', async ({ page }) => {
    await page.goto('/sign-up')
    
    await expect(page).toHaveTitle(/sign up/i)
    await expect(page.getByRole('heading', { name: /sign up/i })).toBeVisible()
    await expect(page.getByLabel(/username/i)).toBeVisible()
    await expect(page.getByLabel(/email/i)).toBeVisible()
    await expect(page.getByLabel(/password/i)).toBeVisible()
    await expect(page.getByRole('button', { name: /sign up/i })).toBeVisible()
  })

  test('should show validation errors for empty sign in form', async ({ page }) => {
    await page.goto('/sign-in')
    
    // Try to submit empty form
    await page.getByRole('button', { name: /sign in/i }).click()
    
    await expect(page.getByText(/username is required/i)).toBeVisible()
    await expect(page.getByText(/password is required/i)).toBeVisible()
  })

  test('should show validation errors for empty sign up form', async ({ page }) => {
    await page.goto('/sign-up')
    
    // Try to submit empty form
    await page.getByRole('button', { name: /sign up/i }).click()
    
    await expect(page.getByText(/username is required/i)).toBeVisible()
    await expect(page.getByText(/email is required/i)).toBeVisible()
    await expect(page.getByText(/password is required/i)).toBeVisible()
  })

  test('should handle invalid login credentials', async ({ page }) => {
    await page.goto('/sign-in')
    
    // Fill in invalid credentials
    await page.getByLabel(/username/i).fill('invaliduser')
    await page.getByLabel(/password/i).fill('wrongpassword')
    await page.getByRole('button', { name: /sign in/i }).click()
    
    // Should show error message
    await expect(page.getByText(/invalid credentials/i)).toBeVisible()
  })

  test('should successfully sign up new user', async ({ page }) => {
    await page.goto('/sign-up')
    
    // Fill in valid registration data
    const timestamp = Date.now()
    await page.getByLabel(/username/i).fill(`testuser${timestamp}`)
    await page.getByLabel(/email/i).fill(`test${timestamp}@example.com`)
    await page.getByLabel(/full name/i).fill('Test User')
    await page.getByLabel(/password/i).fill('testpassword123')
    await page.getByRole('button', { name: /sign up/i }).click()
    
    // Should redirect to dashboard or show success message
    await expect(page).toHaveURL(/dashboard|projects/)
    await expect(page.getByText(/welcome/i)).toBeVisible()
  })

  test('should successfully sign in existing user', async ({ page }) => {
    // First create a user (this assumes the sign up test passed)
    await page.goto('/sign-up')
    
    const timestamp = Date.now()
    const username = `testuser${timestamp}`
    const password = 'testpassword123'
    
    await page.getByLabel(/username/i).fill(username)
    await page.getByLabel(/email/i).fill(`test${timestamp}@example.com`)
    await page.getByLabel(/full name/i).fill('Test User')
    await page.getByLabel(/password/i).fill(password)
    await page.getByRole('button', { name: /sign up/i }).click()
    
    // Wait for redirect and then sign out
    await page.waitForURL(/dashboard|projects/)
    await page.getByRole('button', { name: /sign out/i }).click()
    
    // Now test sign in
    await page.goto('/sign-in')
    await page.getByLabel(/username/i).fill(username)
    await page.getByLabel(/password/i).fill(password)
    await page.getByRole('button', { name: /sign in/i }).click()
    
    // Should redirect to dashboard
    await expect(page).toHaveURL(/dashboard|projects/)
    await expect(page.getByText(/welcome/i)).toBeVisible()
  })

  test('should redirect unauthenticated users to sign in', async ({ page }) => {
    // Try to access protected route
    await page.goto('/projects')
    
    // Should redirect to sign in
    await expect(page).toHaveURL(/sign-in/)
    await expect(page.getByRole('heading', { name: /sign in/i })).toBeVisible()
  })

  test('should allow sign out', async ({ page }) => {
    // First sign in
    await page.goto('/sign-in')
    await page.getByLabel(/username/i).fill('testuser')
    await page.getByLabel(/password/i).fill('testpassword123')
    await page.getByRole('button', { name: /sign in/i }).click()
    
    // Wait for dashboard
    await page.waitForURL(/dashboard|projects/)
    
    // Sign out
    await page.getByRole('button', { name: /sign out/i }).click()
    
    // Should redirect to home or sign in
    await expect(page).toHaveURL(/\/|sign-in/)
  })

  test('should persist authentication across page reloads', async ({ page }) => {
    // Sign in
    await page.goto('/sign-in')
    await page.getByLabel(/username/i).fill('testuser')
    await page.getByLabel(/password/i).fill('testpassword123')
    await page.getByRole('button', { name: /sign in/i }).click()
    
    // Wait for dashboard
    await page.waitForURL(/dashboard|projects/)
    
    // Reload page
    await page.reload()
    
    // Should still be authenticated
    await expect(page).toHaveURL(/dashboard|projects/)
    await expect(page.getByText(/welcome/i)).toBeVisible()
  })

  test('should handle password reset flow', async ({ page }) => {
    await page.goto('/sign-in')
    
    // Click forgot password link
    await page.getByText(/forgot password/i).click()
    
    // Should navigate to password reset page
    await expect(page).toHaveURL(/reset-password|forgot-password/)
    await expect(page.getByLabel(/email/i)).toBeVisible()
    await expect(page.getByRole('button', { name: /reset password/i })).toBeVisible()
    
    // Fill in email and submit
    await page.getByLabel(/email/i).fill('<EMAIL>')
    await page.getByRole('button', { name: /reset password/i }).click()
    
    // Should show success message
    await expect(page.getByText(/reset link sent/i)).toBeVisible()
  })
})
