import { chromium, FullConfig } from '@playwright/test'

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global setup...')
  
  // Start the backend API server if needed
  // This could be done here or in a separate script
  
  // Create a browser instance for setup tasks
  const browser = await chromium.launch()
  const page = await browser.newPage()
  
  try {
    // Wait for the frontend to be ready
    console.log('⏳ Waiting for frontend to be ready...')
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle' })
    console.log('✅ Frontend is ready')
    
    // Perform any global setup tasks here
    // For example, create test users, seed data, etc.
    
  } catch (error) {
    console.error('❌ Global setup failed:', error)
    throw error
  } finally {
    await browser.close()
  }
  
  console.log('✅ Global setup completed')
}

export default globalSetup
