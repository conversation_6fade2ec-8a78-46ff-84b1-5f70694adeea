@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --bg: 255 255 255;
}
body {
  @apply bg-white text-gray-900;
}
.container {
  @apply max-w-6xl mx-auto px-4;
}
.card {
  @apply rounded-2xl shadow-sm border border-gray-200 bg-white;
}

/* Compact density tweaks */
:root { --cell-py: 0.5rem; --cell-px: 0.5rem; --card-p: 1rem; }
.compact :root {}
.compact table.resizable-table th,
.compact table.resizable-table td { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.compact .kanban-card { padding: 0.5rem !important; }
.compact .text-sm { font-size: 0.8rem; }
.compact .text-xs { font-size: 0.7rem; }
.resizable-table th[data-col], .resizable-table td[data-col] { width: auto; }
.resizable-table th[data-col] { position: relative; }
.resizable-table th[data-col] > div { position: relative; }
.resizable-table th[data-col] .handle { position: absolute; right: 0; top: 0; height: 100%; width: 4px; cursor: col-resize; }

/* React Grid Layout Styles */
.react-grid-layout {
  position: relative;
}

.react-grid-item {
  transition: all 200ms ease;
  transition-property: left, top, width, height;
}

.react-grid-item.cssTransforms {
  transition-property: transform, width, height;
}

.react-grid-item > .react-resizable-handle {
  position: absolute;
  width: 20px;
  height: 20px;
  bottom: 0;
  right: 0;
  cursor: se-resize;
  background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNiIgaGVpZ2h0PSI2IiB2aWV3Qm94PSIwIDAgNiA2IiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxnIGZpbGw9IiM0QTVBNjgiIGZpbGwtcnVsZT0iZXZlbm9kZCI+PGNpcmNsZSBjeD0iNSIgY3k9IjUiIHI9IjEiLz48Y2lyY2xlIGN4PSI1IiBjeT0iMSIgcj0iMSIvPjxjaXJjbGUgY3g9IjEiIGN5PSI1IiByPSIxIi8+PC9nPjwvc3ZnPg==') no-repeat;
  background-position: bottom right;
  padding: 0 3px 3px 0;
  background-repeat: no-repeat;
  background-origin: content-box;
  box-sizing: border-box;
}

.react-grid-item.react-grid-placeholder {
  background: rgba(59, 130, 246, 0.15);
  border: 2px dashed rgba(59, 130, 246, 0.4);
  opacity: 0.2;
  transition-duration: 100ms;
  z-index: 2;
  user-select: none;
}

.react-grid-item.react-draggable-dragging {
  transition: none;
  z-index: 3;
  opacity: 0.8;
}

.react-grid-item.react-grid-resizing {
  opacity: 0.8;
  z-index: 3;
}
